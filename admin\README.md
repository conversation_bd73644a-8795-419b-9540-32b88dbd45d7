# Sky Mirror CMS - 内容管理系统

## 🌊 项目简介

Sky Mirror CMS 是为 Sky Mirror World Tour 旅游网站开发的轻量级内容管理系统。该系统采用纯前端技术栈，使用 localStorage 进行数据存储，为非技术用户提供简洁易用的管理界面。

## ✨ 主要功能

### 🗺️ 路线管理
- 旅游路线的增删改查
- 路线排序和分类管理
- 路线状态管理（发布/草稿/下线）
- 批量操作功能

### 📦 套餐管理
- 套餐产品的完整管理
- 价格设置和时间管理
- 包含/不包含项目管理
- 套餐类型分类

### 🖼️ 图片管理
- 图片上传和管理
- 图片分类和标签
- 图片使用情况追踪
- 多种视图模式

### ⚙️ 系统功能
- 数据导入/导出
- 缓存管理
- 实时数据同步
- 活动日志记录

## 🚀 快速开始

### 1. 访问登录页面
打开 `admin/login.html` 进行登录

**默认登录凭据：**
- 用户名: `admin` 密码: `admin123`
- 用户名: `skymirror` 密码: `cms2024`

### 2. 系统导航
登录后可以通过左侧导航栏访问不同的管理模块：
- 仪表板：查看系统概览和统计信息
- 路线管理：管理旅游路线
- 套餐管理：管理套餐产品
- 图片管理：管理图片资源
- 系统设置：系统配置和数据管理

### 3. 数据管理
- 所有数据存储在浏览器的 localStorage 中
- 支持数据导出为 JSON 文件进行备份
- 支持从 JSON 文件导入数据

## 📁 项目结构

```
admin/
├── index.html              # CMS主页面
├── login.html              # 登录页面
├── css/
│   ├── admin.css          # CMS主样式
│   └── components.css     # 组件样式
├── js/
│   ├── admin-core.js      # CMS核心功能
│   ├── data-manager.js    # 数据管理核心
│   ├── route-manager.js   # 路线管理模块
│   ├── package-manager.js # 套餐管理模块
│   ├── image-manager.js   # 图片管理模块
│   └── sync-manager.js    # 数据同步管理
├── data/
│   ├── routes.json        # 路线数据
│   ├── packages.json      # 套餐数据
│   └── images.json        # 图片数据
└── README.md              # 项目说明
```

## 🔧 技术架构

### 前端技术栈
- **HTML5**: 语义化标记
- **CSS3**: 现代样式和响应式设计
- **JavaScript ES6+**: 模块化开发
- **Font Awesome**: 图标库

### 数据存储
- **localStorage**: 本地数据存储
- **JSON**: 数据交换格式
- **版本控制**: 数据版本管理

### 架构特点
- **模块化设计**: 每个功能模块独立开发
- **事件驱动**: 基于事件的数据同步
- **响应式布局**: 适配桌面和移动设备
- **轻量级**: 无需服务器端支持

## 📊 数据结构

### 路线数据 (routes.json)
```json
{
  "version": "1.0.0",
  "lastUpdated": "2024-01-01T00:00:00Z",
  "routes": [
    {
      "id": "route-id",
      "title": "路线标题",
      "description": "路线描述",
      "image": "图片URL",
      "highlights": ["亮点1", "亮点2"],
      "duration": "7天6夜",
      "price": {
        "from": 3299,
        "currency": "RM"
      },
      "badge": {
        "type": "hot",
        "text": "热门"
      },
      "category": "international",
      "status": "active"
    }
  ]
}
```

### 套餐数据 (packages.json)
```json
{
  "version": "1.0.0",
  "lastUpdated": "2024-01-01T00:00:00Z",
  "packages": [
    {
      "id": "package-id",
      "title": "套餐标题",
      "type": "signature-tickets",
      "description": "套餐描述",
      "duration": "4-5小时",
      "location": "瓜拉雪兰莪",
      "includes": ["包含项目"],
      "excludes": ["不包含项目"],
      "pricing": {
        "adult": 150,
        "child": 100,
        "currency": "RM"
      },
      "status": "active"
    }
  ]
}
```

## 🔐 安全特性

- 简单的登录验证机制
- 操作日志记录
- 数据备份和恢复
- 输入验证和XSS防护

## 📱 响应式设计

- **桌面端**: 完整功能界面
- **平板端**: 优化的触摸操作
- **手机端**: 简化的移动界面

## 🔄 数据同步

系统支持前后台数据同步：
- 后台修改数据后自动触发同步
- 版本控制确保数据一致性
- 实时状态显示同步进度

## 📝 开发状态

### ✅ 已完成
- [x] CMS基础架构
- [x] 数据管理核心
- [x] 用户界面框架
- [x] 路线管理基础功能
- [x] 套餐管理基础功能
- [x] 图片管理基础功能
- [x] 登录验证系统

### 🚧 开发中
- [ ] 路线编辑功能
- [ ] 套餐编辑功能
- [ ] 图片上传功能
- [ ] 前后台数据集成

### 📋 计划中
- [ ] 高级搜索功能
- [ ] 批量操作功能
- [ ] 数据统计报表
- [ ] 用户权限管理

## 🤝 贡献指南

1. 遵循现有的代码风格
2. 为新功能添加适当的注释
3. 确保响应式设计兼容性
4. 测试所有功能模块

## 📄 许可证

本项目为 Sky Mirror World Tour 专用内容管理系统。

---

**Sky Mirror World Tour CMS v1.0.0**  
*让内容管理变得简单高效* 🌊
