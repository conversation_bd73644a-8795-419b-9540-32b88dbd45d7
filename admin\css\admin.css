/**
 * Sky Mirror CMS - 管理后台样式
 * 简洁现代的管理界面设计
 */

/* 重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: #f8fafc;
    color: #334155;
    line-height: 1.6;
}

/* 加载遮罩 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.3s ease;
}

.loading-overlay.hidden {
    opacity: 0;
    pointer-events: none;
}

.loading-spinner {
    text-align: center;
}

.loading-spinner i {
    font-size: 2rem;
    color: #3b82f6;
    margin-bottom: 1rem;
}

/* 主容器布局 */
.admin-container {
    display: flex;
    min-height: 100vh;
}

/* 侧边栏样式 */
.sidebar {
    width: 260px;
    background: #1e293b;
    color: white;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease;
    position: fixed;
    height: 100vh;
    z-index: 1000;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid #334155;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.25rem;
    font-weight: 600;
}

.logo i {
    color: #3b82f6;
    font-size: 1.5rem;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: background-color 0.2s;
}

.sidebar-toggle:hover {
    background-color: #334155;
}

/* 导航菜单 */
.sidebar-nav {
    flex: 1;
    padding: 1rem 0;
}

.nav-menu {
    list-style: none;
}

.nav-item {
    margin-bottom: 0.25rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    color: #cbd5e1;
    text-decoration: none;
    transition: all 0.2s;
    border-left: 3px solid transparent;
}

.nav-link:hover {
    background-color: #334155;
    color: white;
}

.nav-link.active {
    background-color: #1e40af;
    color: white;
    border-left-color: #3b82f6;
}

.nav-link i {
    width: 1.25rem;
    text-align: center;
}

/* 侧边栏底部 */
.sidebar-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #334155;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
    color: #cbd5e1;
}

.btn-logout {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    width: 100%;
    padding: 0.5rem;
    background: none;
    border: 1px solid #475569;
    color: #cbd5e1;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-logout:hover {
    background-color: #ef4444;
    border-color: #ef4444;
    color: white;
}

/* 主内容区 */
.main-content {
    flex: 1;
    margin-left: 260px;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* 顶部工具栏 */
.toolbar {
    background: white;
    border-bottom: 1px solid #e2e8f0;
    padding: 1rem 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 100;
}

.toolbar-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: background-color 0.2s;
}

.mobile-menu-btn:hover {
    background-color: #f1f5f9;
}

.page-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
}

.toolbar-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.sync-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #64748b;
}

.text-success {
    color: #10b981 !important;
}

.text-warning {
    color: #f59e0b !important;
}

.text-error {
    color: #ef4444 !important;
}

/* 内容区域 */
.content-area {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

.page-content {
    max-width: 1200px;
    margin: 0 auto;
}

.page-content.hidden {
    display: none;
}

/* 页面头部 */
.page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e2e8f0;
}

.page-header h2 {
    font-size: 1.875rem;
    font-weight: 600;
    color: #1e293b;
}

/* 仪表板样式 */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.2s, box-shadow 0.2s;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 0.75rem;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.stat-info h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.25rem;
}

.stat-info p {
    color: #64748b;
    font-size: 0.875rem;
}

/* 最近活动 */
.recent-activity {
    background: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.recent-activity h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
}

.activity-list {
    space-y: 0.75rem;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    border-radius: 0.5rem;
    transition: background-color 0.2s;
}

.activity-item:hover {
    background-color: #f8fafc;
}

.activity-item i {
    color: #3b82f6;
    width: 1rem;
}

.activity-item time {
    margin-left: auto;
    font-size: 0.75rem;
    color: #64748b;
}

/* 工具类 */
.hidden {
    display: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .mobile-menu-btn {
        display: block;
    }
    
    .toolbar {
        padding: 1rem;
    }
    
    .content-area {
        padding: 1rem;
    }
    
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .toolbar-right {
        flex-wrap: wrap;
        gap: 0.5rem;
    }
}

@media (max-width: 480px) {
    .stat-card {
        flex-direction: column;
        text-align: center;
    }
    
    .toolbar-right .btn span {
        display: none;
    }
}
