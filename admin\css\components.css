/**
 * Sky Mirror CMS - 组件样式
 * 可复用的UI组件样式定义
 */

/* 按钮组件 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: 1px solid transparent;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s;
    white-space: nowrap;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 按钮变体 */
.btn-primary {
    background-color: #3b82f6;
    color: white;
    border-color: #3b82f6;
}

.btn-primary:hover:not(:disabled) {
    background-color: #2563eb;
    border-color: #2563eb;
}

.btn-secondary {
    background-color: #64748b;
    color: white;
    border-color: #64748b;
}

.btn-secondary:hover:not(:disabled) {
    background-color: #475569;
    border-color: #475569;
}

.btn-success {
    background-color: #10b981;
    color: white;
    border-color: #10b981;
}

.btn-success:hover:not(:disabled) {
    background-color: #059669;
    border-color: #059669;
}

.btn-danger {
    background-color: #ef4444;
    color: white;
    border-color: #ef4444;
}

.btn-danger:hover:not(:disabled) {
    background-color: #dc2626;
    border-color: #dc2626;
}

.btn-warning {
    background-color: #f59e0b;
    color: white;
    border-color: #f59e0b;
}

.btn-warning:hover:not(:disabled) {
    background-color: #d97706;
    border-color: #d97706;
}

.btn-outline {
    background-color: transparent;
    color: #64748b;
    border-color: #d1d5db;
}

.btn-outline:hover:not(:disabled) {
    background-color: #f8fafc;
    border-color: #9ca3af;
}

/* 按钮尺寸 */
.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
}

/* 表单组件 */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #374151;
}

.form-label.required::after {
    content: " *";
    color: #ef4444;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-control:invalid {
    border-color: #ef4444;
}

.form-control:invalid:focus {
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-control[readonly] {
    background-color: #f9fafb;
    color: #6b7280;
}

/* 文本域 */
.form-textarea {
    min-height: 100px;
    resize: vertical;
}

/* 选择框 */
.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

/* 复选框和单选框 */
.form-check {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
}

.form-check-input {
    width: 1rem;
    height: 1rem;
    margin: 0;
}

.form-check-label {
    margin: 0;
    font-weight: normal;
}

/* 表单错误信息 */
.form-error {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.75rem;
    color: #ef4444;
}

/* 表格组件 */
.table-container {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 0.75rem 1rem;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
}

.table th {
    background-color: #f9fafb;
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
}

.table tbody tr:hover {
    background-color: #f9fafb;
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* 表格操作按钮 */
.table-actions {
    display: flex;
    gap: 0.5rem;
}

/* 卡片组件 */
.card {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.card-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    background-color: #f9fafb;
}

.card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e5e7eb;
    background-color: #f9fafb;
}

/* 模态框组件 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.modal {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.95);
    transition: transform 0.3s ease;
}

.modal-overlay.show .modal {
    transform: scale(1);
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #6b7280;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: color 0.2s;
}

.modal-close:hover {
    color: #374151;
}

.modal-body {
    padding: 1.5rem;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e5e7eb;
    display: flex;
    gap: 0.75rem;
    justify-content: flex-end;
}

/* 通知组件 */
.notification-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 3000;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.notification {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    padding: 1rem;
    min-width: 300px;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification-icon {
    width: 1.25rem;
    height: 1.25rem;
    flex-shrink: 0;
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.notification-message {
    font-size: 0.875rem;
    color: #6b7280;
}

.notification-close {
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    padding: 0.25rem;
}

.notification-close:hover {
    color: #6b7280;
}

/* 通知类型 */
.notification.success {
    border-left: 4px solid #10b981;
}

.notification.success .notification-icon {
    color: #10b981;
}

.notification.error {
    border-left: 4px solid #ef4444;
}

.notification.error .notification-icon {
    color: #ef4444;
}

.notification.warning {
    border-left: 4px solid #f59e0b;
}

.notification.warning .notification-icon {
    color: #f59e0b;
}

.notification.info {
    border-left: 4px solid #3b82f6;
}

.notification.info .notification-icon {
    color: #3b82f6;
}

/* 加载状态 */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }

.d-flex { display: flex; }
.d-block { display: block; }
.d-none { display: none; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-end { justify-content: flex-end; }

.align-center { align-items: center; }
.align-start { align-items: flex-start; }
.align-end { align-items: flex-end; }

/* 路线管理专用样式 */
.routes-toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding: 1rem;
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.toolbar-left,
.toolbar-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box i {
    position: absolute;
    left: 0.75rem;
    color: #9ca3af;
    z-index: 1;
}

.search-box input {
    padding-left: 2.5rem;
    min-width: 250px;
}

.routes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
}

.route-card {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.2s, box-shadow 0.2s;
}

.route-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.route-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.route-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s;
}

.route-card:hover .route-image img {
    transform: scale(1.05);
}

.route-badge {
    position: absolute;
    top: 0.75rem;
    left: 0.75rem;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.route-status {
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-active {
    background-color: #10b981;
    color: white;
}

.status-draft {
    background-color: #f59e0b;
    color: white;
}

.status-inactive {
    background-color: #6b7280;
    color: white;
}

.route-content {
    padding: 1.5rem;
}

.route-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.75rem;
    line-height: 1.4;
}

.route-highlights {
    color: #6b7280;
    font-size: 0.875rem;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.route-highlights span {
    display: inline-block;
}

.route-details {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.route-duration {
    color: #6b7280;
    font-size: 0.875rem;
}

.route-price {
    font-size: 0.875rem;
    color: #1f2937;
}

.route-price strong {
    font-size: 1rem;
    color: #3b82f6;
}

.route-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.75rem;
    color: #9ca3af;
    margin-bottom: 1rem;
}

.route-category {
    background-color: #f3f4f6;
    color: #374151;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

.route-actions {
    display: flex;
    gap: 0.5rem;
    padding: 0 1.5rem 1.5rem;
}

.route-actions .btn {
    flex: 1;
    justify-content: center;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.empty-icon {
    width: 4rem;
    height: 4rem;
    background: linear-gradient(135deg, #e5e7eb, #d1d5db);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: #6b7280;
    font-size: 2rem;
}

.empty-state h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.empty-state p {
    color: #6b7280;
    margin-bottom: 2rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .routes-toolbar {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .toolbar-left,
    .toolbar-right {
        justify-content: space-between;
    }

    .search-box input {
        min-width: auto;
        flex: 1;
    }

    .routes-grid {
        grid-template-columns: 1fr;
    }

    .route-actions {
        flex-wrap: wrap;
    }

    .route-actions .btn {
        flex: none;
        min-width: calc(50% - 0.25rem);
    }
}

/* 套餐管理专用样式 */
.packages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 1.5rem;
}

.package-card {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.2s, box-shadow 0.2s;
}

.package-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.package-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    position: relative;
}

.package-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
    padding-right: 6rem;
}

.package-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.package-badge.featured {
    background-color: #f59e0b;
    color: white;
}

.package-status {
    position: absolute;
    top: 2.5rem;
    right: 1rem;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.package-content {
    padding: 1.5rem;
}

.package-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;
    color: #6b7280;
}

.package-type {
    background-color: #f3f4f6;
    color: #374151;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

.package-description {
    color: #6b7280;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.package-pricing {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.price-item {
    text-align: center;
    padding: 1rem;
    background-color: #f9fafb;
    border-radius: 0.5rem;
}

.price-label {
    display: block;
    font-size: 0.75rem;
    color: #6b7280;
    margin-bottom: 0.25rem;
}

.price-value {
    display: block;
    font-size: 1.125rem;
    font-weight: 600;
    color: #3b82f6;
}

.package-actions {
    display: flex;
    gap: 0.5rem;
    padding: 0 1.5rem 1.5rem;
}

.package-actions .btn {
    flex: 1;
    justify-content: center;
}

/* 图片管理专用样式 */
.images-toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding: 1rem;
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.view-toggle {
    display: flex;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    overflow: hidden;
}

.view-toggle .btn {
    border: none;
    border-radius: 0;
    margin: 0;
}

.view-toggle .btn.active {
    background-color: #3b82f6;
    color: white;
}

.images-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
}

.image-card {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.2s, box-shadow 0.2s;
}

.image-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.image-preview {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.image-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s;
}

.image-card:hover .image-preview img {
    transform: scale(1.05);
}

.image-badge {
    position: absolute;
    top: 0.75rem;
    left: 0.75rem;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.image-badge.featured {
    background-color: #f59e0b;
    color: white;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    opacity: 0;
    transition: opacity 0.3s;
}

.image-card:hover .image-overlay {
    opacity: 1;
}

.image-info {
    padding: 1.5rem;
}

.image-title {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.75rem;
}

.image-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.75rem;
    font-size: 0.75rem;
    color: #6b7280;
}

.image-category {
    background-color: #f3f4f6;
    color: #374151;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

.image-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    margin-bottom: 0.75rem;
}

.tag {
    background-color: #e5e7eb;
    color: #374151;
    padding: 0.125rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
}

.image-usage {
    font-size: 0.75rem;
    color: #6b7280;
}

/* 设置页面样式 */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f3f4f6;
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-item label {
    font-weight: 500;
    color: #374151;
}

.setting-item span {
    color: #6b7280;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .packages-grid {
        grid-template-columns: 1fr;
    }

    .images-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }

    .images-toolbar {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .settings-grid {
        grid-template-columns: 1fr;
    }
}

/* 表单专用样式 */
.route-form,
.package-form {
    max-height: 70vh;
    overflow-y: auto;
    padding-right: 0.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.form-help {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.75rem;
    color: #6b7280;
}

.badge-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-top: 0.5rem;
}

.image-preview-container {
    margin-top: 0.75rem;
}

.image-preview {
    max-width: 200px;
    max-height: 120px;
    border-radius: 0.375rem;
    border: 1px solid #e5e7eb;
    object-fit: cover;
}

.preview-error {
    padding: 1rem;
    background-color: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 0.375rem;
    color: #dc2626;
    text-align: center;
    font-size: 0.875rem;
}

/* 确认对话框样式 */
.confirm-content {
    text-align: center;
    padding: 1rem 0;
}

.confirm-icon {
    width: 4rem;
    height: 4rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 2rem;
}

.confirm-icon.warning {
    background-color: #fef3c7;
    color: #d97706;
}

.confirm-icon.danger {
    background-color: #fef2f2;
    color: #dc2626;
}

.confirm-content p {
    color: #374151;
    line-height: 1.6;
    margin: 0;
}

/* 批量操作工具栏 */
.batch-toolbar {
    display: none;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background-color: #f3f4f6;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.batch-toolbar.show {
    display: flex;
}

.batch-info {
    flex: 1;
    font-size: 0.875rem;
    color: #374151;
}

.batch-actions {
    display: flex;
    gap: 0.5rem;
}

/* 选择框样式 */
.route-checkbox {
    position: absolute;
    top: 0.75rem;
    left: 0.75rem;
    width: 1.25rem;
    height: 1.25rem;
    z-index: 10;
}

.route-card.selected {
    border: 2px solid #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 高级搜索样式 */
.advanced-search {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    margin-bottom: 2rem;
    display: none;
}

.advanced-search.show {
    display: block;
}

.search-filters {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.search-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}

/* 路线统计样式 */
.routes-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-item {
    background: white;
    padding: 1rem;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #3b82f6;
    display: block;
}

.stat-label {
    font-size: 0.75rem;
    color: #6b7280;
    margin-top: 0.25rem;
}

/* 响应式设计增强 */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }

    .badge-options {
        grid-template-columns: 1fr 1fr;
    }

    .batch-toolbar {
        flex-direction: column;
        align-items: stretch;
    }

    .batch-actions {
        justify-content: center;
    }

    .search-filters {
        grid-template-columns: 1fr;
    }

    .routes-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .routes-stats {
        grid-template-columns: 1fr;
    }

    .route-actions {
        flex-direction: column;
    }

    .route-actions .btn {
        width: 100%;
    }
}

/* 套餐表单专用样式 */
.pricing-section,
.availability-section {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e5e7eb;
}

.pricing-section h4,
.availability-section h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.pricing-section h4::before {
    content: '💰';
    font-size: 1.25rem;
}

.availability-section h4::before {
    content: '📅';
    font-size: 1.25rem;
}

/* 套餐卡片增强样式 */
.package-header {
    position: relative;
    min-height: 4rem;
}

.package-title {
    line-height: 1.4;
    margin-bottom: 0;
}

.package-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.package-type {
    font-weight: 500;
}

.package-description {
    line-height: 1.6;
    color: #6b7280;
}

.package-pricing {
    background-color: #f9fafb;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-top: 1rem;
}

.price-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e5e7eb;
}

.price-item:last-child {
    border-bottom: none;
}

.price-label {
    font-size: 0.875rem;
    color: #6b7280;
}

.price-value {
    font-weight: 600;
    color: #3b82f6;
}

/* 表单验证样式 */
.form-control.error {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-control.success {
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.field-error {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.75rem;
    color: #ef4444;
}

/* 加载状态样式 */
.form-loading {
    position: relative;
    pointer-events: none;
}

.form-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

/* 响应式表单设计 */
@media (max-width: 768px) {
    .package-pricing {
        margin-top: 1rem;
    }

    .price-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }

    .pricing-section,
    .availability-section {
        margin-top: 1.5rem;
        padding-top: 1rem;
    }
}

/* 图片管理专用样式 */
.image-form {
    max-height: 70vh;
    overflow-y: auto;
    padding-right: 0.5rem;
}

.image-preview-section {
    margin: 1rem 0;
}

.image-preview-container {
    text-align: center;
    padding: 1rem;
    background-color: #f9fafb;
    border-radius: 0.5rem;
    border: 2px dashed #d1d5db;
}

.image-preview {
    max-width: 100%;
    max-height: 200px;
    border-radius: 0.375rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 图片查看模态框样式 */
.image-view-container {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 2rem;
}

.image-display {
    text-align: center;
    background-color: #f9fafb;
    border-radius: 0.5rem;
    padding: 1rem;
}

.full-image {
    max-width: 100%;
    max-height: 400px;
    border-radius: 0.375rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.image-details {
    space-y: 1rem;
}

.detail-group {
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #f3f4f6;
}

.detail-group:last-child {
    border-bottom: none;
}

.detail-group label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
}

.detail-group span {
    color: #6b7280;
    line-height: 1.5;
}

.category-badge {
    background-color: #dbeafe;
    color: #1e40af;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-badge.active {
    background-color: #dcfce7;
    color: #166534;
}

.status-badge.inactive {
    background-color: #fef2f2;
    color: #dc2626;
}

.tags-display {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    margin-top: 0.25rem;
}

.usage-list {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    margin-top: 0.25rem;
}

.usage-item {
    background-color: #f3f4f6;
    color: #374151;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-family: monospace;
}

/* 图片网格增强样式 */
.image-card {
    position: relative;
}

.image-overlay {
    background: rgba(0, 0, 0, 0.8);
    -webkit-backdrop-filter: blur(2px);
    backdrop-filter: blur(2px);
}

.image-overlay .btn {
    min-width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
}

/* 图片上传区域样式 */
.upload-area {
    border: 2px dashed #d1d5db;
    border-radius: 0.75rem;
    padding: 3rem 2rem;
    text-align: center;
    background-color: #fafafa;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #3b82f6;
    background-color: #f0f9ff;
}

.upload-area.dragover {
    border-color: #3b82f6;
    background-color: #dbeafe;
}

.upload-icon {
    width: 4rem;
    height: 4rem;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 2rem;
}

.upload-text {
    color: #374151;
    margin-bottom: 0.5rem;
}

.upload-hint {
    color: #6b7280;
    font-size: 0.875rem;
}

/* 响应式图片管理设计 */
@media (max-width: 768px) {
    .image-view-container {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .image-display {
        order: 1;
    }

    .image-details {
        order: 2;
    }

    .upload-area {
        padding: 2rem 1rem;
    }

    .upload-icon {
        width: 3rem;
        height: 3rem;
        font-size: 1.5rem;
    }
}
