<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sky Mirror CMS - 内容管理系统</title>
    
    <!-- CSS样式 -->
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="css/components.css">
    
    <!-- 图标字体 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 加载遮罩 -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>正在加载...</p>
        </div>
    </div>

    <!-- 主容器 -->
    <div class="admin-container">
        <!-- 侧边栏 -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-water"></i>
                    <span>Sky Mirror CMS</span>
                </div>
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>

            <nav class="sidebar-nav">
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="#dashboard" class="nav-link active" data-page="dashboard">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>仪表板</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#routes" class="nav-link" data-page="routes">
                            <i class="fas fa-map-marked-alt"></i>
                            <span>路线管理</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#packages" class="nav-link" data-page="packages">
                            <i class="fas fa-box"></i>
                            <span>套餐管理</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#images" class="nav-link" data-page="images">
                            <i class="fas fa-images"></i>
                            <span>图片管理</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#settings" class="nav-link" data-page="settings">
                            <i class="fas fa-cog"></i>
                            <span>系统设置</span>
                        </a>
                    </li>
                </ul>
            </nav>

            <div class="sidebar-footer">
                <div class="user-info">
                    <i class="fas fa-user-circle"></i>
                    <span>管理员</span>
                </div>
                <button class="btn-logout" id="logoutBtn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>退出</span>
                </button>
            </div>
        </aside>

        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部工具栏 -->
            <header class="toolbar">
                <div class="toolbar-left">
                    <button class="mobile-menu-btn" id="mobileMenuBtn">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="page-title" id="pageTitle">仪表板</h1>
                </div>
                
                <div class="toolbar-right">
                    <button class="btn btn-outline" id="previewBtn">
                        <i class="fas fa-eye"></i>
                        <span>预览网站</span>
                    </button>
                    <button class="btn btn-primary" id="publishBtn">
                        <i class="fas fa-upload"></i>
                        <span>发布更改</span>
                    </button>
                    <div class="sync-status" id="syncStatus">
                        <i class="fas fa-check-circle text-success"></i>
                        <span>已同步</span>
                    </div>
                </div>
            </header>

            <!-- 内容区域 -->
            <div class="content-area" id="contentArea">
                <!-- 仪表板页面 -->
                <div class="page-content" id="dashboardPage">
                    <div class="dashboard-grid">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-map-marked-alt"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="routesCount">0</h3>
                                <p>旅游路线</p>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-box"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="packagesCount">0</h3>
                                <p>套餐产品</p>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-images"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="imagesCount">0</h3>
                                <p>图片资源</p>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="lastUpdate">--</h3>
                                <p>最后更新</p>
                            </div>
                        </div>
                    </div>

                    <div class="recent-activity">
                        <h2>最近活动</h2>
                        <div class="activity-list" id="activityList">
                            <div class="activity-item">
                                <i class="fas fa-info-circle"></i>
                                <span>欢迎使用Sky Mirror CMS系统</span>
                                <time>刚刚</time>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 路线管理页面 -->
                <div class="page-content hidden" id="routesPage">
                    <div class="page-header">
                        <h2>路线管理</h2>
                        <button class="btn btn-primary" id="addRouteBtn">
                            <i class="fas fa-plus"></i>
                            <span>添加路线</span>
                        </button>
                    </div>
                    <div class="routes-content">
                        <!-- 路线管理内容将在这里动态加载 -->
                    </div>
                </div>

                <!-- 套餐管理页面 -->
                <div class="page-content hidden" id="packagesPage">
                    <div class="page-header">
                        <h2>套餐管理</h2>
                        <button class="btn btn-primary" id="addPackageBtn">
                            <i class="fas fa-plus"></i>
                            <span>添加套餐</span>
                        </button>
                    </div>
                    <div class="packages-content">
                        <!-- 套餐管理内容将在这里动态加载 -->
                    </div>
                </div>

                <!-- 图片管理页面 -->
                <div class="page-content hidden" id="imagesPage">
                    <div class="page-header">
                        <h2>图片管理</h2>
                        <button class="btn btn-primary" id="uploadImageBtn">
                            <i class="fas fa-upload"></i>
                            <span>上传图片</span>
                        </button>
                    </div>
                    <div class="images-content">
                        <!-- 图片管理内容将在这里动态加载 -->
                    </div>
                </div>

                <!-- 系统设置页面 -->
                <div class="page-content hidden" id="settingsPage">
                    <div class="page-header">
                        <h2>系统设置</h2>
                    </div>
                    <div class="settings-content">
                        <!-- 系统设置内容将在这里动态加载 -->
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 模态框容器 -->
    <div id="modalContainer"></div>

    <!-- 通知容器 -->
    <div id="notificationContainer" class="notification-container"></div>

    <!-- JavaScript -->
    <script src="js/data-manager.js"></script>
    <script src="js/admin-core.js"></script>
    <script src="js/route-manager.js"></script>
    <script src="js/package-manager.js"></script>
    <script src="js/image-manager.js"></script>
    <script src="js/sync-manager.js"></script>
</body>
</html>
