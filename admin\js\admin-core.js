/**
 * Sky Mirror CMS - 管理后台核心类
 * 负责整个CMS系统的初始化和协调
 */

class CMSCore {
    constructor() {
        this.dataManager = new CMSDataManager();
        this.currentPage = 'dashboard';
        this.modules = new Map();
        this.isInitialized = false;
        
        // 绑定this上下文
        this.handleNavigation = this.handleNavigation.bind(this);
        this.handleMobileMenu = this.handleMobileMenu.bind(this);
        this.handlePreview = this.handlePreview.bind(this);
        this.handlePublish = this.handlePublish.bind(this);
        
        this.init();
    }

    /**
     * 初始化CMS系统
     */
    async init() {
        try {
            // 显示加载遮罩
            this.showLoading();

            // 等待DOM加载完成
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.initializeApp());
            } else {
                await this.initializeApp();
            }
        } catch (error) {
            console.error('CMS初始化失败:', error);
            this.showNotification('系统初始化失败', 'error');
        }
    }

    /**
     * 初始化应用
     */
    async initializeApp() {
        console.log('Sky Mirror CMS 正在启动...');

        try {
            // 初始化事件监听
            this.initializeEventListeners();

            // 初始化导航
            this.initializeNavigation();

            // 加载仪表板数据
            await this.loadDashboardData();

            // 初始化数据监听
            this.initializeDataListeners();

            // 初始化同步管理器
            this.initializeSyncManager();

            // 隐藏加载遮罩
            this.hideLoading();

            this.isInitialized = true;
            console.log('Sky Mirror CMS 启动完成');

            // 显示欢迎通知
            this.showNotification('欢迎使用Sky Mirror CMS', 'success');

        } catch (error) {
            console.error('应用初始化失败:', error);
            this.showNotification('应用初始化失败', 'error');
            this.hideLoading();
        }
    }

    /**
     * 初始化事件监听
     */
    initializeEventListeners() {
        // 导航事件
        document.addEventListener('click', (e) => {
            const navLink = e.target.closest('.nav-link');
            if (navLink) {
                e.preventDefault();
                const page = navLink.dataset.page;
                if (page) {
                    this.navigateToPage(page);
                }
            }
        });

        // 移动端菜单
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        if (mobileMenuBtn) {
            mobileMenuBtn.addEventListener('click', this.handleMobileMenu);
        }

        // 侧边栏切换
        const sidebarToggle = document.getElementById('sidebarToggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', this.handleMobileMenu);
        }

        // 预览按钮
        const previewBtn = document.getElementById('previewBtn');
        if (previewBtn) {
            previewBtn.addEventListener('click', this.handlePreview);
        }

        // 发布按钮
        const publishBtn = document.getElementById('publishBtn');
        if (publishBtn) {
            publishBtn.addEventListener('click', this.handlePublish);
        }

        // 退出按钮
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => {
                if (confirm('确定要退出吗？')) {
                    this.logout();
                }
            });
        }

        // 窗口大小变化
        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });
    }

    /**
     * 初始化导航
     */
    initializeNavigation() {
        // 设置默认页面
        this.navigateToPage('dashboard');
    }

    /**
     * 导航到指定页面
     * @param {string} page - 页面名称
     */
    async navigateToPage(page) {
        if (this.currentPage === page) {
            return;
        }

        try {
            // 隐藏所有页面
            document.querySelectorAll('.page-content').forEach(el => {
                el.classList.add('hidden');
            });

            // 更新导航状态
            document.querySelectorAll('.nav-link').forEach(el => {
                el.classList.remove('active');
            });

            const activeNavLink = document.querySelector(`[data-page="${page}"]`);
            if (activeNavLink) {
                activeNavLink.classList.add('active');
            }

            // 显示目标页面
            const targetPage = document.getElementById(`${page}Page`);
            if (targetPage) {
                targetPage.classList.remove('hidden');
            }

            // 更新页面标题
            this.updatePageTitle(page);

            // 加载页面数据
            await this.loadPageData(page);

            this.currentPage = page;

            // 关闭移动端菜单
            this.closeMobileMenu();

        } catch (error) {
            console.error(`导航到${page}页面失败:`, error);
            this.showNotification('页面加载失败', 'error');
        }
    }

    /**
     * 更新页面标题
     * @param {string} page - 页面名称
     */
    updatePageTitle(page) {
        const titles = {
            dashboard: '仪表板',
            routes: '路线管理',
            packages: '套餐管理',
            images: '图片管理',
            settings: '系统设置'
        };

        const pageTitle = document.getElementById('pageTitle');
        if (pageTitle) {
            pageTitle.textContent = titles[page] || page;
        }
    }

    /**
     * 加载页面数据
     * @param {string} page - 页面名称
     */
    async loadPageData(page) {
        switch (page) {
            case 'dashboard':
                await this.loadDashboardData();
                break;
            case 'routes':
                await this.loadRoutesPage();
                break;
            case 'packages':
                await this.loadPackagesPage();
                break;
            case 'images':
                await this.loadImagesPage();
                break;
            case 'settings':
                await this.loadSettingsPage();
                break;
        }
    }

    /**
     * 加载仪表板数据
     */
    async loadDashboardData() {
        try {
            // 加载统计数据
            const [routes, packages, images] = await Promise.all([
                this.dataManager.getAllItems('routes'),
                this.dataManager.getAllItems('packages'),
                this.dataManager.getAllItems('images')
            ]);

            // 更新统计卡片
            this.updateStatCard('routesCount', routes.length);
            this.updateStatCard('packagesCount', packages.length);
            this.updateStatCard('imagesCount', images.length);

            // 更新最后更新时间
            const lastUpdate = this.getLastUpdateTime();
            this.updateStatCard('lastUpdate', this.formatDateTime(lastUpdate));

            // 加载最近活动
            this.loadRecentActivities();

        } catch (error) {
            console.error('加载仪表板数据失败:', error);
        }
    }

    /**
     * 更新统计卡片
     * @param {string} id - 元素ID
     * @param {string|number} value - 值
     */
    updateStatCard(id, value) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    }

    /**
     * 加载最近活动
     */
    loadRecentActivities() {
        const activities = this.dataManager.getActivities(5);
        const activityList = document.getElementById('activityList');
        
        if (activityList && activities.length > 0) {
            activityList.innerHTML = activities.map(activity => `
                <div class="activity-item">
                    <i class="fas fa-info-circle"></i>
                    <span>${activity.message}</span>
                    <time>${this.formatRelativeTime(activity.timestamp)}</time>
                </div>
            `).join('');
        }
    }

    /**
     * 获取最后更新时间
     * @returns {string} 最后更新时间
     */
    getLastUpdateTime() {
        const activities = this.dataManager.getActivities(1);
        return activities.length > 0 ? activities[0].timestamp : new Date().toISOString();
    }

    /**
     * 格式化日期时间
     * @param {string} dateString - 日期字符串
     * @returns {string} 格式化后的日期时间
     */
    formatDateTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    /**
     * 格式化相对时间
     * @param {string} dateString - 日期字符串
     * @returns {string} 相对时间
     */
    formatRelativeTime(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diff = now - date;

        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);

        if (minutes < 1) return '刚刚';
        if (minutes < 60) return `${minutes}分钟前`;
        if (hours < 24) return `${hours}小时前`;
        if (days < 7) return `${days}天前`;

        return this.formatDateTime(dateString);
    }

    /**
     * 初始化数据监听
     */
    initializeDataListeners() {
        // 监听数据变更事件
        this.dataManager.on('dataChanged', (data) => {
            this.handleDataChanged(data);
        });

        // 监听活动日志事件
        this.dataManager.on('activityLogged', (activity) => {
            this.handleActivityLogged(activity);
        });
    }

    /**
     * 处理数据变更
     * @param {Object} data - 变更数据
     */
    handleDataChanged(data) {
        // 更新同步状态
        this.updateSyncStatus('syncing');

        // 模拟同步延迟
        setTimeout(() => {
            this.updateSyncStatus('synced');

            // 如果当前在仪表板页面，刷新统计数据
            if (this.currentPage === 'dashboard') {
                this.loadDashboardData();
            }
        }, 1000);
    }

    /**
     * 处理活动日志
     * @param {Object} activity - 活动数据
     */
    handleActivityLogged(activity) {
        // 如果当前在仪表板页面，更新活动列表
        if (this.currentPage === 'dashboard') {
            this.loadRecentActivities();
        }
    }

    /**
     * 更新同步状态
     * @param {string} status - 状态：synced, syncing, error
     */
    updateSyncStatus(status) {
        const syncStatus = document.getElementById('syncStatus');
        if (!syncStatus) return;

        const statusConfig = {
            synced: {
                icon: 'fas fa-check-circle text-success',
                text: '已同步'
            },
            syncing: {
                icon: 'fas fa-sync-alt fa-spin text-warning',
                text: '同步中...'
            },
            error: {
                icon: 'fas fa-exclamation-triangle text-error',
                text: '同步失败'
            }
        };

        const config = statusConfig[status] || statusConfig.synced;
        syncStatus.innerHTML = `
            <i class="${config.icon}"></i>
            <span>${config.text}</span>
        `;
    }

    /**
     * 处理移动端菜单
     */
    handleMobileMenu() {
        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
            sidebar.classList.toggle('open');
        }
    }

    /**
     * 关闭移动端菜单
     */
    closeMobileMenu() {
        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
            sidebar.classList.remove('open');
        }
    }

    /**
     * 处理预览
     */
    async handlePreview() {
        try {
            // 先发布最新数据到前台
            await this.handlePublish();

            // 等待一下确保数据同步完成
            setTimeout(() => {
                // 在新窗口打开前台网站
                const previewUrl = window.location.origin + window.location.pathname.replace('/admin/index.html', '/index.html');
                window.open(previewUrl, '_blank', 'width=1200,height=800');
            }, 1000);

        } catch (error) {
            console.error('预览失败:', error);
            this.showNotification('预览失败，请先发布数据', 'error');
        }
    }

    /**
     * 处理发布
     */
    async handlePublish() {
        try {
            this.updateSyncStatus('syncing');

            // 使用同步管理器发布数据
            const syncManager = this.modules.get('syncManager');
            if (syncManager) {
                await syncManager.publishToFrontend();
            } else {
                // 备用发布方法
                await this.generateDataFiles();
            }

            this.updateSyncStatus('synced');
            this.showNotification('发布成功，前台数据已更新', 'success');

        } catch (error) {
            console.error('发布失败:', error);
            this.updateSyncStatus('error');
            this.showNotification('发布失败: ' + error.message, 'error');
        }
    }

    /**
     * 生成数据文件
     */
    async generateDataFiles() {
        try {
            // 获取所有数据
            const [routes, packages, images] = await Promise.all([
                this.dataManager.loadData('routes'),
                this.dataManager.loadData('packages'),
                this.dataManager.loadData('images')
            ]);

            // 这里可以将数据写入到前台可访问的JSON文件
            // 由于浏览器安全限制，实际实现可能需要服务器端支持
            console.log('生成数据文件:', { routes, packages, images });

        } catch (error) {
            console.error('生成数据文件失败:', error);
            throw error;
        }
    }

    /**
     * 处理窗口大小变化
     */
    handleResize() {
        // 在小屏幕上自动关闭侧边栏
        if (window.innerWidth <= 768) {
            this.closeMobileMenu();
        }
    }

    /**
     * 处理键盘快捷键
     * @param {KeyboardEvent} e - 键盘事件
     */
    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + S: 保存
        if ((e.ctrlKey || e.metaKey) && e.key === 's') {
            e.preventDefault();
            this.handlePublish();
        }

        // Ctrl/Cmd + P: 预览
        if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
            e.preventDefault();
            this.handlePreview();
        }

        // ESC: 关闭模态框
        if (e.key === 'Escape') {
            this.closeAllModals();
        }
    }

    /**
     * 显示加载遮罩
     */
    showLoading() {
        const loadingOverlay = document.getElementById('loadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.classList.remove('hidden');
        }
    }

    /**
     * 隐藏加载遮罩
     */
    hideLoading() {
        const loadingOverlay = document.getElementById('loadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.classList.add('hidden');
        }
    }

    /**
     * 显示通知
     * @param {string} message - 消息内容
     * @param {string} type - 通知类型：success, error, warning, info
     * @param {number} duration - 显示时长（毫秒）
     */
    showNotification(message, type = 'info', duration = 3000) {
        const container = document.getElementById('notificationContainer');
        if (!container) return;

        const notification = document.createElement('div');
        notification.className = `notification ${type}`;

        const iconMap = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        notification.innerHTML = `
            <i class="notification-icon ${iconMap[type]}"></i>
            <div class="notification-content">
                <div class="notification-message">${message}</div>
            </div>
            <button class="notification-close">
                <i class="fas fa-times"></i>
            </button>
        `;

        // 添加关闭事件
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            this.removeNotification(notification);
        });

        // 添加到容器
        container.appendChild(notification);

        // 显示动画
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        // 自动移除
        if (duration > 0) {
            setTimeout(() => {
                this.removeNotification(notification);
            }, duration);
        }
    }

    /**
     * 移除通知
     * @param {HTMLElement} notification - 通知元素
     */
    removeNotification(notification) {
        notification.classList.remove('show');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }

    /**
     * 关闭所有模态框
     */
    closeAllModals() {
        document.querySelectorAll('.modal-overlay').forEach(modal => {
            modal.classList.remove('show');
        });
    }

    /**
     * 退出登录
     */
    logout() {
        // 清除登录状态
        localStorage.removeItem('cms_logged_in');

        // 跳转到登录页面
        window.location.href = 'login.html';
    }

    /**
     * 加载路线页面
     */
    async loadRoutesPage() {
        console.log('加载路线页面');

        // 初始化路线管理器（如果还没有）
        if (!this.modules.has('routeManager')) {
            const routeManager = new RouteManager(this.dataManager);
            this.modules.set('routeManager', routeManager);
            // 设置全局引用
            window.routeManager = routeManager;
        }

        // 加载路线页面
        const routeManager = this.modules.get('routeManager');
        await routeManager.loadRoutesPage();
    }

    /**
     * 加载套餐页面
     */
    async loadPackagesPage() {
        console.log('加载套餐页面');

        // 初始化套餐管理器（如果还没有）
        if (!this.modules.has('packageManager')) {
            const packageManager = new PackageManager(this.dataManager);
            this.modules.set('packageManager', packageManager);
            // 设置全局引用
            window.packageManager = packageManager;
        }

        // 加载套餐页面
        const packageManager = this.modules.get('packageManager');
        await packageManager.loadPackagesPage();
    }

    /**
     * 加载图片页面
     */
    async loadImagesPage() {
        console.log('加载图片页面');

        // 初始化图片管理器（如果还没有）
        if (!this.modules.has('imageManager')) {
            const imageManager = new ImageManager(this.dataManager);
            this.modules.set('imageManager', imageManager);
            // 设置全局引用
            window.imageManager = imageManager;
        }

        // 加载图片页面
        const imageManager = this.modules.get('imageManager');
        await imageManager.loadImagesPage();
    }

    /**
     * 加载设置页面
     */
    async loadSettingsPage() {
        console.log('加载设置页面');

        const settingsContent = document.querySelector('#settingsPage .settings-content');
        if (!settingsContent) return;

        settingsContent.innerHTML = `
            <div class="settings-grid">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">系统信息</h3>
                    </div>
                    <div class="card-body">
                        <div class="setting-item">
                            <label>系统版本</label>
                            <span>1.0.0</span>
                        </div>
                        <div class="setting-item">
                            <label>数据版本</label>
                            <span>${this.dataManager.version}</span>
                        </div>
                        <div class="setting-item">
                            <label>最后同步时间</label>
                            <span>${this.formatDateTime(new Date().toISOString())}</span>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">数据管理</h3>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary mb-2" onclick="cmsCore.exportAllData()">
                            <i class="fas fa-download"></i>
                            导出所有数据
                        </button>
                        <button class="btn btn-outline mb-2" onclick="cmsCore.importData()">
                            <i class="fas fa-upload"></i>
                            导入数据
                        </button>
                        <button class="btn btn-warning mb-2" onclick="cmsCore.clearCache()">
                            <i class="fas fa-trash"></i>
                            清除缓存
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 初始化同步管理器
     */
    initializeSyncManager() {
        if (!this.modules.has('syncManager')) {
            this.modules.set('syncManager', new SyncManager(this.dataManager));
        }
    }

    /**
     * 导出所有数据
     */
    async exportAllData() {
        try {
            const data = {
                routes: await this.dataManager.exportData('routes'),
                packages: await this.dataManager.exportData('packages'),
                images: await this.dataManager.exportData('images'),
                config: await this.dataManager.exportData('config')
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `sky-mirror-cms-backup-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            this.showNotification('数据导出成功', 'success');
        } catch (error) {
            console.error('导出数据失败:', error);
            this.showNotification('数据导出失败', 'error');
        }
    }

    /**
     * 导入数据
     */
    importData() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = async (e) => {
            const file = e.target.files[0];
            if (!file) return;

            try {
                const text = await file.text();
                const data = JSON.parse(text);

                // 验证数据格式
                if (!data.routes || !data.packages || !data.images) {
                    throw new Error('数据格式不正确');
                }

                // 确认导入
                if (!confirm('确定要导入数据吗？这将覆盖现有数据。')) {
                    return;
                }

                // 导入数据
                await Promise.all([
                    this.dataManager.importData('routes', data.routes),
                    this.dataManager.importData('packages', data.packages),
                    this.dataManager.importData('images', data.images)
                ]);

                this.showNotification('数据导入成功', 'success');

                // 刷新当前页面
                await this.loadPageData(this.currentPage);

            } catch (error) {
                console.error('导入数据失败:', error);
                this.showNotification('数据导入失败: ' + error.message, 'error');
            }
        };
        input.click();
    }

    /**
     * 清除缓存
     */
    clearCache() {
        if (confirm('确定要清除所有缓存吗？')) {
            this.dataManager.clearCache();
            this.showNotification('缓存已清除', 'success');
        }
    }
}

// 全局CMS实例
let cmsCore;

// 初始化CMS
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        cmsCore = new CMSCore();
    });
} else {
    cmsCore = new CMSCore();
}

// 导出到全局作用域
window.CMSCore = CMSCore;
window.cmsCore = cmsCore;
