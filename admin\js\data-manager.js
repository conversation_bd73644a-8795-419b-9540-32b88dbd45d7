/**
 * Sky Mirror CMS - 数据管理核心类
 * 负责数据的加载、保存、验证和同步
 */

class CMSDataManager {
    constructor() {
        this.storage = new LocalStorageManager();
        this.cache = new Map();
        this.version = '1.0.0';
        this.dataTypes = ['routes', 'packages', 'images', 'config'];
        this.listeners = new Map();
        
        // 初始化数据结构
        this.initializeDataStructures();
    }

    /**
     * 初始化数据结构
     */
    initializeDataStructures() {
        // 路线数据结构
        this.schemas = {
            routes: {
                version: '1.0.0',
                lastUpdated: new Date().toISOString(),
                routes: []
            },
            packages: {
                version: '1.0.0',
                lastUpdated: new Date().toISOString(),
                packages: []
            },
            images: {
                version: '1.0.0',
                lastUpdated: new Date().toISOString(),
                images: [],
                categories: ['routes', 'packages', 'hero', 'gallery']
            },
            config: {
                version: '1.0.0',
                lastUpdated: new Date().toISOString(),
                settings: {
                    siteName: 'Sky Mirror World Tour',
                    adminEmail: '<EMAIL>',
                    autoSync: true,
                    backupEnabled: true
                }
            }
        };
    }

    /**
     * 加载数据
     * @param {string} type - 数据类型
     * @returns {Promise<Object>} 数据对象
     */
    async loadData(type) {
        try {
            // 检查缓存
            if (this.cache.has(type)) {
                return this.cache.get(type);
            }

            // 从localStorage加载
            let data = this.storage.get(`cms_${type}`);
            
            // 如果没有数据，使用默认结构
            if (!data) {
                data = this.schemas[type];
                await this.saveData(type, data);
            }

            // 验证数据结构
            data = this.validateAndMigrateData(type, data);

            // 缓存数据
            this.cache.set(type, data);

            return data;
        } catch (error) {
            console.error(`加载${type}数据失败:`, error);
            return this.schemas[type];
        }
    }

    /**
     * 保存数据
     * @param {string} type - 数据类型
     * @param {Object} data - 要保存的数据
     * @returns {Promise<boolean>} 保存是否成功
     */
    async saveData(type, data) {
        try {
            // 更新时间戳
            data.lastUpdated = new Date().toISOString();
            
            // 验证数据
            if (!this.validateData(type, data)) {
                throw new Error(`${type}数据验证失败`);
            }

            // 保存到localStorage
            this.storage.set(`cms_${type}`, data);

            // 更新缓存
            this.cache.set(type, data);

            // 触发数据变更事件
            this.emit('dataChanged', { type, data });

            // 记录活动日志
            this.logActivity(`更新了${this.getTypeDisplayName(type)}数据`);

            return true;
        } catch (error) {
            console.error(`保存${type}数据失败:`, error);
            return false;
        }
    }

    /**
     * 验证数据结构
     * @param {string} type - 数据类型
     * @param {Object} data - 要验证的数据
     * @returns {boolean} 验证是否通过
     */
    validateData(type, data) {
        if (!data || typeof data !== 'object') {
            return false;
        }

        const schema = this.schemas[type];
        if (!schema) {
            return false;
        }

        // 检查必需字段
        const requiredFields = Object.keys(schema);
        for (const field of requiredFields) {
            if (!(field in data)) {
                console.warn(`缺少必需字段: ${field}`);
                return false;
            }
        }

        return true;
    }

    /**
     * 验证并迁移数据（处理版本兼容性）
     * @param {string} type - 数据类型
     * @param {Object} data - 原始数据
     * @returns {Object} 迁移后的数据
     */
    validateAndMigrateData(type, data) {
        // 如果没有版本信息，添加默认版本
        if (!data.version) {
            data.version = this.version;
        }

        // 如果没有更新时间，添加当前时间
        if (!data.lastUpdated) {
            data.lastUpdated = new Date().toISOString();
        }

        // 根据类型进行特定的数据迁移
        switch (type) {
            case 'routes':
                if (!Array.isArray(data.routes)) {
                    data.routes = [];
                }
                break;
            case 'packages':
                if (!Array.isArray(data.packages)) {
                    data.packages = [];
                }
                break;
            case 'images':
                if (!Array.isArray(data.images)) {
                    data.images = [];
                }
                if (!Array.isArray(data.categories)) {
                    data.categories = ['routes', 'packages', 'hero', 'gallery'];
                }
                break;
        }

        return data;
    }

    /**
     * 添加项目
     * @param {string} type - 数据类型
     * @param {Object} item - 要添加的项目
     * @returns {Promise<string>} 新项目的ID
     */
    async addItem(type, item) {
        try {
            const data = await this.loadData(type);
            const arrayKey = this.getArrayKey(type);
            
            // 生成唯一ID
            item.id = this.generateId();
            item.createdAt = new Date().toISOString();
            item.updatedAt = new Date().toISOString();

            // 添加到数组
            data[arrayKey].push(item);

            // 保存数据
            await this.saveData(type, data);

            return item.id;
        } catch (error) {
            console.error(`添加${type}项目失败:`, error);
            throw error;
        }
    }

    /**
     * 更新项目
     * @param {string} type - 数据类型
     * @param {string} id - 项目ID
     * @param {Object} updates - 更新的字段
     * @returns {Promise<boolean>} 更新是否成功
     */
    async updateItem(type, id, updates) {
        try {
            const data = await this.loadData(type);
            const arrayKey = this.getArrayKey(type);
            
            const index = data[arrayKey].findIndex(item => item.id === id);
            if (index === -1) {
                throw new Error(`未找到ID为${id}的项目`);
            }

            // 更新项目
            data[arrayKey][index] = {
                ...data[arrayKey][index],
                ...updates,
                updatedAt: new Date().toISOString()
            };

            // 保存数据
            await this.saveData(type, data);

            return true;
        } catch (error) {
            console.error(`更新${type}项目失败:`, error);
            throw error;
        }
    }

    /**
     * 删除项目
     * @param {string} type - 数据类型
     * @param {string} id - 项目ID
     * @returns {Promise<boolean>} 删除是否成功
     */
    async deleteItem(type, id) {
        try {
            const data = await this.loadData(type);
            const arrayKey = this.getArrayKey(type);
            
            const index = data[arrayKey].findIndex(item => item.id === id);
            if (index === -1) {
                throw new Error(`未找到ID为${id}的项目`);
            }

            // 删除项目
            data[arrayKey].splice(index, 1);

            // 保存数据
            await this.saveData(type, data);

            return true;
        } catch (error) {
            console.error(`删除${type}项目失败:`, error);
            throw error;
        }
    }

    /**
     * 获取项目
     * @param {string} type - 数据类型
     * @param {string} id - 项目ID
     * @returns {Promise<Object|null>} 项目对象
     */
    async getItem(type, id) {
        try {
            const data = await this.loadData(type);
            const arrayKey = this.getArrayKey(type);
            
            return data[arrayKey].find(item => item.id === id) || null;
        } catch (error) {
            console.error(`获取${type}项目失败:`, error);
            return null;
        }
    }

    /**
     * 获取所有项目
     * @param {string} type - 数据类型
     * @returns {Promise<Array>} 项目数组
     */
    async getAllItems(type) {
        try {
            const data = await this.loadData(type);
            const arrayKey = this.getArrayKey(type);
            
            return data[arrayKey] || [];
        } catch (error) {
            console.error(`获取所有${type}项目失败:`, error);
            return [];
        }
    }

    /**
     * 清除缓存
     * @param {string} type - 数据类型（可选）
     */
    clearCache(type = null) {
        if (type) {
            this.cache.delete(type);
        } else {
            this.cache.clear();
        }
    }

    /**
     * 导出数据
     * @param {string} type - 数据类型
     * @returns {Promise<string>} JSON字符串
     */
    async exportData(type) {
        try {
            const data = await this.loadData(type);
            return JSON.stringify(data, null, 2);
        } catch (error) {
            console.error(`导出${type}数据失败:`, error);
            throw error;
        }
    }

    /**
     * 导入数据
     * @param {string} type - 数据类型
     * @param {string} jsonData - JSON字符串
     * @returns {Promise<boolean>} 导入是否成功
     */
    async importData(type, jsonData) {
        try {
            const data = JSON.parse(jsonData);
            
            if (!this.validateData(type, data)) {
                throw new Error('数据格式验证失败');
            }

            await this.saveData(type, data);
            return true;
        } catch (error) {
            console.error(`导入${type}数据失败:`, error);
            throw error;
        }
    }

    /**
     * 记录活动日志
     * @param {string} message - 活动消息
     */
    logActivity(message) {
        const activity = {
            id: this.generateId(),
            message,
            timestamp: new Date().toISOString()
        };

        // 获取现有活动日志
        let activities = this.storage.get('cms_activities') || [];
        
        // 添加新活动
        activities.unshift(activity);
        
        // 只保留最近50条记录
        if (activities.length > 50) {
            activities = activities.slice(0, 50);
        }

        // 保存活动日志
        this.storage.set('cms_activities', activities);

        // 触发活动事件
        this.emit('activityLogged', activity);
    }

    /**
     * 获取活动日志
     * @param {number} limit - 限制数量
     * @returns {Array} 活动日志数组
     */
    getActivities(limit = 10) {
        const activities = this.storage.get('cms_activities') || [];
        return activities.slice(0, limit);
    }

    /**
     * 生成唯一ID
     * @returns {string} 唯一ID
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    /**
     * 获取数组键名
     * @param {string} type - 数据类型
     * @returns {string} 数组键名
     */
    getArrayKey(type) {
        const keyMap = {
            routes: 'routes',
            packages: 'packages',
            images: 'images'
        };
        return keyMap[type] || type;
    }

    /**
     * 获取类型显示名称
     * @param {string} type - 数据类型
     * @returns {string} 显示名称
     */
    getTypeDisplayName(type) {
        const nameMap = {
            routes: '路线',
            packages: '套餐',
            images: '图片',
            config: '配置'
        };
        return nameMap[type] || type;
    }

    /**
     * 事件监听
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    on(event, callback) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event).push(callback);
    }

    /**
     * 触发事件
     * @param {string} event - 事件名称
     * @param {*} data - 事件数据
     */
    emit(event, data) {
        if (this.listeners.has(event)) {
            this.listeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`事件回调执行失败:`, error);
                }
            });
        }
    }
}

/**
 * 本地存储管理器
 */
class LocalStorageManager {
    /**
     * 获取数据
     * @param {string} key - 键名
     * @returns {*} 数据
     */
    get(key) {
        try {
            const data = localStorage.getItem(key);
            return data ? JSON.parse(data) : null;
        } catch (error) {
            console.error(`读取localStorage失败:`, error);
            return null;
        }
    }

    /**
     * 设置数据
     * @param {string} key - 键名
     * @param {*} value - 数据
     * @returns {boolean} 设置是否成功
     */
    set(key, value) {
        try {
            localStorage.setItem(key, JSON.stringify(value));
            return true;
        } catch (error) {
            console.error(`写入localStorage失败:`, error);
            return false;
        }
    }

    /**
     * 删除数据
     * @param {string} key - 键名
     * @returns {boolean} 删除是否成功
     */
    remove(key) {
        try {
            localStorage.removeItem(key);
            return true;
        } catch (error) {
            console.error(`删除localStorage失败:`, error);
            return false;
        }
    }

    /**
     * 清空所有数据
     * @returns {boolean} 清空是否成功
     */
    clear() {
        try {
            localStorage.clear();
            return true;
        } catch (error) {
            console.error(`清空localStorage失败:`, error);
            return false;
        }
    }
}

// 导出到全局作用域
window.CMSDataManager = CMSDataManager;
window.LocalStorageManager = LocalStorageManager;
