/**
 * Sky Mirror CMS - 错误处理器
 * 统一的错误处理和日志记录
 */

class CMSErrorHandler {
    constructor() {
        this.errorLog = [];
        this.maxLogSize = 100;
        this.isDebugMode = this.getDebugMode();
        
        this.init();
    }

    /**
     * 初始化错误处理器
     */
    init() {
        // 全局错误捕获
        window.addEventListener('error', (event) => {
            this.handleGlobalError(event.error, event.filename, event.lineno, event.colno);
        });

        // Promise 错误捕获
        window.addEventListener('unhandledrejection', (event) => {
            this.handlePromiseRejection(event.reason);
        });

        // 加载已有的错误日志
        this.loadErrorLog();
    }

    /**
     * 处理全局错误
     */
    handleGlobalError(error, filename, lineno, colno) {
        const errorInfo = {
            type: 'JavaScript Error',
            message: error.message || error,
            filename: filename || 'unknown',
            line: lineno || 0,
            column: colno || 0,
            stack: error.stack || '',
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href
        };

        this.logError(errorInfo);
        
        if (this.isDebugMode) {
            console.error('Global Error:', errorInfo);
        }
    }

    /**
     * 处理Promise拒绝
     */
    handlePromiseRejection(reason) {
        const errorInfo = {
            type: 'Promise Rejection',
            message: reason.message || reason,
            stack: reason.stack || '',
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href
        };

        this.logError(errorInfo);
        
        if (this.isDebugMode) {
            console.error('Promise Rejection:', errorInfo);
        }
    }

    /**
     * 记录错误
     */
    logError(errorInfo) {
        // 添加到错误日志
        this.errorLog.unshift(errorInfo);
        
        // 限制日志大小
        if (this.errorLog.length > this.maxLogSize) {
            this.errorLog = this.errorLog.slice(0, this.maxLogSize);
        }

        // 保存到localStorage
        this.saveErrorLog();

        // 显示用户友好的错误消息
        this.showUserError(errorInfo);
    }

    /**
     * 显示用户友好的错误消息
     */
    showUserError(errorInfo) {
        // 根据错误类型显示不同的消息
        let userMessage = this.getUserFriendlyMessage(errorInfo);
        
        // 显示通知
        if (window.cmsCore) {
            window.cmsCore.showNotification(userMessage, 'error', 5000);
        }
    }

    /**
     * 获取用户友好的错误消息
     */
    getUserFriendlyMessage(errorInfo) {
        const message = errorInfo.message.toLowerCase();
        
        // 网络错误
        if (message.includes('network') || message.includes('fetch')) {
            return '网络连接出现问题，请检查网络连接后重试';
        }
        
        // 数据错误
        if (message.includes('json') || message.includes('parse')) {
            return '数据格式错误，请联系管理员';
        }
        
        // 权限错误
        if (message.includes('permission') || message.includes('unauthorized')) {
            return '权限不足，请重新登录';
        }
        
        // 存储错误
        if (message.includes('storage') || message.includes('quota')) {
            return '存储空间不足，请清理浏览器数据';
        }
        
        // 默认错误消息
        return '系统出现异常，请刷新页面重试';
    }

    /**
     * 手动记录错误
     */
    recordError(error, context = {}) {
        const errorInfo = {
            type: 'Manual Error',
            message: error.message || error,
            stack: error.stack || '',
            context: context,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href
        };

        this.logError(errorInfo);
    }

    /**
     * 记录操作日志
     */
    recordOperation(operation, details = {}) {
        const operationInfo = {
            type: 'Operation',
            operation: operation,
            details: details,
            timestamp: new Date().toISOString(),
            url: window.location.href
        };

        // 添加到操作日志
        let operationLog = JSON.parse(localStorage.getItem('cms_operation_log') || '[]');
        operationLog.unshift(operationInfo);
        
        // 限制日志大小
        if (operationLog.length > 50) {
            operationLog = operationLog.slice(0, 50);
        }
        
        localStorage.setItem('cms_operation_log', JSON.stringify(operationLog));
    }

    /**
     * 获取错误统计
     */
    getErrorStats() {
        const stats = {
            total: this.errorLog.length,
            byType: {},
            recent: this.errorLog.slice(0, 10),
            lastHour: 0,
            lastDay: 0
        };

        const now = new Date();
        const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
        const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

        this.errorLog.forEach(error => {
            // 按类型统计
            stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;
            
            // 时间统计
            const errorTime = new Date(error.timestamp);
            if (errorTime > oneHourAgo) {
                stats.lastHour++;
            }
            if (errorTime > oneDayAgo) {
                stats.lastDay++;
            }
        });

        return stats;
    }

    /**
     * 清除错误日志
     */
    clearErrorLog() {
        this.errorLog = [];
        localStorage.removeItem('cms_error_log');
    }

    /**
     * 导出错误日志
     */
    exportErrorLog() {
        const data = {
            errors: this.errorLog,
            stats: this.getErrorStats(),
            exportTime: new Date().toISOString(),
            version: '1.0.0'
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `cms-error-log-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    /**
     * 保存错误日志
     */
    saveErrorLog() {
        try {
            localStorage.setItem('cms_error_log', JSON.stringify(this.errorLog));
        } catch (error) {
            console.warn('保存错误日志失败:', error);
        }
    }

    /**
     * 加载错误日志
     */
    loadErrorLog() {
        try {
            const saved = localStorage.getItem('cms_error_log');
            if (saved) {
                this.errorLog = JSON.parse(saved);
            }
        } catch (error) {
            console.warn('加载错误日志失败:', error);
            this.errorLog = [];
        }
    }

    /**
     * 获取调试模式
     */
    getDebugMode() {
        return localStorage.getItem('cms_debug_mode') === 'true' || 
               window.location.hostname === 'localhost' ||
               window.location.search.includes('debug=true');
    }

    /**
     * 设置调试模式
     */
    setDebugMode(enabled) {
        this.isDebugMode = enabled;
        localStorage.setItem('cms_debug_mode', enabled.toString());
    }

    /**
     * 创建错误边界
     */
    createErrorBoundary(element, fallbackContent = '加载失败') {
        const originalContent = element.innerHTML;
        
        try {
            // 监听元素内的错误
            element.addEventListener('error', (event) => {
                this.recordError(event.error, { element: element.tagName });
                element.innerHTML = `<div class="error-fallback">${fallbackContent}</div>`;
            }, true);
            
        } catch (error) {
            this.recordError(error, { context: 'createErrorBoundary' });
        }
    }

    /**
     * 安全执行函数
     */
    async safeExecute(fn, context = {}, fallback = null) {
        try {
            return await fn();
        } catch (error) {
            this.recordError(error, context);
            return fallback;
        }
    }

    /**
     * 重试机制
     */
    async retry(fn, maxAttempts = 3, delay = 1000) {
        let lastError;
        
        for (let attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                return await fn();
            } catch (error) {
                lastError = error;
                
                if (attempt === maxAttempts) {
                    this.recordError(error, { 
                        context: 'retry', 
                        attempts: maxAttempts 
                    });
                    throw error;
                }
                
                // 等待后重试
                await new Promise(resolve => setTimeout(resolve, delay * attempt));
            }
        }
    }
}

// 创建全局错误处理器实例
window.cmsErrorHandler = new CMSErrorHandler();

// 导出类
window.CMSErrorHandler = CMSErrorHandler;
