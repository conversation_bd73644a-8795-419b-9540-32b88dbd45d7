/**
 * Sky Mirror CMS - 图片管理模块
 * 负责图片的上传、管理和组织
 */

class ImageManager {
    constructor(dataManager) {
        this.dataManager = dataManager;
        this.currentImages = [];
        this.selectedCategory = 'all';
        
        this.init();
    }

    /**
     * 初始化图片管理器
     */
    init() {
        console.log('图片管理器初始化');
        this.bindEvents();
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 上传图片按钮
        const uploadImageBtn = document.getElementById('uploadImageBtn');
        if (uploadImageBtn) {
            uploadImageBtn.addEventListener('click', () => this.showUploadModal());
        }
    }

    /**
     * 加载图片页面
     */
    async loadImagesPage() {
        try {
            console.log('加载图片页面');
            
            // 获取图片数据
            this.currentImages = await this.dataManager.getAllItems('images');
            
            // 渲染图片列表
            this.renderImagesList();
            
        } catch (error) {
            console.error('加载图片页面失败:', error);
            this.showNotification('加载图片数据失败', 'error');
        }
    }

    /**
     * 渲染图片列表
     */
    renderImagesList() {
        const imagesContent = document.querySelector('#imagesPage .images-content');
        if (!imagesContent) return;

        if (this.currentImages.length === 0) {
            imagesContent.innerHTML = this.renderEmptyState();
            return;
        }

        // 过滤图片
        let filteredImages = this.filterImages(this.currentImages);

        imagesContent.innerHTML = `
            <div class="images-toolbar">
                ${this.renderImageToolbar()}
            </div>
            <div class="images-grid">
                ${filteredImages.map(image => this.renderImageCard(image)).join('')}
            </div>
        `;

        // 绑定事件
        this.bindImageEvents();
    }

    /**
     * 渲染图片工具栏
     */
    renderImageToolbar() {
        return `
            <div class="toolbar-left">
                <select id="categoryFilter" class="form-select">
                    <option value="all">所有分类</option>
                    <option value="routes">旅游路线</option>
                    <option value="packages">套餐产品</option>
                    <option value="hero">首页横幅</option>
                    <option value="gallery">图片画廊</option>
                </select>
            </div>
            <div class="toolbar-right">
                <div class="view-toggle">
                    <button class="btn btn-outline active" data-view="grid">
                        <i class="fas fa-th"></i>
                    </button>
                    <button class="btn btn-outline" data-view="list">
                        <i class="fas fa-list"></i>
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * 渲染图片卡片
     */
    renderImageCard(image) {
        const featuredBadge = image.featured ? 
            '<div class="image-badge featured">精选</div>' : '';

        return `
            <div class="image-card" data-image-id="${image.id}">
                <div class="image-preview">
                    <img src="${image.url}" alt="${image.alt}" loading="lazy">
                    ${featuredBadge}
                    <div class="image-overlay">
                        <button class="btn btn-sm btn-primary view-image" data-image-id="${image.id}">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline edit-image" data-image-id="${image.id}">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger delete-image" data-image-id="${image.id}">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="image-info">
                    <h4 class="image-title">${image.title}</h4>
                    <div class="image-meta">
                        <span class="image-category">${this.getCategoryText(image.category)}</span>
                        <span class="image-size">${image.size.width}×${image.size.height}</span>
                    </div>
                    <div class="image-tags">
                        ${image.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                    </div>
                    <div class="image-usage">
                        使用于: ${image.usedIn.length} 个项目
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 渲染空状态
     */
    renderEmptyState() {
        return `
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-images"></i>
                </div>
                <h3>暂无图片</h3>
                <p>开始上传您的第一张图片吧</p>
                <button class="btn btn-primary" onclick="imageManager.showUploadModal()">
                    <i class="fas fa-upload"></i>
                    上传图片
                </button>
            </div>
        `;
    }

    /**
     * 过滤图片
     */
    filterImages(images) {
        if (this.selectedCategory === 'all') {
            return images;
        }
        return images.filter(image => image.category === this.selectedCategory);
    }

    /**
     * 绑定图片事件
     */
    bindImageEvents() {
        // 分类筛选
        const categoryFilter = document.getElementById('categoryFilter');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', (e) => {
                this.selectedCategory = e.target.value;
                this.renderImagesList();
            });
        }

        // 查看图片
        document.querySelectorAll('.view-image').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const imageId = e.target.closest('[data-image-id]').dataset.imageId;
                this.viewImage(imageId);
            });
        });

        // 编辑图片
        document.querySelectorAll('.edit-image').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const imageId = e.target.closest('[data-image-id]').dataset.imageId;
                this.editImage(imageId);
            });
        });

        // 删除图片
        document.querySelectorAll('.delete-image').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const imageId = e.target.closest('[data-image-id]').dataset.imageId;
                this.deleteImage(imageId);
            });
        });

        // 视图切换
        document.querySelectorAll('[data-view]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const view = e.target.closest('[data-view]').dataset.view;
                this.switchView(view);
            });
        });
    }

    /**
     * 显示上传模态框
     */
    showUploadModal() {
        this.showImageModal();
    }

    /**
     * 显示图片编辑模态框
     */
    showImageModal(image = null) {
        const isEdit = image !== null;
        const modalTitle = isEdit ? '编辑图片' : '添加图片';

        const modalHtml = `
            <div class="modal-overlay" id="imageModal">
                <div class="modal" style="max-width: 700px;">
                    <div class="modal-header">
                        <h3 class="modal-title">${modalTitle}</h3>
                        <button class="modal-close" onclick="imageManager.closeImageModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="imageForm" class="image-form">
                            <div class="form-group">
                                <label for="imageTitle" class="form-label required">图片标题</label>
                                <input type="text" id="imageTitle" name="title" class="form-control"
                                       placeholder="请输入图片标题" required>
                            </div>

                            <div class="form-group">
                                <label for="imageUrl" class="form-label required">图片URL</label>
                                <input type="url" id="imageUrl" name="url" class="form-control"
                                       placeholder="https://example.com/image.jpg" required>
                                <small class="form-help">支持 JPG、PNG、WebP 格式</small>
                            </div>

                            <div class="image-preview-section" id="imagePreviewSection" style="display: none;">
                                <label class="form-label">图片预览</label>
                                <div class="image-preview-container">
                                    <img id="imagePreview" class="image-preview" alt="图片预览">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="imageAlt" class="form-label required">替代文本</label>
                                    <input type="text" id="imageAlt" name="alt" class="form-control"
                                           placeholder="图片的描述文本" required>
                                    <small class="form-help">用于无障碍访问和SEO优化</small>
                                </div>
                                <div class="form-group">
                                    <label for="imageCategory" class="form-label required">图片分类</label>
                                    <select id="imageCategory" name="category" class="form-control form-select" required>
                                        <option value="">请选择分类</option>
                                        <option value="routes">旅游路线</option>
                                        <option value="packages">套餐产品</option>
                                        <option value="hero">首页横幅</option>
                                        <option value="gallery">图片画廊</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="imageDescription" class="form-label">图片描述</label>
                                <textarea id="imageDescription" name="description" class="form-control"
                                          placeholder="请输入图片的详细描述" rows="3"></textarea>
                            </div>

                            <div class="form-group">
                                <label for="imageTags" class="form-label">标签</label>
                                <input type="text" id="imageTags" name="tags" class="form-control"
                                       placeholder="用逗号分隔，例如：天空之镜,瓜拉雪兰莪,自然奇观">
                                <small class="form-help">用逗号分隔多个标签，便于搜索和分类</small>
                            </div>

                            <div class="form-group">
                                <label for="imageUsedIn" class="form-label">使用位置</label>
                                <input type="text" id="imageUsedIn" name="usedIn" class="form-control"
                                       placeholder="用逗号分隔，例如：sky-mirror-basic,combo-sky-eagle" readonly>
                                <small class="form-help">此图片被使用的项目ID（自动更新）</small>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="imageStatus" class="form-label required">状态</label>
                                    <select id="imageStatus" name="status" class="form-control form-select" required>
                                        <option value="active">已发布</option>
                                        <option value="inactive">已下线</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <div class="form-check" style="margin-top: 2rem;">
                                        <input type="checkbox" id="imageFeatured" name="featured" class="form-check-input">
                                        <label for="imageFeatured" class="form-check-label">设为精选图片</label>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline" onclick="imageManager.closeImageModal()">
                            取消
                        </button>
                        <button type="submit" form="imageForm" class="btn btn-primary" id="saveImageBtn">
                            <i class="fas fa-save"></i>
                            ${isEdit ? '更新图片' : '添加图片'}
                        </button>
                    </div>
                </div>
            </div>
        `;

        // 添加模态框到页面
        const modalContainer = document.getElementById('modalContainer');
        modalContainer.innerHTML = modalHtml;

        // 如果是编辑模式，填充表单数据
        if (isEdit && image) {
            this.populateImageForm(image);
        }

        // 绑定表单事件
        this.bindImageFormEvents();

        // 显示模态框
        setTimeout(() => {
            document.getElementById('imageModal').classList.add('show');
        }, 100);
    }

    /**
     * 查看图片
     */
    viewImage(imageId) {
        const image = this.currentImages.find(img => img.id === imageId);
        if (!image) {
            this.showNotification('未找到指定图片', 'error');
            return;
        }

        this.showImageViewModal(image);
    }

    /**
     * 编辑图片
     */
    editImage(imageId) {
        const image = this.currentImages.find(img => img.id === imageId);
        if (!image) {
            this.showNotification('未找到指定图片', 'error');
            return;
        }

        this.editingImage = image;
        this.showImageModal(image);
    }

    /**
     * 删除图片
     */
    async deleteImage(imageId) {
        try {
            const image = this.currentImages.find(img => img.id === imageId);
            if (!image) {
                this.showNotification('未找到指定图片', 'error');
                return;
            }

            // 检查图片使用情况
            if (image.usedIn && image.usedIn.length > 0) {
                const confirmed = await this.showConfirmDialog(
                    '确认删除',
                    `图片"${image.title}"正在被 ${image.usedIn.length} 个项目使用。删除后这些项目将无法显示该图片。确定要删除吗？`,
                    'danger'
                );
                if (!confirmed) return;
            } else {
                const confirmed = await this.showConfirmDialog(
                    '确认删除',
                    `确定要删除图片"${image.title}"吗？此操作不可撤销。`,
                    'danger'
                );
                if (!confirmed) return;
            }

            // 删除图片
            await this.dataManager.deleteItem('images', imageId);

            this.showNotification('图片删除成功', 'success');

            // 重新加载图片列表
            await this.loadImagesPage();

        } catch (error) {
            console.error('删除图片失败:', error);
            this.showNotification('删除图片失败: ' + error.message, 'error');
        }
    }

    /**
     * 显示图片查看模态框
     */
    showImageViewModal(image) {
        const modalHtml = `
            <div class="modal-overlay" id="imageViewModal">
                <div class="modal" style="max-width: 800px;">
                    <div class="modal-header">
                        <h3 class="modal-title">查看图片 - ${image.title}</h3>
                        <button class="modal-close" onclick="imageManager.closeImageViewModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="image-view-container">
                            <div class="image-display">
                                <img src="${image.url}" alt="${image.alt}" class="full-image">
                            </div>
                            <div class="image-details">
                                <div class="detail-group">
                                    <label>标题:</label>
                                    <span>${image.title}</span>
                                </div>
                                <div class="detail-group">
                                    <label>分类:</label>
                                    <span class="category-badge">${this.getCategoryText(image.category)}</span>
                                </div>
                                <div class="detail-group">
                                    <label>尺寸:</label>
                                    <span>${image.size ? `${image.size.width}×${image.size.height}` : '未知'}</span>
                                </div>
                                <div class="detail-group">
                                    <label>状态:</label>
                                    <span class="status-badge ${image.status}">${image.status === 'active' ? '已发布' : '已下线'}</span>
                                </div>
                                ${image.description ? `
                                <div class="detail-group">
                                    <label>描述:</label>
                                    <span>${image.description}</span>
                                </div>
                                ` : ''}
                                ${image.tags && image.tags.length > 0 ? `
                                <div class="detail-group">
                                    <label>标签:</label>
                                    <div class="tags-display">
                                        ${image.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                                    </div>
                                </div>
                                ` : ''}
                                ${image.usedIn && image.usedIn.length > 0 ? `
                                <div class="detail-group">
                                    <label>使用位置:</label>
                                    <div class="usage-list">
                                        ${image.usedIn.map(usage => `<span class="usage-item">${usage}</span>`).join('')}
                                    </div>
                                </div>
                                ` : ''}
                                <div class="detail-group">
                                    <label>上传时间:</label>
                                    <span>${this.formatDate(image.uploadedAt)}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline" onclick="imageManager.closeImageViewModal()">
                            关闭
                        </button>
                        <button type="button" class="btn btn-primary" onclick="imageManager.editImage('${image.id}'); imageManager.closeImageViewModal();">
                            <i class="fas fa-edit"></i>
                            编辑图片
                        </button>
                    </div>
                </div>
            </div>
        `;

        // 添加模态框到页面
        const modalContainer = document.getElementById('modalContainer');
        modalContainer.innerHTML = modalHtml;

        // 显示模态框
        setTimeout(() => {
            document.getElementById('imageViewModal').classList.add('show');
        }, 100);
    }

    /**
     * 关闭图片查看模态框
     */
    closeImageViewModal() {
        const modal = document.getElementById('imageViewModal');
        if (modal) {
            modal.classList.remove('show');
            setTimeout(() => {
                modal.remove();
            }, 300);
        }
    }

    /**
     * 填充图片表单数据
     */
    populateImageForm(image) {
        document.getElementById('imageTitle').value = image.title || '';
        document.getElementById('imageUrl').value = image.url || '';
        document.getElementById('imageAlt').value = image.alt || '';
        document.getElementById('imageCategory').value = image.category || '';
        document.getElementById('imageDescription').value = image.description || '';
        document.getElementById('imageTags').value = image.tags ? image.tags.join(', ') : '';
        document.getElementById('imageUsedIn').value = image.usedIn ? image.usedIn.join(', ') : '';
        document.getElementById('imageStatus').value = image.status || 'active';
        document.getElementById('imageFeatured').checked = image.featured || false;

        // 显示图片预览
        if (image.url) {
            this.showImagePreview(image.url);
        }
    }

    /**
     * 绑定图片表单事件
     */
    bindImageFormEvents() {
        const form = document.getElementById('imageForm');
        if (!form) return;

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            await this.saveImage();
        });

        // 图片URL预览
        const imageUrlInput = document.getElementById('imageUrl');
        imageUrlInput.addEventListener('blur', () => {
            this.showImagePreview(imageUrlInput.value);
        });
    }

    /**
     * 显示图片预览
     */
    showImagePreview(imageUrl) {
        if (!imageUrl) return;

        const previewSection = document.getElementById('imagePreviewSection');
        const previewImg = document.getElementById('imagePreview');

        previewImg.src = imageUrl;
        previewImg.onload = () => {
            previewSection.style.display = 'block';
        };
        previewImg.onerror = () => {
            previewSection.style.display = 'none';
            this.showNotification('图片加载失败，请检查URL是否正确', 'warning');
        };
    }

    /**
     * 保存图片
     */
    async saveImage() {
        try {
            const formData = this.getImageFormData();

            // 验证表单数据
            if (!this.validateImageData(formData)) {
                return;
            }

            // 显示保存状态
            const saveBtn = document.getElementById('saveImageBtn');
            const originalText = saveBtn.innerHTML;
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 保存中...';
            saveBtn.disabled = true;

            if (this.editingImage) {
                // 更新现有图片
                await this.dataManager.updateItem('images', this.editingImage.id, formData);
                this.showNotification('图片更新成功', 'success');
            } else {
                // 添加新图片
                await this.dataManager.addItem('images', formData);
                this.showNotification('图片添加成功', 'success');
            }

            // 关闭模态框
            this.closeImageModal();

            // 重新加载图片列表
            await this.loadImagesPage();

        } catch (error) {
            console.error('保存图片失败:', error);
            this.showNotification('保存图片失败: ' + error.message, 'error');
        } finally {
            // 恢复按钮状态
            const saveBtn = document.getElementById('saveImageBtn');
            if (saveBtn) {
                saveBtn.innerHTML = this.editingImage ?
                    '<i class="fas fa-save"></i> 更新图片' :
                    '<i class="fas fa-save"></i> 添加图片';
                saveBtn.disabled = false;
            }
        }
    }

    /**
     * 获取表单数据
     */
    getImageFormData() {
        const form = document.getElementById('imageForm');
        const formData = new FormData(form);

        // 处理标签数组
        const tags = formData.get('tags').split(',').map(tag => tag.trim()).filter(tag => tag);

        // 处理使用位置数组
        const usedIn = formData.get('usedIn').split(',').map(usage => usage.trim()).filter(usage => usage);

        return {
            title: formData.get('title'),
            url: formData.get('url'),
            alt: formData.get('alt'),
            category: formData.get('category'),
            description: formData.get('description') || null,
            tags: tags,
            usedIn: usedIn,
            status: formData.get('status'),
            featured: formData.has('featured'),
            size: {
                width: 0,
                height: 0,
                fileSize: '未知'
            }
        };
    }

    /**
     * 验证图片数据
     */
    validateImageData(data) {
        const errors = [];

        if (!data.title || data.title.trim().length < 2) {
            errors.push('图片标题至少需要2个字符');
        }

        if (!data.url || !this.isValidUrl(data.url)) {
            errors.push('请输入有效的图片URL');
        }

        if (!data.alt || data.alt.trim().length < 2) {
            errors.push('替代文本至少需要2个字符');
        }

        if (!data.category) {
            errors.push('请选择图片分类');
        }

        if (errors.length > 0) {
            this.showNotification('表单验证失败：\n' + errors.join('\n'), 'error');
            return false;
        }

        return true;
    }

    /**
     * 验证URL格式
     */
    isValidUrl(string) {
        try {
            new URL(string);
            return true;
        } catch (_) {
            return false;
        }
    }

    /**
     * 关闭图片模态框
     */
    closeImageModal() {
        const modal = document.getElementById('imageModal');
        if (modal) {
            modal.classList.remove('show');
            setTimeout(() => {
                modal.remove();
            }, 300);
        }
        this.editingImage = null;
    }

    /**
     * 格式化日期
     */
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    /**
     * 切换视图
     */
    switchView(view) {
        console.log('切换视图:', view);
        
        // 更新按钮状态
        document.querySelectorAll('[data-view]').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-view="${view}"]`).classList.add('active');
        
        // 这里可以实现不同的视图布局
        this.showNotification('视图切换功能正在开发中', 'info');
    }

    /**
     * 获取分类文本
     */
    getCategoryText(category) {
        const categoryMap = {
            routes: '旅游路线',
            packages: '套餐产品',
            hero: '首页横幅',
            gallery: '图片画廊'
        };
        return categoryMap[category] || category;
    }

    /**
     * 显示确认对话框
     */
    showConfirmDialog(title, message, type = 'warning') {
        return new Promise((resolve) => {
            const modalHtml = `
                <div class="modal-overlay" id="confirmModal">
                    <div class="modal" style="max-width: 400px;">
                        <div class="modal-header">
                            <h3 class="modal-title">${title}</h3>
                        </div>
                        <div class="modal-body">
                            <div class="confirm-content">
                                <div class="confirm-icon ${type}">
                                    <i class="fas ${type === 'danger' ? 'fa-exclamation-triangle' : 'fa-question-circle'}"></i>
                                </div>
                                <p>${message}</p>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline" onclick="imageManager.closeConfirmDialog(false)">
                                取消
                            </button>
                            <button type="button" class="btn btn-${type === 'danger' ? 'danger' : 'primary'}"
                                    onclick="imageManager.closeConfirmDialog(true)">
                                确认
                            </button>
                        </div>
                    </div>
                </div>
            `;

            // 添加模态框到页面
            const modalContainer = document.getElementById('modalContainer');
            modalContainer.innerHTML = modalHtml;

            // 显示模态框
            setTimeout(() => {
                document.getElementById('confirmModal').classList.add('show');
            }, 100);

            // 保存resolve函数
            this.confirmResolve = resolve;
        });
    }

    /**
     * 关闭确认对话框
     */
    closeConfirmDialog(result) {
        const modal = document.getElementById('confirmModal');
        if (modal) {
            modal.classList.remove('show');
            setTimeout(() => {
                modal.remove();
            }, 300);
        }

        if (this.confirmResolve) {
            this.confirmResolve(result);
            this.confirmResolve = null;
        }
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        if (window.cmsCore) {
            window.cmsCore.showNotification(message, type);
        } else {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }
}

// 导出到全局作用域
window.ImageManager = ImageManager;
