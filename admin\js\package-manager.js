/**
 * Sky Mirror CMS - 套餐管理模块
 * 负责套餐的增删改查功能
 */

class PackageManager {
    constructor(dataManager) {
        this.dataManager = dataManager;
        this.currentPackages = [];
        this.editingPackage = null;
        
        this.init();
    }

    /**
     * 初始化套餐管理器
     */
    init() {
        console.log('套餐管理器初始化');
        this.bindEvents();
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 添加套餐按钮
        const addPackageBtn = document.getElementById('addPackageBtn');
        if (addPackageBtn) {
            addPackageBtn.addEventListener('click', () => this.showAddPackageModal());
        }
    }

    /**
     * 加载套餐页面
     */
    async loadPackagesPage() {
        try {
            console.log('加载套餐页面');
            
            // 获取套餐数据
            this.currentPackages = await this.dataManager.getAllItems('packages');
            
            // 渲染套餐列表
            this.renderPackagesList();
            
        } catch (error) {
            console.error('加载套餐页面失败:', error);
            this.showNotification('加载套餐数据失败', 'error');
        }
    }

    /**
     * 渲染套餐列表
     */
    renderPackagesList() {
        const packagesContent = document.querySelector('#packagesPage .packages-content');
        if (!packagesContent) return;

        if (this.currentPackages.length === 0) {
            packagesContent.innerHTML = this.renderEmptyState();
            return;
        }

        packagesContent.innerHTML = `
            <div class="packages-grid">
                ${this.currentPackages.map(pkg => this.renderPackageCard(pkg)).join('')}
            </div>
        `;

        // 绑定卡片事件
        this.bindPackageCardEvents();
    }

    /**
     * 渲染套餐卡片
     */
    renderPackageCard(pkg) {
        const featuredBadge = pkg.featured ? 
            '<div class="package-badge featured">精选</div>' : '';

        const statusClass = pkg.status === 'active' ? 'status-active' : 'status-inactive';

        return `
            <div class="package-card" data-package-id="${pkg.id}">
                <div class="package-header">
                    <h3 class="package-title">${pkg.title}</h3>
                    ${featuredBadge}
                    <div class="package-status ${statusClass}">
                        ${pkg.status === 'active' ? '已发布' : '已下线'}
                    </div>
                </div>
                <div class="package-content">
                    <div class="package-meta">
                        <span class="package-type">${this.getTypeText(pkg.type)}</span>
                        <span class="package-duration">${pkg.duration}</span>
                    </div>
                    <p class="package-description">${pkg.description.substring(0, 100)}...</p>
                    <div class="package-pricing">
                        <div class="price-item">
                            <span class="price-label">成人</span>
                            <span class="price-value">${pkg.pricing.currency} ${pkg.pricing.adult}</span>
                        </div>
                        <div class="price-item">
                            <span class="price-label">儿童</span>
                            <span class="price-value">${pkg.pricing.currency} ${pkg.pricing.child}</span>
                        </div>
                    </div>
                </div>
                <div class="package-actions">
                    <button class="btn btn-sm btn-outline edit-package" data-package-id="${pkg.id}">
                        <i class="fas fa-edit"></i>
                        编辑
                    </button>
                    <button class="btn btn-sm btn-danger delete-package" data-package-id="${pkg.id}">
                        <i class="fas fa-trash"></i>
                        删除
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * 渲染空状态
     */
    renderEmptyState() {
        return `
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-box"></i>
                </div>
                <h3>暂无套餐</h3>
                <p>开始创建您的第一个套餐产品吧</p>
                <button class="btn btn-primary" onclick="packageManager.showAddPackageModal()">
                    <i class="fas fa-plus"></i>
                    添加套餐
                </button>
            </div>
        `;
    }

    /**
     * 绑定套餐卡片事件
     */
    bindPackageCardEvents() {
        // 编辑按钮
        document.querySelectorAll('.edit-package').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const packageId = e.target.closest('[data-package-id]').dataset.packageId;
                this.editPackage(packageId);
            });
        });

        // 删除按钮
        document.querySelectorAll('.delete-package').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const packageId = e.target.closest('[data-package-id]').dataset.packageId;
                this.deletePackage(packageId);
            });
        });
    }

    /**
     * 显示添加套餐模态框
     */
    showAddPackageModal() {
        this.editingPackage = null;
        this.showPackageModal();
    }

    /**
     * 显示套餐编辑模态框
     */
    showPackageModal(pkg = null) {
        const isEdit = pkg !== null;
        const modalTitle = isEdit ? '编辑套餐' : '添加套餐';

        const modalHtml = `
            <div class="modal-overlay" id="packageModal">
                <div class="modal" style="max-width: 900px;">
                    <div class="modal-header">
                        <h3 class="modal-title">${modalTitle}</h3>
                        <button class="modal-close" onclick="packageManager.closePackageModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="packageForm" class="package-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="packageTitle" class="form-label required">套餐标题</label>
                                    <input type="text" id="packageTitle" name="title" class="form-control"
                                           placeholder="请输入套餐标题" required>
                                </div>
                                <div class="form-group">
                                    <label for="packageType" class="form-label required">套餐类型</label>
                                    <select id="packageType" name="type" class="form-control form-select" required>
                                        <option value="">请选择类型</option>
                                        <option value="signature-tickets">特色船票</option>
                                        <option value="transport">交通服务</option>
                                        <option value="accommodation">住宿服务</option>
                                        <option value="tour">旅游套餐</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="packageDescription" class="form-label required">套餐描述</label>
                                <textarea id="packageDescription" name="description" class="form-control form-textarea"
                                          placeholder="请输入套餐详细描述" rows="4" required></textarea>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="packageDuration" class="form-label required">活动时长</label>
                                    <input type="text" id="packageDuration" name="duration" class="form-control"
                                           placeholder="例如：4-5小时" required>
                                </div>
                                <div class="form-group">
                                    <label for="packageLocation" class="form-label required">活动地点</label>
                                    <input type="text" id="packageLocation" name="location" class="form-control"
                                           placeholder="例如：瓜拉雪兰莪" required>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="packageBestTime" class="form-label">最佳时间</label>
                                <input type="text" id="packageBestTime" name="bestTime" class="form-control"
                                       placeholder="例如：早上6:00-10:00">
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="packageIncludes" class="form-label required">包含项目</label>
                                    <textarea id="packageIncludes" name="includes" class="form-control"
                                              placeholder="每行一个项目，例如：&#10;往返船票&#10;专业向导&#10;救生衣" rows="4" required></textarea>
                                    <small class="form-help">每行输入一个包含项目</small>
                                </div>
                                <div class="form-group">
                                    <label for="packageExcludes" class="form-label">不包含项目</label>
                                    <textarea id="packageExcludes" name="excludes" class="form-control"
                                              placeholder="每行一个项目，例如：&#10;餐食&#10;个人消费&#10;小费" rows="4"></textarea>
                                    <small class="form-help">每行输入一个不包含项目</small>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="packageTips" class="form-label">温馨提示</label>
                                <textarea id="packageTips" name="tips" class="form-control"
                                          placeholder="每行一个提示，例如：&#10;建议穿着鲜艳色彩的衣服&#10;请准备防晒用品" rows="3"></textarea>
                                <small class="form-help">每行输入一个温馨提示</small>
                            </div>

                            <div class="form-group">
                                <label for="packageTimeSlots" class="form-label">可选时间段</label>
                                <input type="text" id="packageTimeSlots" name="timeSlots" class="form-control"
                                       placeholder="用逗号分隔，例如：06:00,07:00,08:00,09:00">
                                <small class="form-help">用逗号分隔多个时间段</small>
                            </div>

                            <div class="pricing-section">
                                <h4>价格设置</h4>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="priceAdult" class="form-label required">成人价格</label>
                                        <input type="number" id="priceAdult" name="priceAdult" class="form-control"
                                               placeholder="150" min="0" step="1" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="priceChild" class="form-label required">儿童价格</label>
                                        <input type="number" id="priceChild" name="priceChild" class="form-control"
                                               placeholder="100" min="0" step="1" required>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="priceSenior" class="form-label">长者价格</label>
                                        <input type="number" id="priceSenior" name="priceSenior" class="form-control"
                                               placeholder="120" min="0" step="1">
                                    </div>
                                    <div class="form-group">
                                        <label for="priceCurrency" class="form-label required">货币</label>
                                        <select id="priceCurrency" name="currency" class="form-control form-select" required>
                                            <option value="RM">RM (马来西亚林吉特)</option>
                                            <option value="USD">USD (美元)</option>
                                            <option value="CNY">CNY (人民币)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="availability-section">
                                <h4>可用性设置</h4>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="maxCapacity" class="form-label">最大容量</label>
                                        <input type="number" id="maxCapacity" name="maxCapacity" class="form-control"
                                               placeholder="20" min="1" step="1">
                                    </div>
                                    <div class="form-group">
                                        <label for="minParticipants" class="form-label">最少参与人数</label>
                                        <input type="number" id="minParticipants" name="minParticipants" class="form-control"
                                               placeholder="2" min="1" step="1">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="advanceBooking" class="form-label">提前预订时间（小时）</label>
                                    <input type="number" id="advanceBooking" name="advanceBooking" class="form-control"
                                           placeholder="24" min="1" step="1">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="packageStatus" class="form-label required">状态</label>
                                    <select id="packageStatus" name="status" class="form-control form-select" required>
                                        <option value="active">已发布</option>
                                        <option value="inactive">已下线</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <div class="form-check" style="margin-top: 2rem;">
                                        <input type="checkbox" id="packageFeatured" name="featured" class="form-check-input">
                                        <label for="packageFeatured" class="form-check-label">设为精选套餐</label>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline" onclick="packageManager.closePackageModal()">
                            取消
                        </button>
                        <button type="submit" form="packageForm" class="btn btn-primary" id="savePackageBtn">
                            <i class="fas fa-save"></i>
                            ${isEdit ? '更新套餐' : '添加套餐'}
                        </button>
                    </div>
                </div>
            </div>
        `;

        // 添加模态框到页面
        const modalContainer = document.getElementById('modalContainer');
        modalContainer.innerHTML = modalHtml;

        // 如果是编辑模式，填充表单数据
        if (isEdit && pkg) {
            this.populatePackageForm(pkg);
        }

        // 绑定表单事件
        this.bindPackageFormEvents();

        // 显示模态框
        setTimeout(() => {
            document.getElementById('packageModal').classList.add('show');
        }, 100);
    }

    /**
     * 编辑套餐
     */
    editPackage(packageId) {
        const pkg = this.currentPackages.find(p => p.id === packageId);
        if (!pkg) {
            this.showNotification('未找到指定套餐', 'error');
            return;
        }

        this.editingPackage = pkg;
        this.showPackageModal(pkg);
    }

    /**
     * 删除套餐
     */
    async deletePackage(packageId) {
        try {
            const pkg = this.currentPackages.find(p => p.id === packageId);
            if (!pkg) {
                this.showNotification('未找到指定套餐', 'error');
                return;
            }

            // 确认删除
            const confirmed = await this.showConfirmDialog(
                '确认删除',
                `确定要删除套餐"${pkg.title}"吗？此操作不可撤销。`,
                'danger'
            );

            if (!confirmed) return;

            // 删除套餐
            await this.dataManager.deleteItem('packages', packageId);

            this.showNotification('套餐删除成功', 'success');

            // 重新加载套餐列表
            await this.loadPackagesPage();

        } catch (error) {
            console.error('删除套餐失败:', error);
            this.showNotification('删除套餐失败: ' + error.message, 'error');
        }
    }

    /**
     * 填充套餐表单数据
     */
    populatePackageForm(pkg) {
        document.getElementById('packageTitle').value = pkg.title || '';
        document.getElementById('packageType').value = pkg.type || '';
        document.getElementById('packageDescription').value = pkg.description || '';
        document.getElementById('packageDuration').value = pkg.duration || '';
        document.getElementById('packageLocation').value = pkg.location || '';
        document.getElementById('packageBestTime').value = pkg.bestTime || '';

        // 处理数组字段
        document.getElementById('packageIncludes').value = pkg.includes ? pkg.includes.join('\n') : '';
        document.getElementById('packageExcludes').value = pkg.excludes ? pkg.excludes.join('\n') : '';
        document.getElementById('packageTips').value = pkg.tips ? pkg.tips.join('\n') : '';
        document.getElementById('packageTimeSlots').value = pkg.timeSlots ? pkg.timeSlots.join(', ') : '';

        // 价格设置
        if (pkg.pricing) {
            document.getElementById('priceAdult').value = pkg.pricing.adult || '';
            document.getElementById('priceChild').value = pkg.pricing.child || '';
            document.getElementById('priceSenior').value = pkg.pricing.senior || '';
            document.getElementById('priceCurrency').value = pkg.pricing.currency || 'RM';
        }

        // 可用性设置
        if (pkg.availability) {
            document.getElementById('maxCapacity').value = pkg.availability.maxCapacity || '';
            document.getElementById('minParticipants').value = pkg.availability.minParticipants || '';
            document.getElementById('advanceBooking').value = pkg.availability.advanceBooking || '';
        }

        document.getElementById('packageStatus').value = pkg.status || 'active';
        document.getElementById('packageFeatured').checked = pkg.featured || false;
    }

    /**
     * 绑定套餐表单事件
     */
    bindPackageFormEvents() {
        const form = document.getElementById('packageForm');
        if (!form) return;

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            await this.savePackage();
        });
    }

    /**
     * 保存套餐
     */
    async savePackage() {
        try {
            const formData = this.getPackageFormData();

            // 验证表单数据
            if (!this.validatePackageData(formData)) {
                return;
            }

            // 显示保存状态
            const saveBtn = document.getElementById('savePackageBtn');
            const originalText = saveBtn.innerHTML;
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 保存中...';
            saveBtn.disabled = true;

            if (this.editingPackage) {
                // 更新现有套餐
                await this.dataManager.updateItem('packages', this.editingPackage.id, formData);
                this.showNotification('套餐更新成功', 'success');
            } else {
                // 添加新套餐
                await this.dataManager.addItem('packages', formData);
                this.showNotification('套餐添加成功', 'success');
            }

            // 关闭模态框
            this.closePackageModal();

            // 重新加载套餐列表
            await this.loadPackagesPage();

        } catch (error) {
            console.error('保存套餐失败:', error);
            this.showNotification('保存套餐失败: ' + error.message, 'error');
        } finally {
            // 恢复按钮状态
            const saveBtn = document.getElementById('savePackageBtn');
            if (saveBtn) {
                saveBtn.innerHTML = this.editingPackage ?
                    '<i class="fas fa-save"></i> 更新套餐' :
                    '<i class="fas fa-save"></i> 添加套餐';
                saveBtn.disabled = false;
            }
        }
    }

    /**
     * 获取表单数据
     */
    getPackageFormData() {
        const form = document.getElementById('packageForm');
        const formData = new FormData(form);

        // 处理数组字段
        const includes = formData.get('includes').split('\n').map(item => item.trim()).filter(item => item);
        const excludes = formData.get('excludes').split('\n').map(item => item.trim()).filter(item => item);
        const tips = formData.get('tips').split('\n').map(item => item.trim()).filter(item => item);
        const timeSlots = formData.get('timeSlots').split(',').map(slot => slot.trim()).filter(slot => slot);

        return {
            title: formData.get('title'),
            type: formData.get('type'),
            description: formData.get('description'),
            duration: formData.get('duration'),
            location: formData.get('location'),
            bestTime: formData.get('bestTime') || null,
            includes: includes,
            excludes: excludes,
            tips: tips,
            timeSlots: timeSlots,
            pricing: {
                adult: parseInt(formData.get('priceAdult')),
                child: parseInt(formData.get('priceChild')),
                senior: formData.get('priceSenior') ? parseInt(formData.get('priceSenior')) : null,
                currency: formData.get('currency')
            },
            availability: {
                maxCapacity: formData.get('maxCapacity') ? parseInt(formData.get('maxCapacity')) : null,
                minParticipants: formData.get('minParticipants') ? parseInt(formData.get('minParticipants')) : null,
                advanceBooking: formData.get('advanceBooking') ? parseInt(formData.get('advanceBooking')) : null
            },
            status: formData.get('status'),
            featured: formData.has('featured'),
            category: formData.get('type') // 使用类型作为分类
        };
    }

    /**
     * 验证套餐数据
     */
    validatePackageData(data) {
        const errors = [];

        if (!data.title || data.title.trim().length < 2) {
            errors.push('套餐标题至少需要2个字符');
        }

        if (!data.type) {
            errors.push('请选择套餐类型');
        }

        if (!data.description || data.description.trim().length < 10) {
            errors.push('套餐描述至少需要10个字符');
        }

        if (!data.duration) {
            errors.push('请输入活动时长');
        }

        if (!data.location) {
            errors.push('请输入活动地点');
        }

        if (!data.includes || data.includes.length === 0) {
            errors.push('请至少输入一个包含项目');
        }

        if (!data.pricing.adult || data.pricing.adult <= 0) {
            errors.push('请输入有效的成人价格');
        }

        if (!data.pricing.child || data.pricing.child <= 0) {
            errors.push('请输入有效的儿童价格');
        }

        if (errors.length > 0) {
            this.showNotification('表单验证失败：\n' + errors.join('\n'), 'error');
            return false;
        }

        return true;
    }

    /**
     * 关闭套餐模态框
     */
    closePackageModal() {
        const modal = document.getElementById('packageModal');
        if (modal) {
            modal.classList.remove('show');
            setTimeout(() => {
                modal.remove();
            }, 300);
        }
        this.editingPackage = null;
    }

    /**
     * 显示确认对话框
     */
    showConfirmDialog(title, message, type = 'warning') {
        return new Promise((resolve) => {
            const modalHtml = `
                <div class="modal-overlay" id="confirmModal">
                    <div class="modal" style="max-width: 400px;">
                        <div class="modal-header">
                            <h3 class="modal-title">${title}</h3>
                        </div>
                        <div class="modal-body">
                            <div class="confirm-content">
                                <div class="confirm-icon ${type}">
                                    <i class="fas ${type === 'danger' ? 'fa-exclamation-triangle' : 'fa-question-circle'}"></i>
                                </div>
                                <p>${message}</p>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline" onclick="packageManager.closeConfirmDialog(false)">
                                取消
                            </button>
                            <button type="button" class="btn btn-${type === 'danger' ? 'danger' : 'primary'}"
                                    onclick="packageManager.closeConfirmDialog(true)">
                                确认
                            </button>
                        </div>
                    </div>
                </div>
            `;

            // 添加模态框到页面
            const modalContainer = document.getElementById('modalContainer');
            modalContainer.innerHTML = modalHtml;

            // 显示模态框
            setTimeout(() => {
                document.getElementById('confirmModal').classList.add('show');
            }, 100);

            // 保存resolve函数
            this.confirmResolve = resolve;
        });
    }

    /**
     * 关闭确认对话框
     */
    closeConfirmDialog(result) {
        const modal = document.getElementById('confirmModal');
        if (modal) {
            modal.classList.remove('show');
            setTimeout(() => {
                modal.remove();
            }, 300);
        }

        if (this.confirmResolve) {
            this.confirmResolve(result);
            this.confirmResolve = null;
        }
    }

    /**
     * 获取类型文本
     */
    getTypeText(type) {
        const typeMap = {
            'signature-tickets': '特色船票',
            'transport': '交通服务',
            'accommodation': '住宿服务',
            'tour': '旅游套餐'
        };
        return typeMap[type] || type;
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        if (window.cmsCore) {
            window.cmsCore.showNotification(message, type);
        } else {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }
}

// 导出到全局作用域
window.PackageManager = PackageManager;
