/**
 * Sky Mirror CMS - 路线管理模块
 * 负责路线的增删改查功能
 */

class RouteManager {
    constructor(dataManager) {
        this.dataManager = dataManager;
        this.currentRoutes = [];
        this.editingRoute = null;
        this.sortBy = 'sortOrder';
        this.sortDirection = 'asc';
        this.filterStatus = 'all';
        
        this.init();
    }

    /**
     * 初始化路线管理器
     */
    init() {
        console.log('路线管理器初始化');
        this.bindEvents();
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 添加路线按钮
        const addRouteBtn = document.getElementById('addRouteBtn');
        if (addRouteBtn) {
            addRouteBtn.addEventListener('click', () => this.showAddRouteModal());
        }
    }

    /**
     * 加载路线页面
     */
    async loadRoutesPage() {
        try {
            console.log('加载路线页面');
            
            // 获取路线数据
            this.currentRoutes = await this.dataManager.getAllItems('routes');
            
            // 渲染路线列表
            this.renderRoutesList();
            
        } catch (error) {
            console.error('加载路线页面失败:', error);
            this.showNotification('加载路线数据失败', 'error');
        }
    }

    /**
     * 渲染路线列表
     */
    renderRoutesList() {
        const routesContent = document.querySelector('#routesPage .routes-content');
        if (!routesContent) return;

        // 过滤和排序路线
        let filteredRoutes = this.filterRoutes(this.currentRoutes);
        filteredRoutes = this.sortRoutes(filteredRoutes);

        if (filteredRoutes.length === 0) {
            routesContent.innerHTML = this.renderEmptyState();
            return;
        }

        routesContent.innerHTML = `
            <div class="routes-toolbar">
                ${this.renderToolbar()}
            </div>
            <div class="routes-grid">
                ${filteredRoutes.map(route => this.renderRouteCard(route)).join('')}
            </div>
        `;

        // 绑定卡片事件
        this.bindRouteCardEvents();
    }

    /**
     * 渲染工具栏
     */
    renderToolbar() {
        return `
            <div class="toolbar-left">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="搜索路线..." id="routeSearch">
                </div>
                <select id="statusFilter" class="form-select">
                    <option value="all">所有状态</option>
                    <option value="active">已发布</option>
                    <option value="draft">草稿</option>
                    <option value="inactive">已下线</option>
                </select>
            </div>
            <div class="toolbar-right">
                <select id="sortSelect" class="form-select">
                    <option value="sortOrder">排序顺序</option>
                    <option value="title">标题</option>
                    <option value="createdAt">创建时间</option>
                    <option value="updatedAt">更新时间</option>
                    <option value="price.from">价格</option>
                </select>
                <button class="btn btn-outline" id="sortDirection">
                    <i class="fas fa-sort-amount-up"></i>
                </button>
            </div>
        `;
    }

    /**
     * 渲染路线卡片
     */
    renderRouteCard(route) {
        const badgeHtml = route.badge ? 
            `<div class="route-badge ${route.badge.type}" style="background-color: ${route.badge.color}">
                ${route.badge.text}
            </div>` : '';

        const statusClass = route.status === 'active' ? 'status-active' : 
                           route.status === 'draft' ? 'status-draft' : 'status-inactive';

        return `
            <div class="route-card" data-route-id="${route.id}">
                <div class="route-image">
                    <img src="${route.image}" alt="${route.title}" loading="lazy">
                    ${badgeHtml}
                    <div class="route-status ${statusClass}">
                        ${this.getStatusText(route.status)}
                    </div>
                </div>
                <div class="route-content">
                    <h3 class="route-title">${route.title}</h3>
                    <div class="route-highlights">
                        ${route.highlights.map(highlight => `<span>${highlight}</span>`).join(' • ')}
                    </div>
                    <div class="route-details">
                        <span class="route-duration">${route.duration}</span>
                        <div class="route-price">
                            从 <strong>${route.price.currency} ${route.price.from}</strong> 起
                        </div>
                    </div>
                    <div class="route-meta">
                        <span class="route-category">${this.getCategoryText(route.category)}</span>
                        <span class="route-updated">更新于 ${this.formatDate(route.updatedAt)}</span>
                    </div>
                </div>
                <div class="route-actions">
                    <button class="btn btn-sm btn-outline edit-route" data-route-id="${route.id}">
                        <i class="fas fa-edit"></i>
                        编辑
                    </button>
                    <button class="btn btn-sm btn-outline duplicate-route" data-route-id="${route.id}">
                        <i class="fas fa-copy"></i>
                        复制
                    </button>
                    <button class="btn btn-sm btn-danger delete-route" data-route-id="${route.id}">
                        <i class="fas fa-trash"></i>
                        删除
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * 渲染空状态
     */
    renderEmptyState() {
        return `
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-map-marked-alt"></i>
                </div>
                <h3>暂无路线</h3>
                <p>开始创建您的第一条旅游路线吧</p>
                <button class="btn btn-primary" onclick="routeManager.showAddRouteModal()">
                    <i class="fas fa-plus"></i>
                    添加路线
                </button>
            </div>
        `;
    }

    /**
     * 绑定路线卡片事件
     */
    bindRouteCardEvents() {
        // 编辑按钮
        document.querySelectorAll('.edit-route').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const routeId = e.target.closest('[data-route-id]').dataset.routeId;
                this.editRoute(routeId);
            });
        });

        // 复制按钮
        document.querySelectorAll('.duplicate-route').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const routeId = e.target.closest('[data-route-id]').dataset.routeId;
                this.duplicateRoute(routeId);
            });
        });

        // 删除按钮
        document.querySelectorAll('.delete-route').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const routeId = e.target.closest('[data-route-id]').dataset.routeId;
                this.deleteRoute(routeId);
            });
        });

        // 搜索功能
        const searchInput = document.getElementById('routeSearch');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.handleSearch(e.target.value);
            });
        }

        // 状态筛选
        const statusFilter = document.getElementById('statusFilter');
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.filterStatus = e.target.value;
                this.renderRoutesList();
            });
        }

        // 排序
        const sortSelect = document.getElementById('sortSelect');
        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                this.sortBy = e.target.value;
                this.renderRoutesList();
            });
        }

        const sortDirection = document.getElementById('sortDirection');
        if (sortDirection) {
            sortDirection.addEventListener('click', () => {
                this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
                const icon = sortDirection.querySelector('i');
                icon.className = this.sortDirection === 'asc' ? 
                    'fas fa-sort-amount-up' : 'fas fa-sort-amount-down';
                this.renderRoutesList();
            });
        }
    }

    /**
     * 过滤路线
     */
    filterRoutes(routes) {
        let filtered = routes;

        // 状态筛选
        if (this.filterStatus !== 'all') {
            filtered = filtered.filter(route => route.status === this.filterStatus);
        }

        return filtered;
    }

    /**
     * 排序路线
     */
    sortRoutes(routes) {
        return routes.sort((a, b) => {
            let aValue = this.getNestedValue(a, this.sortBy);
            let bValue = this.getNestedValue(b, this.sortBy);

            // 处理数字类型
            if (typeof aValue === 'number' && typeof bValue === 'number') {
                return this.sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
            }

            // 处理字符串类型
            aValue = String(aValue).toLowerCase();
            bValue = String(bValue).toLowerCase();

            if (this.sortDirection === 'asc') {
                return aValue.localeCompare(bValue);
            } else {
                return bValue.localeCompare(aValue);
            }
        });
    }

    /**
     * 获取嵌套对象的值
     */
    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => current && current[key], obj);
    }

    /**
     * 处理搜索
     */
    handleSearch(query) {
        this.searchQuery = query.toLowerCase().trim();
        this.renderRoutesList();
    }

    /**
     * 过滤路线（增强版）
     */
    filterRoutes(routes) {
        let filtered = routes;

        // 状态筛选
        if (this.filterStatus !== 'all') {
            filtered = filtered.filter(route => route.status === this.filterStatus);
        }

        // 搜索筛选
        if (this.searchQuery) {
            filtered = filtered.filter(route => {
                const searchFields = [
                    route.title,
                    route.description,
                    route.category,
                    ...(route.highlights || []),
                    ...(route.seo?.keywords || [])
                ].join(' ').toLowerCase();

                return searchFields.includes(this.searchQuery);
            });
        }

        return filtered;
    }

    /**
     * 批量操作
     */
    async batchOperation(operation, routeIds) {
        try {
            const operations = [];

            switch (operation) {
                case 'delete':
                    const confirmed = await this.showConfirmDialog(
                        '批量删除',
                        `确定要删除选中的 ${routeIds.length} 条路线吗？此操作不可撤销。`,
                        'danger'
                    );
                    if (!confirmed) return;

                    for (const id of routeIds) {
                        operations.push(this.dataManager.deleteItem('routes', id));
                    }
                    break;

                case 'activate':
                    for (const id of routeIds) {
                        operations.push(this.dataManager.updateItem('routes', id, { status: 'active' }));
                    }
                    break;

                case 'deactivate':
                    for (const id of routeIds) {
                        operations.push(this.dataManager.updateItem('routes', id, { status: 'inactive' }));
                    }
                    break;

                default:
                    throw new Error('未知的批量操作');
            }

            await Promise.all(operations);

            this.showNotification(`批量${this.getOperationText(operation)}成功`, 'success');

            // 重新加载路线列表
            await this.loadRoutesPage();

        } catch (error) {
            console.error('批量操作失败:', error);
            this.showNotification('批量操作失败: ' + error.message, 'error');
        }
    }

    /**
     * 获取操作文本
     */
    getOperationText(operation) {
        const operationMap = {
            delete: '删除',
            activate: '发布',
            deactivate: '下线'
        };
        return operationMap[operation] || operation;
    }

    /**
     * 导出路线数据
     */
    async exportRoutes() {
        try {
            const data = await this.dataManager.exportData('routes');
            const blob = new Blob([data], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `routes-export-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            this.showNotification('路线数据导出成功', 'success');
        } catch (error) {
            console.error('导出路线数据失败:', error);
            this.showNotification('导出失败: ' + error.message, 'error');
        }
    }

    /**
     * 显示添加路线模态框
     */
    showAddRouteModal() {
        this.editingRoute = null;
        this.showRouteModal();
    }

    /**
     * 显示路线编辑模态框
     */
    showRouteModal(route = null) {
        const isEdit = route !== null;
        const modalTitle = isEdit ? '编辑路线' : '添加路线';

        const modalHtml = `
            <div class="modal-overlay" id="routeModal">
                <div class="modal" style="max-width: 800px;">
                    <div class="modal-header">
                        <h3 class="modal-title">${modalTitle}</h3>
                        <button class="modal-close" onclick="routeManager.closeRouteModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="routeForm" class="route-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="routeTitle" class="form-label required">路线标题</label>
                                    <input type="text" id="routeTitle" name="title" class="form-control"
                                           placeholder="请输入路线标题" required>
                                </div>
                                <div class="form-group">
                                    <label for="routeCategory" class="form-label required">路线分类</label>
                                    <select id="routeCategory" name="category" class="form-control form-select" required>
                                        <option value="">请选择分类</option>
                                        <option value="international">国际旅游</option>
                                        <option value="domestic">国内旅游</option>
                                        <option value="local">本地游</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="routeDescription" class="form-label required">路线描述</label>
                                <textarea id="routeDescription" name="description" class="form-control form-textarea"
                                          placeholder="请输入路线详细描述" rows="4" required></textarea>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="routeImage" class="form-label required">路线图片URL</label>
                                    <input type="url" id="routeImage" name="image" class="form-control"
                                           placeholder="https://example.com/image.jpg" required>
                                </div>
                                <div class="form-group">
                                    <label for="routeDuration" class="form-label required">行程时长</label>
                                    <input type="text" id="routeDuration" name="duration" class="form-control"
                                           placeholder="例如：7天6夜" required>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="routeHighlights" class="form-label required">路线亮点</label>
                                <input type="text" id="routeHighlights" name="highlights" class="form-control"
                                       placeholder="用逗号分隔，例如：东京,京都,大阪,富士山" required>
                                <small class="form-help">请用逗号分隔多个亮点</small>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="routePrice" class="form-label required">起始价格</label>
                                    <input type="number" id="routePrice" name="price" class="form-control"
                                           placeholder="3299" min="0" step="1" required>
                                </div>
                                <div class="form-group">
                                    <label for="routeCurrency" class="form-label required">货币</label>
                                    <select id="routeCurrency" name="currency" class="form-control form-select" required>
                                        <option value="RM">RM (马来西亚林吉特)</option>
                                        <option value="USD">USD (美元)</option>
                                        <option value="CNY">CNY (人民币)</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="routeStatus" class="form-label required">状态</label>
                                    <select id="routeStatus" name="status" class="form-control form-select" required>
                                        <option value="active">已发布</option>
                                        <option value="draft">草稿</option>
                                        <option value="inactive">已下线</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="routeSortOrder" class="form-label">排序顺序</label>
                                    <input type="number" id="routeSortOrder" name="sortOrder" class="form-control"
                                           placeholder="1" min="1" step="1">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">标签设置</label>
                                <div class="badge-options">
                                    <div class="form-check">
                                        <input type="radio" id="badgeNone" name="badgeType" value="" class="form-check-input">
                                        <label for="badgeNone" class="form-check-label">无标签</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="radio" id="badgeHot" name="badgeType" value="hot" class="form-check-input">
                                        <label for="badgeHot" class="form-check-label">热门</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="radio" id="badgeSpecial" name="badgeType" value="special" class="form-check-input">
                                        <label for="badgeSpecial" class="form-check-label">特价</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="radio" id="badgeRecommended" name="badgeType" value="recommended" class="form-check-input">
                                        <label for="badgeRecommended" class="form-check-label">推荐</label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="routeKeywords" class="form-label">SEO关键词</label>
                                <input type="text" id="routeKeywords" name="keywords" class="form-control"
                                       placeholder="用逗号分隔，例如：日本,樱花,东京,京都">
                                <small class="form-help">用于搜索引擎优化，用逗号分隔</small>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline" onclick="routeManager.closeRouteModal()">
                            取消
                        </button>
                        <button type="submit" form="routeForm" class="btn btn-primary" id="saveRouteBtn">
                            <i class="fas fa-save"></i>
                            ${isEdit ? '更新路线' : '添加路线'}
                        </button>
                    </div>
                </div>
            </div>
        `;

        // 添加模态框到页面
        const modalContainer = document.getElementById('modalContainer');
        modalContainer.innerHTML = modalHtml;

        // 如果是编辑模式，填充表单数据
        if (isEdit && route) {
            this.populateRouteForm(route);
        }

        // 绑定表单事件
        this.bindRouteFormEvents();

        // 显示模态框
        setTimeout(() => {
            document.getElementById('routeModal').classList.add('show');
        }, 100);
    }

    /**
     * 编辑路线
     */
    editRoute(routeId) {
        const route = this.currentRoutes.find(r => r.id === routeId);
        if (!route) {
            this.showNotification('未找到指定路线', 'error');
            return;
        }

        this.editingRoute = route;
        this.showRouteModal(route);
    }

    /**
     * 填充路线表单数据
     */
    populateRouteForm(route) {
        document.getElementById('routeTitle').value = route.title || '';
        document.getElementById('routeCategory').value = route.category || '';
        document.getElementById('routeDescription').value = route.description || '';
        document.getElementById('routeImage').value = route.image || '';
        document.getElementById('routeDuration').value = route.duration || '';
        document.getElementById('routeHighlights').value = route.highlights ? route.highlights.join(', ') : '';
        document.getElementById('routePrice').value = route.price ? route.price.from : '';
        document.getElementById('routeCurrency').value = route.price ? route.price.currency : 'RM';
        document.getElementById('routeStatus').value = route.status || 'draft';
        document.getElementById('routeSortOrder').value = route.sortOrder || '';
        document.getElementById('routeKeywords').value = route.seo && route.seo.keywords ? route.seo.keywords.join(', ') : '';

        // 设置标签类型
        const badgeType = route.badge ? route.badge.type : '';
        const badgeRadio = document.querySelector(`input[name="badgeType"][value="${badgeType}"]`);
        if (badgeRadio) {
            badgeRadio.checked = true;
        } else {
            document.getElementById('badgeNone').checked = true;
        }
    }

    /**
     * 绑定路线表单事件
     */
    bindRouteFormEvents() {
        const form = document.getElementById('routeForm');
        if (!form) return;

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            await this.saveRoute();
        });

        // 图片URL预览
        const imageInput = document.getElementById('routeImage');
        imageInput.addEventListener('blur', () => {
            this.previewRouteImage(imageInput.value);
        });
    }

    /**
     * 预览路线图片
     */
    previewRouteImage(imageUrl) {
        if (!imageUrl) return;

        // 创建预览元素
        let preview = document.getElementById('imagePreview');
        if (!preview) {
            preview = document.createElement('div');
            preview.id = 'imagePreview';
            preview.className = 'image-preview-container';
            document.getElementById('routeImage').parentNode.appendChild(preview);
        }

        preview.innerHTML = `
            <img src="${imageUrl}" alt="路线图片预览" class="image-preview"
                 onerror="this.parentNode.innerHTML='<div class=\\'preview-error\\'>图片加载失败</div>'">
        `;
    }

    /**
     * 保存路线
     */
    async saveRoute() {
        try {
            const formData = this.getRouteFormData();

            // 验证表单数据
            if (!this.validateRouteData(formData)) {
                return;
            }

            // 显示保存状态
            const saveBtn = document.getElementById('saveRouteBtn');
            const originalText = saveBtn.innerHTML;
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 保存中...';
            saveBtn.disabled = true;

            if (this.editingRoute) {
                // 更新现有路线
                await this.dataManager.updateItem('routes', this.editingRoute.id, formData);
                this.showNotification('路线更新成功', 'success');
            } else {
                // 添加新路线
                await this.dataManager.addItem('routes', formData);
                this.showNotification('路线添加成功', 'success');
            }

            // 关闭模态框
            this.closeRouteModal();

            // 重新加载路线列表
            await this.loadRoutesPage();

        } catch (error) {
            console.error('保存路线失败:', error);
            this.showNotification('保存路线失败: ' + error.message, 'error');
        } finally {
            // 恢复按钮状态
            const saveBtn = document.getElementById('saveRouteBtn');
            if (saveBtn) {
                saveBtn.innerHTML = this.editingRoute ?
                    '<i class="fas fa-save"></i> 更新路线' :
                    '<i class="fas fa-save"></i> 添加路线';
                saveBtn.disabled = false;
            }
        }
    }

    /**
     * 获取表单数据
     */
    getRouteFormData() {
        const form = document.getElementById('routeForm');
        const formData = new FormData(form);

        // 处理亮点数组
        const highlights = formData.get('highlights').split(',').map(h => h.trim()).filter(h => h);

        // 处理关键词数组
        const keywords = formData.get('keywords').split(',').map(k => k.trim()).filter(k => k);

        // 处理标签
        const badgeType = formData.get('badgeType');
        let badge = null;
        if (badgeType) {
            const badgeConfig = {
                hot: { type: 'hot', text: '热门', color: '#ef4444' },
                special: { type: 'special', text: '特价', color: '#10b981' },
                recommended: { type: 'recommended', text: '推荐', color: '#3b82f6' }
            };
            badge = badgeConfig[badgeType];
        }

        return {
            title: formData.get('title'),
            category: formData.get('category'),
            description: formData.get('description'),
            image: formData.get('image'),
            duration: formData.get('duration'),
            highlights: highlights,
            price: {
                from: parseInt(formData.get('price')),
                currency: formData.get('currency'),
                priceType: '起价'
            },
            badge: badge,
            status: formData.get('status'),
            sortOrder: parseInt(formData.get('sortOrder')) || 999,
            seo: {
                keywords: keywords,
                metaDescription: formData.get('description').substring(0, 160)
            }
        };
    }

    /**
     * 验证路线数据
     */
    validateRouteData(data) {
        const errors = [];

        if (!data.title || data.title.trim().length < 2) {
            errors.push('路线标题至少需要2个字符');
        }

        if (!data.category) {
            errors.push('请选择路线分类');
        }

        if (!data.description || data.description.trim().length < 10) {
            errors.push('路线描述至少需要10个字符');
        }

        if (!data.image || !this.isValidUrl(data.image)) {
            errors.push('请输入有效的图片URL');
        }

        if (!data.duration) {
            errors.push('请输入行程时长');
        }

        if (!data.highlights || data.highlights.length === 0) {
            errors.push('请至少输入一个路线亮点');
        }

        if (!data.price.from || data.price.from <= 0) {
            errors.push('请输入有效的价格');
        }

        if (errors.length > 0) {
            this.showNotification('表单验证失败：\n' + errors.join('\n'), 'error');
            return false;
        }

        return true;
    }

    /**
     * 验证URL格式
     */
    isValidUrl(string) {
        try {
            new URL(string);
            return true;
        } catch (_) {
            return false;
        }
    }

    /**
     * 关闭路线模态框
     */
    closeRouteModal() {
        const modal = document.getElementById('routeModal');
        if (modal) {
            modal.classList.remove('show');
            setTimeout(() => {
                modal.remove();
            }, 300);
        }
        this.editingRoute = null;
    }

    /**
     * 复制路线
     */
    async duplicateRoute(routeId) {
        try {
            const route = this.currentRoutes.find(r => r.id === routeId);
            if (!route) {
                this.showNotification('未找到指定路线', 'error');
                return;
            }

            // 创建副本数据
            const duplicateData = {
                ...route,
                title: route.title + ' (副本)',
                status: 'draft', // 副本默认为草稿状态
                sortOrder: (route.sortOrder || 0) + 1
            };

            // 移除原有的ID和时间戳
            delete duplicateData.id;
            delete duplicateData.createdAt;
            delete duplicateData.updatedAt;

            // 添加副本
            await this.dataManager.addItem('routes', duplicateData);

            this.showNotification('路线复制成功', 'success');

            // 重新加载路线列表
            await this.loadRoutesPage();

        } catch (error) {
            console.error('复制路线失败:', error);
            this.showNotification('复制路线失败: ' + error.message, 'error');
        }
    }

    /**
     * 删除路线
     */
    async deleteRoute(routeId) {
        try {
            const route = this.currentRoutes.find(r => r.id === routeId);
            if (!route) {
                this.showNotification('未找到指定路线', 'error');
                return;
            }

            // 确认删除
            const confirmed = await this.showConfirmDialog(
                '确认删除',
                `确定要删除路线"${route.title}"吗？此操作不可撤销。`,
                'danger'
            );

            if (!confirmed) return;

            // 删除路线
            await this.dataManager.deleteItem('routes', routeId);

            this.showNotification('路线删除成功', 'success');

            // 重新加载路线列表
            await this.loadRoutesPage();

        } catch (error) {
            console.error('删除路线失败:', error);
            this.showNotification('删除路线失败: ' + error.message, 'error');
        }
    }

    /**
     * 显示确认对话框
     */
    showConfirmDialog(title, message, type = 'warning') {
        return new Promise((resolve) => {
            const modalHtml = `
                <div class="modal-overlay" id="confirmModal">
                    <div class="modal" style="max-width: 400px;">
                        <div class="modal-header">
                            <h3 class="modal-title">${title}</h3>
                        </div>
                        <div class="modal-body">
                            <div class="confirm-content">
                                <div class="confirm-icon ${type}">
                                    <i class="fas ${type === 'danger' ? 'fa-exclamation-triangle' : 'fa-question-circle'}"></i>
                                </div>
                                <p>${message}</p>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline" onclick="routeManager.closeConfirmDialog(false)">
                                取消
                            </button>
                            <button type="button" class="btn btn-${type === 'danger' ? 'danger' : 'primary'}"
                                    onclick="routeManager.closeConfirmDialog(true)">
                                确认
                            </button>
                        </div>
                    </div>
                </div>
            `;

            // 添加模态框到页面
            const modalContainer = document.getElementById('modalContainer');
            modalContainer.innerHTML = modalHtml;

            // 显示模态框
            setTimeout(() => {
                document.getElementById('confirmModal').classList.add('show');
            }, 100);

            // 保存resolve函数
            this.confirmResolve = resolve;
        });
    }

    /**
     * 关闭确认对话框
     */
    closeConfirmDialog(result) {
        const modal = document.getElementById('confirmModal');
        if (modal) {
            modal.classList.remove('show');
            setTimeout(() => {
                modal.remove();
            }, 300);
        }

        if (this.confirmResolve) {
            this.confirmResolve(result);
            this.confirmResolve = null;
        }
    }

    /**
     * 获取状态文本
     */
    getStatusText(status) {
        const statusMap = {
            active: '已发布',
            draft: '草稿',
            inactive: '已下线'
        };
        return statusMap[status] || status;
    }

    /**
     * 获取分类文本
     */
    getCategoryText(category) {
        const categoryMap = {
            international: '国际旅游',
            domestic: '国内旅游',
            local: '本地游'
        };
        return categoryMap[category] || category;
    }

    /**
     * 格式化日期
     */
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        });
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        if (window.cmsCore) {
            window.cmsCore.showNotification(message, type);
        } else {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }
}

// 导出到全局作用域
window.RouteManager = RouteManager;
