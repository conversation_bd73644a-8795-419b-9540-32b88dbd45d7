/**
 * Sky Mirror CMS - 同步管理模块
 * 负责前后台数据同步
 */

class SyncManager {
    constructor(dataManager) {
        this.dataManager = dataManager;
        this.syncQueue = [];
        this.issyncing = false;
        
        this.init();
    }

    /**
     * 初始化同步管理器
     */
    init() {
        console.log('同步管理器初始化');
        
        // 监听数据变更事件
        this.dataManager.on('dataChanged', (data) => {
            this.queueSync(data.type);
        });
    }

    /**
     * 将同步任务加入队列
     * @param {string} type - 数据类型
     */
    queueSync(type) {
        if (!this.syncQueue.includes(type)) {
            this.syncQueue.push(type);
        }
        
        // 如果没有正在同步，立即开始同步
        if (!this.issyncing) {
            this.processQueue();
        }
    }

    /**
     * 处理同步队列
     */
    async processQueue() {
        if (this.syncQueue.length === 0 || this.issyncing) {
            return;
        }

        this.issyncing = true;

        try {
            while (this.syncQueue.length > 0) {
                const type = this.syncQueue.shift();
                await this.syncDataType(type);
            }
        } catch (error) {
            console.error('同步处理失败:', error);
        } finally {
            this.issyncing = false;
        }
    }

    /**
     * 同步特定类型的数据
     * @param {string} type - 数据类型
     */
    async syncDataType(type) {
        try {
            console.log(`开始同步${type}数据`);
            
            // 获取数据
            const data = await this.dataManager.loadData(type);
            
            // 根据数据类型进行不同的同步处理
            switch (type) {
                case 'routes':
                    await this.syncRoutes(data);
                    break;
                case 'packages':
                    await this.syncPackages(data);
                    break;
                case 'images':
                    await this.syncImages(data);
                    break;
                default:
                    console.warn(`未知的数据类型: ${type}`);
            }
            
            console.log(`${type}数据同步完成`);
            
        } catch (error) {
            console.error(`同步${type}数据失败:`, error);
            throw error;
        }
    }

    /**
     * 同步路线数据到前台
     * @param {Object} routesData - 路线数据
     */
    async syncRoutes(routesData) {
        try {
            // 生成前台可用的路线数据
            const frontendData = this.transformRoutesForFrontend(routesData);
            
            // 这里应该将数据写入到前台可访问的位置
            // 由于浏览器安全限制，实际实现可能需要服务器端支持
            console.log('路线数据已准备同步到前台:', frontendData);
            
            // 模拟同步延迟
            await new Promise(resolve => setTimeout(resolve, 500));
            
        } catch (error) {
            console.error('同步路线数据失败:', error);
            throw error;
        }
    }

    /**
     * 同步套餐数据到前台
     * @param {Object} packagesData - 套餐数据
     */
    async syncPackages(packagesData) {
        try {
            // 生成前台可用的套餐数据
            const frontendData = this.transformPackagesForFrontend(packagesData);
            
            console.log('套餐数据已准备同步到前台:', frontendData);
            
            // 模拟同步延迟
            await new Promise(resolve => setTimeout(resolve, 500));
            
        } catch (error) {
            console.error('同步套餐数据失败:', error);
            throw error;
        }
    }

    /**
     * 同步图片数据到前台
     * @param {Object} imagesData - 图片数据
     */
    async syncImages(imagesData) {
        try {
            // 生成前台可用的图片数据
            const frontendData = this.transformImagesForFrontend(imagesData);
            
            console.log('图片数据已准备同步到前台:', frontendData);
            
            // 模拟同步延迟
            await new Promise(resolve => setTimeout(resolve, 500));
            
        } catch (error) {
            console.error('同步图片数据失败:', error);
            throw error;
        }
    }

    /**
     * 转换路线数据为前台格式
     * @param {Object} routesData - 后台路线数据
     * @returns {Object} 前台格式的路线数据
     */
    transformRoutesForFrontend(routesData) {
        const activeRoutes = routesData.routes.filter(route => route.status === 'active');
        
        return {
            version: routesData.version,
            lastUpdated: routesData.lastUpdated,
            routes: activeRoutes.map(route => ({
                id: route.id,
                title: route.title,
                description: route.description,
                image: route.image,
                highlights: route.highlights,
                duration: route.duration,
                price: route.price,
                badge: route.badge,
                category: route.category,
                sortOrder: route.sortOrder
            }))
        };
    }

    /**
     * 转换套餐数据为前台格式
     * @param {Object} packagesData - 后台套餐数据
     * @returns {Object} 前台格式的套餐数据
     */
    transformPackagesForFrontend(packagesData) {
        const activePackages = packagesData.packages.filter(pkg => pkg.status === 'active');
        
        return {
            version: packagesData.version,
            lastUpdated: packagesData.lastUpdated,
            packages: activePackages.map(pkg => ({
                id: pkg.id,
                title: pkg.title,
                type: pkg.type,
                description: pkg.description,
                duration: pkg.duration,
                bestTime: pkg.bestTime,
                location: pkg.location,
                includes: pkg.includes,
                excludes: pkg.excludes,
                tips: pkg.tips,
                timeSlots: pkg.timeSlots,
                pricing: pkg.pricing,
                featured: pkg.featured
            }))
        };
    }

    /**
     * 转换图片数据为前台格式
     * @param {Object} imagesData - 后台图片数据
     * @returns {Object} 前台格式的图片数据
     */
    transformImagesForFrontend(imagesData) {
        const activeImages = imagesData.images.filter(img => img.status === 'active');
        
        return {
            version: imagesData.version,
            lastUpdated: imagesData.lastUpdated,
            images: activeImages.map(img => ({
                id: img.id,
                url: img.url,
                alt: img.alt,
                title: img.title,
                category: img.category,
                tags: img.tags,
                featured: img.featured
            })),
            categories: imagesData.categories
        };
    }

    /**
     * 手动触发全量同步
     */
    async syncAll() {
        try {
            console.log('开始全量同步');
            
            // 清空队列
            this.syncQueue = [];
            
            // 添加所有数据类型到队列
            this.queueSync('routes');
            this.queueSync('packages');
            this.queueSync('images');
            
            // 处理队列
            await this.processQueue();
            
            console.log('全量同步完成');
            
            // 触发同步完成事件
            this.emit('syncCompleted');
            
        } catch (error) {
            console.error('全量同步失败:', error);
            this.emit('syncFailed', error);
            throw error;
        }
    }

    /**
     * 获取同步状态
     * @returns {Object} 同步状态信息
     */
    getSyncStatus() {
        return {
            issyncing: this.issyncing,
            queueLength: this.syncQueue.length,
            pendingTypes: [...this.syncQueue]
        };
    }

    /**
     * 事件发射器（简单实现）
     */
    emit(event, data) {
        // 这里可以实现更完整的事件系统
        console.log(`同步事件: ${event}`, data);
    }

    /**
     * 生成前台数据文件（模拟）
     * 实际项目中可能需要服务器端支持
     */
    async generateFrontendFiles() {
        try {
            console.log('开始生成前台数据文件');

            // 获取所有数据
            const [routes, packages, images] = await Promise.all([
                this.dataManager.loadData('routes'),
                this.dataManager.loadData('packages'),
                this.dataManager.loadData('images')
            ]);

            // 转换为前台格式
            const frontendRoutes = this.transformRoutesForFrontend(routes);
            const frontendPackages = this.transformPackagesForFrontend(packages);
            const frontendImages = this.transformImagesForFrontend(images);

            // 生成文件内容
            const files = {
                'admin/data/routes.json': JSON.stringify(frontendRoutes, null, 2),
                'admin/data/packages.json': JSON.stringify(frontendPackages, null, 2),
                'admin/data/images.json': JSON.stringify(frontendImages, null, 2)
            };

            // 由于浏览器安全限制，我们将数据存储在localStorage中
            // 前台的数据加载器会优先从localStorage读取
            localStorage.setItem('frontend_routes', JSON.stringify(frontendRoutes));
            localStorage.setItem('frontend_packages', JSON.stringify(frontendPackages));
            localStorage.setItem('frontend_images', JSON.stringify(frontendImages));
            localStorage.setItem('frontend_last_sync', new Date().toISOString());

            console.log('前台数据已同步到localStorage');

            return files;

        } catch (error) {
            console.error('生成前台数据文件失败:', error);
            throw error;
        }
    }

    /**
     * 发布数据到前台
     */
    async publishToFrontend() {
        try {
            console.log('开始发布数据到前台');

            // 生成前台数据文件
            await this.generateFrontendFiles();

            // 触发前台数据刷新
            this.notifyFrontendUpdate();

            console.log('数据发布完成');

        } catch (error) {
            console.error('发布数据失败:', error);
            throw error;
        }
    }

    /**
     * 通知前台更新数据
     */
    notifyFrontendUpdate() {
        // 设置更新标记
        localStorage.setItem('frontend_data_updated', 'true');

        // 如果前台页面在同一个浏览器中打开，可以通过storage事件通知
        window.dispatchEvent(new StorageEvent('storage', {
            key: 'frontend_data_updated',
            newValue: 'true'
        }));
    }
}

// 导出到全局作用域
window.SyncManager = SyncManager;
