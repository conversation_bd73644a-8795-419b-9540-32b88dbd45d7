/**
 * Sky Mirror CMS - 数据验证器
 * 提供统一的数据验证功能
 */

class CMSValidator {
    constructor() {
        this.rules = new Map();
        this.messages = new Map();
        
        this.initializeDefaultRules();
        this.initializeDefaultMessages();
    }

    /**
     * 初始化默认验证规则
     */
    initializeDefaultRules() {
        // 基础验证规则
        this.rules.set('required', (value) => {
            return value !== null && value !== undefined && String(value).trim() !== '';
        });

        this.rules.set('email', (value) => {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(value);
        });

        this.rules.set('url', (value) => {
            try {
                new URL(value);
                return true;
            } catch {
                return false;
            }
        });

        this.rules.set('number', (value) => {
            return !isNaN(value) && isFinite(value);
        });

        this.rules.set('integer', (value) => {
            return Number.isInteger(Number(value));
        });

        this.rules.set('positive', (value) => {
            return Number(value) > 0;
        });

        this.rules.set('minLength', (value, min) => {
            return String(value).length >= min;
        });

        this.rules.set('maxLength', (value, max) => {
            return String(value).length <= max;
        });

        this.rules.set('min', (value, min) => {
            return Number(value) >= min;
        });

        this.rules.set('max', (value, max) => {
            return Number(value) <= max;
        });

        this.rules.set('pattern', (value, pattern) => {
            const regex = new RegExp(pattern);
            return regex.test(value);
        });

        this.rules.set('in', (value, options) => {
            return options.includes(value);
        });

        // 自定义业务规则
        this.rules.set('imageUrl', (value) => {
            if (!this.rules.get('url')(value)) return false;
            const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'];
            const url = value.toLowerCase();
            return imageExtensions.some(ext => url.includes(ext)) || url.includes('images.pexels.com');
        });

        this.rules.set('slug', (value) => {
            const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
            return slugRegex.test(value);
        });

        this.rules.set('currency', (value) => {
            const currencies = ['RM', 'USD', 'CNY', 'EUR', 'SGD'];
            return currencies.includes(value);
        });

        this.rules.set('status', (value) => {
            const statuses = ['active', 'inactive', 'draft'];
            return statuses.includes(value);
        });

        this.rules.set('category', (value) => {
            const categories = ['international', 'domestic', 'local'];
            return categories.includes(value);
        });

        this.rules.set('packageType', (value) => {
            const types = ['signature-tickets', 'transport', 'accommodation', 'tour'];
            return types.includes(value);
        });

        this.rules.set('imageCategory', (value) => {
            const categories = ['routes', 'packages', 'hero', 'gallery'];
            return categories.includes(value);
        });
    }

    /**
     * 初始化默认错误消息
     */
    initializeDefaultMessages() {
        this.messages.set('required', '此字段为必填项');
        this.messages.set('email', '请输入有效的邮箱地址');
        this.messages.set('url', '请输入有效的URL地址');
        this.messages.set('number', '请输入有效的数字');
        this.messages.set('integer', '请输入整数');
        this.messages.set('positive', '请输入正数');
        this.messages.set('minLength', '长度不能少于 {min} 个字符');
        this.messages.set('maxLength', '长度不能超过 {max} 个字符');
        this.messages.set('min', '值不能小于 {min}');
        this.messages.set('max', '值不能大于 {max}');
        this.messages.set('pattern', '格式不正确');
        this.messages.set('in', '请选择有效的选项');
        this.messages.set('imageUrl', '请输入有效的图片URL');
        this.messages.set('slug', '只能包含小写字母、数字和连字符');
        this.messages.set('currency', '请选择有效的货币类型');
        this.messages.set('status', '请选择有效的状态');
        this.messages.set('category', '请选择有效的分类');
        this.messages.set('packageType', '请选择有效的套餐类型');
        this.messages.set('imageCategory', '请选择有效的图片分类');
    }

    /**
     * 验证单个字段
     * @param {*} value - 要验证的值
     * @param {Array} rules - 验证规则数组
     * @param {string} fieldName - 字段名称
     * @returns {Object} 验证结果
     */
    validateField(value, rules, fieldName = '') {
        const errors = [];

        for (const rule of rules) {
            let ruleName, ruleParams;

            if (typeof rule === 'string') {
                ruleName = rule;
                ruleParams = [];
            } else if (Array.isArray(rule)) {
                [ruleName, ...ruleParams] = rule;
            } else if (typeof rule === 'object') {
                ruleName = rule.name;
                ruleParams = rule.params || [];
            }

            const validator = this.rules.get(ruleName);
            if (!validator) {
                console.warn(`未知的验证规则: ${ruleName}`);
                continue;
            }

            if (!validator(value, ...ruleParams)) {
                const message = this.getErrorMessage(ruleName, ruleParams, fieldName);
                errors.push(message);
            }
        }

        return {
            valid: errors.length === 0,
            errors: errors
        };
    }

    /**
     * 验证对象
     * @param {Object} data - 要验证的数据对象
     * @param {Object} schema - 验证模式
     * @returns {Object} 验证结果
     */
    validateObject(data, schema) {
        const errors = {};
        let isValid = true;

        for (const [fieldName, rules] of Object.entries(schema)) {
            const value = this.getNestedValue(data, fieldName);
            const result = this.validateField(value, rules, fieldName);

            if (!result.valid) {
                errors[fieldName] = result.errors;
                isValid = false;
            }
        }

        return {
            valid: isValid,
            errors: errors
        };
    }

    /**
     * 获取错误消息
     * @param {string} ruleName - 规则名称
     * @param {Array} params - 规则参数
     * @param {string} fieldName - 字段名称
     * @returns {string} 错误消息
     */
    getErrorMessage(ruleName, params, fieldName) {
        let message = this.messages.get(ruleName) || '验证失败';

        // 替换参数占位符
        params.forEach((param, index) => {
            const placeholder = `{${index === 0 ? ruleName.replace(/[A-Z]/g, letter => letter.toLowerCase()) : index}}`;
            message = message.replace(placeholder, param);
        });

        // 替换通用占位符
        message = message.replace('{min}', params[0]);
        message = message.replace('{max}', params[0]);

        return fieldName ? `${fieldName}: ${message}` : message;
    }

    /**
     * 获取嵌套对象的值
     * @param {Object} obj - 对象
     * @param {string} path - 路径
     * @returns {*} 值
     */
    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => current && current[key], obj);
    }

    /**
     * 路线数据验证模式
     */
    getRouteSchema() {
        return {
            title: ['required', ['minLength', 2], ['maxLength', 100]],
            description: ['required', ['minLength', 10], ['maxLength', 500]],
            image: ['required', 'imageUrl'],
            duration: ['required', ['minLength', 2]],
            'price.from': ['required', 'number', 'positive'],
            'price.currency': ['required', 'currency'],
            category: ['required', 'category'],
            status: ['required', 'status'],
            highlights: ['required']
        };
    }

    /**
     * 套餐数据验证模式
     */
    getPackageSchema() {
        return {
            title: ['required', ['minLength', 2], ['maxLength', 100]],
            type: ['required', 'packageType'],
            description: ['required', ['minLength', 10], ['maxLength', 1000]],
            duration: ['required', ['minLength', 2]],
            location: ['required', ['minLength', 2]],
            'pricing.adult': ['required', 'number', 'positive'],
            'pricing.child': ['required', 'number', 'positive'],
            'pricing.currency': ['required', 'currency'],
            status: ['required', 'status'],
            includes: ['required']
        };
    }

    /**
     * 图片数据验证模式
     */
    getImageSchema() {
        return {
            title: ['required', ['minLength', 2], ['maxLength', 100]],
            url: ['required', 'imageUrl'],
            alt: ['required', ['minLength', 2], ['maxLength', 200]],
            category: ['required', 'imageCategory'],
            status: ['required', 'status']
        };
    }

    /**
     * 添加自定义验证规则
     * @param {string} name - 规则名称
     * @param {Function} validator - 验证函数
     * @param {string} message - 错误消息
     */
    addRule(name, validator, message) {
        this.rules.set(name, validator);
        this.messages.set(name, message);
    }

    /**
     * 添加自定义错误消息
     * @param {string} ruleName - 规则名称
     * @param {string} message - 错误消息
     */
    addMessage(ruleName, message) {
        this.messages.set(ruleName, message);
    }
}

// 创建全局验证器实例
window.cmsValidator = new CMSValidator();

// 导出类
window.CMSValidator = CMSValidator;
