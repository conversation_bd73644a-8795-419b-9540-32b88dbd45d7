<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sky Mirror CMS - 登录</title>
    
    <!-- CSS样式 -->
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="css/components.css">
    
    <!-- 图标字体 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        /* 登录页面专用样式 */
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 2rem;
        }
        
        .login-card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            padding: 3rem;
            width: 100%;
            max-width: 400px;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .login-logo {
            width: 4rem;
            height: 4rem;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            border-radius: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 2rem;
        }
        
        .login-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }
        
        .login-subtitle {
            color: #6b7280;
            font-size: 0.875rem;
        }
        
        .login-form {
            space-y: 1.5rem;
        }
        
        .login-form .form-group {
            margin-bottom: 1.5rem;
        }
        
        .login-form .form-control {
            padding: 0.875rem 1rem;
            font-size: 1rem;
        }
        
        .login-btn {
            width: 100%;
            padding: 0.875rem;
            font-size: 1rem;
            font-weight: 600;
        }
        
        .login-footer {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #e5e7eb;
            color: #6b7280;
            font-size: 0.875rem;
        }
        
        .error-message {
            background-color: #fef2f2;
            border: 1px solid #fecaca;
            color: #dc2626;
            padding: 0.75rem;
            border-radius: 0.375rem;
            margin-bottom: 1rem;
            font-size: 0.875rem;
            display: none;
        }
        
        .error-message.show {
            display: block;
        }
        
        .loading-btn {
            position: relative;
            pointer-events: none;
        }
        
        .loading-btn .btn-text {
            opacity: 0;
        }
        
        .loading-btn::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 1rem;
            height: 1rem;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="login-logo">
                    <i class="fas fa-water"></i>
                </div>
                <h1 class="login-title">Sky Mirror CMS</h1>
                <p class="login-subtitle">请登录以访问管理后台</p>
            </div>
            
            <div class="error-message" id="errorMessage">
                <i class="fas fa-exclamation-circle"></i>
                <span id="errorText">用户名或密码错误</span>
            </div>
            
            <form class="login-form" id="loginForm">
                <div class="form-group">
                    <label for="username" class="form-label">用户名</label>
                    <input 
                        type="text" 
                        id="username" 
                        name="username" 
                        class="form-control" 
                        placeholder="请输入用户名"
                        required
                        autocomplete="username"
                    >
                </div>
                
                <div class="form-group">
                    <label for="password" class="form-label">密码</label>
                    <input 
                        type="password" 
                        id="password" 
                        name="password" 
                        class="form-control" 
                        placeholder="请输入密码"
                        required
                        autocomplete="current-password"
                    >
                </div>
                
                <div class="form-check">
                    <input type="checkbox" id="remember" name="remember" class="form-check-input">
                    <label for="remember" class="form-check-label">记住我</label>
                </div>
                
                <button type="submit" class="btn btn-primary login-btn" id="loginBtn">
                    <span class="btn-text">
                        <i class="fas fa-sign-in-alt"></i>
                        登录
                    </span>
                </button>
            </form>
            
            <div class="login-footer">
                <p>&copy; 2024 Sky Mirror World Tour. 保留所有权利。</p>
            </div>
        </div>
    </div>

    <script>
        /**
         * 登录页面功能
         */
        class LoginPage {
            constructor() {
                this.form = document.getElementById('loginForm');
                this.usernameInput = document.getElementById('username');
                this.passwordInput = document.getElementById('password');
                this.rememberCheckbox = document.getElementById('remember');
                this.loginBtn = document.getElementById('loginBtn');
                this.errorMessage = document.getElementById('errorMessage');
                this.errorText = document.getElementById('errorText');
                
                this.init();
            }
            
            init() {
                // 检查是否已经登录
                this.checkLoginStatus();
                
                // 绑定事件
                this.form.addEventListener('submit', (e) => this.handleLogin(e));
                
                // 回车键登录
                this.passwordInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.handleLogin(e);
                    }
                });
                
                // 隐藏错误信息
                this.usernameInput.addEventListener('input', () => this.hideError());
                this.passwordInput.addEventListener('input', () => this.hideError());
                
                // 设置焦点
                this.usernameInput.focus();
            }
            
            /**
             * 检查登录状态
             */
            checkLoginStatus() {
                const isLoggedIn = localStorage.getItem('cms_logged_in');
                if (isLoggedIn === 'true') {
                    // 已登录，跳转到管理后台
                    window.location.href = 'index.html';
                }
            }
            
            /**
             * 处理登录
             */
            async handleLogin(e) {
                e.preventDefault();
                
                const username = this.usernameInput.value.trim();
                const password = this.passwordInput.value.trim();
                
                // 基本验证
                if (!username || !password) {
                    this.showError('请输入用户名和密码');
                    return;
                }
                
                // 显示加载状态
                this.setLoading(true);
                
                try {
                    // 模拟登录验证（实际项目中应该调用API）
                    const isValid = await this.validateCredentials(username, password);
                    
                    if (isValid) {
                        // 登录成功
                        this.handleLoginSuccess();
                    } else {
                        // 登录失败
                        this.showError('用户名或密码错误');
                    }
                } catch (error) {
                    console.error('登录失败:', error);
                    this.showError('登录过程中发生错误，请稍后重试');
                } finally {
                    this.setLoading(false);
                }
            }
            
            /**
             * 验证登录凭据
             */
            async validateCredentials(username, password) {
                // 模拟API调用延迟
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // 简单的用户名密码验证（实际项目中应该使用更安全的方式）
                const validCredentials = [
                    { username: 'admin', password: 'admin123' },
                    { username: 'skymirror', password: 'cms2024' }
                ];
                
                return validCredentials.some(cred => 
                    cred.username === username && cred.password === password
                );
            }
            
            /**
             * 处理登录成功
             */
            handleLoginSuccess() {
                // 保存登录状态
                localStorage.setItem('cms_logged_in', 'true');
                localStorage.setItem('cms_login_time', new Date().toISOString());
                
                // 如果选择了记住我，保存用户名
                if (this.rememberCheckbox.checked) {
                    localStorage.setItem('cms_remember_username', this.usernameInput.value);
                } else {
                    localStorage.removeItem('cms_remember_username');
                }
                
                // 记录登录日志
                this.logLoginActivity();
                
                // 跳转到管理后台
                window.location.href = 'index.html';
            }
            
            /**
             * 记录登录活动
             */
            logLoginActivity() {
                try {
                    let activities = JSON.parse(localStorage.getItem('cms_activities') || '[]');
                    
                    const activity = {
                        id: Date.now().toString(36) + Math.random().toString(36).substr(2),
                        message: '管理员登录系统',
                        timestamp: new Date().toISOString()
                    };
                    
                    activities.unshift(activity);
                    
                    // 只保留最近50条记录
                    if (activities.length > 50) {
                        activities = activities.slice(0, 50);
                    }
                    
                    localStorage.setItem('cms_activities', JSON.stringify(activities));
                } catch (error) {
                    console.error('记录登录活动失败:', error);
                }
            }
            
            /**
             * 显示错误信息
             */
            showError(message) {
                this.errorText.textContent = message;
                this.errorMessage.classList.add('show');
                
                // 3秒后自动隐藏
                setTimeout(() => {
                    this.hideError();
                }, 3000);
            }
            
            /**
             * 隐藏错误信息
             */
            hideError() {
                this.errorMessage.classList.remove('show');
            }
            
            /**
             * 设置加载状态
             */
            setLoading(loading) {
                if (loading) {
                    this.loginBtn.classList.add('loading-btn');
                    this.loginBtn.disabled = true;
                } else {
                    this.loginBtn.classList.remove('loading-btn');
                    this.loginBtn.disabled = false;
                }
            }
        }
        
        // 初始化登录页面
        document.addEventListener('DOMContentLoaded', () => {
            new LoginPage();
        });
    </script>
</body>
</html>
