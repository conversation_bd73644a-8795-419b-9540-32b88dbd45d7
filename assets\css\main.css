/* Sky Mirror World Tour - 主样式文件 */

/* CSS 变量定义 */
:root {
  /* 颜色系统 */
  --color-blue-50: #eff6ff;
  --color-blue-100: #dbeafe;
  --color-blue-600: #2563eb;
  --color-blue-700: #1d4ed8;
  --color-emerald-500: #10b981;
  --color-emerald-600: #059669;
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
  --color-white: #ffffff;
  --color-red-50: #fef2f2;
  --color-red-500: #ef4444;
  --color-red-600: #dc2626;
  --color-yellow-50: #fffbeb;
  --color-yellow-500: #f59e0b;
  --color-amber-500: #f59e0b;
  --color-amber-600: #d97706;
  --color-orange-600: #ea580c;
  
  /* 渐变 */
  --gradient-primary: linear-gradient(to right, var(--color-blue-600), var(--color-emerald-600));
  --gradient-secondary: linear-gradient(to right, var(--color-amber-600), var(--color-orange-600));
  
  /* 间距 */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;
  
  /* 字体大小 */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  --text-6xl: 3.75rem;
  --text-7xl: 4.5rem;
  
  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* 边框圆角 */
  --radius-sm: 0.125rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-full: 9999px;
  
  /* 过渡 */
  --transition-all: all 0.3s ease;
  --transition-colors: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
  
  /* 断点 */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
}

/* 基础重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: var(--color-gray-800);
  background-color: var(--color-white);
}

/* 容器 */
.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

@media (min-width: 640px) {
  .container {
    padding: 0 var(--spacing-6);
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 var(--spacing-8);
  }
}

/* 网格系统 */
.grid {
  display: grid;
  gap: var(--spacing-4);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

@media (min-width: 640px) {
  .sm\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .sm\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
}

@media (min-width: 768px) {
  .md\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .md\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
  .md\:gap-6 { gap: var(--spacing-6); }
  .md\:gap-8 { gap: var(--spacing-8); }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
}

@media (min-width: 1280px) {
  .xl\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .xl\:col-span-2 { grid-column: span 2 / span 2; }
}

/* 服务网格 */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-6);
}

/* Flexbox */
.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.items-start {
  align-items: flex-start;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.space-x-2 > * + * {
  margin-left: var(--spacing-2);
}

.space-x-3 > * + * {
  margin-left: var(--spacing-3);
}

.space-x-4 > * + * {
  margin-left: var(--spacing-4);
}

.space-y-2 > * + * {
  margin-top: var(--spacing-2);
}

.space-y-4 > * + * {
  margin-top: var(--spacing-4);
}

.space-y-6 > * + * {
  margin-top: var(--spacing-6);
}

/* 文本样式 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-xs { font-size: var(--text-xs); }
.text-sm { font-size: var(--text-sm); }
.text-base { font-size: var(--text-base); }
.text-lg { font-size: var(--text-lg); }
.text-xl { font-size: var(--text-xl); }
.text-2xl { font-size: var(--text-2xl); }
.text-3xl { font-size: var(--text-3xl); }
.text-4xl { font-size: var(--text-4xl); }
.text-5xl { font-size: var(--text-5xl); }

@media (min-width: 768px) {
  .md\:text-xl { font-size: var(--text-xl); }
  .md\:text-2xl { font-size: var(--text-2xl); }
  .md\:text-3xl { font-size: var(--text-3xl); }
  .md\:text-4xl { font-size: var(--text-4xl); }
  .md\:text-5xl { font-size: var(--text-5xl); }
  .md\:text-6xl { font-size: var(--text-6xl); }
}

@media (min-width: 1024px) {
  .lg\:text-2xl { font-size: var(--text-2xl); }
  .lg\:text-4xl { font-size: var(--text-4xl); }
  .lg\:text-5xl { font-size: var(--text-5xl); }
  .lg\:text-7xl { font-size: var(--text-7xl); }
}

.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

/* 颜色 */
.text-white { color: var(--color-white); }
.text-gray-600 { color: var(--color-gray-600); }
.text-gray-700 { color: var(--color-gray-700); }
.text-gray-800 { color: var(--color-gray-800); }
.text-gray-900 { color: var(--color-gray-900); }
.text-blue-600 { color: var(--color-blue-600); }
.text-blue-700 { color: var(--color-blue-700); }

.bg-white { background-color: var(--color-white); }
.bg-gray-50 { background-color: var(--color-gray-50); }
.bg-gray-100 { background-color: var(--color-gray-100); }
.bg-blue-50 { background-color: var(--color-blue-50); }
.bg-blue-600 { background-color: var(--color-blue-600); }

/* 间距 */
.p-3 { padding: var(--spacing-3); }
.p-4 { padding: var(--spacing-4); }
.p-5 { padding: var(--spacing-5); }
.p-6 { padding: var(--spacing-6); }
.p-8 { padding: var(--spacing-8); }

.px-3 { padding-left: var(--spacing-3); padding-right: var(--spacing-3); }
.px-4 { padding-left: var(--spacing-4); padding-right: var(--spacing-4); }
.px-6 { padding-left: var(--spacing-6); padding-right: var(--spacing-6); }
.px-8 { padding-left: var(--spacing-8); padding-right: var(--spacing-8); }

.py-2 { padding-top: var(--spacing-2); padding-bottom: var(--spacing-2); }
.py-3 { padding-top: var(--spacing-3); padding-bottom: var(--spacing-3); }
.py-12 { padding-top: var(--spacing-12); padding-bottom: var(--spacing-12); }
.py-16 { padding-top: var(--spacing-16); padding-bottom: var(--spacing-16); }
.py-20 { padding-top: var(--spacing-20); padding-bottom: var(--spacing-20); }

@media (min-width: 768px) {
  .md\:p-6 { padding: var(--spacing-6); }
  .md\:py-16 { padding-top: var(--spacing-16); padding-bottom: var(--spacing-16); }
}

.mb-3 { margin-bottom: var(--spacing-3); }
.mb-4 { margin-bottom: var(--spacing-4); }
.mb-6 { margin-bottom: var(--spacing-6); }
.mb-8 { margin-bottom: var(--spacing-8); }
.mb-10 { margin-bottom: var(--spacing-10); }
.mb-12 { margin-bottom: var(--spacing-12); }
.mb-16 { margin-bottom: var(--spacing-16); }

@media (min-width: 768px) {
  .md\:mb-4 { margin-bottom: var(--spacing-4); }
  .md\:mb-6 { margin-bottom: var(--spacing-6); }
  .md\:mb-8 { margin-bottom: var(--spacing-8); }
  .md\:mb-10 { margin-bottom: var(--spacing-10); }
  .md\:mb-12 { margin-bottom: var(--spacing-12); }
}

/* 边框和圆角 */
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: var(--radius-full); }

.border { border: 1px solid var(--color-gray-300); }
.border-gray-200 { border-color: var(--color-gray-200); }

/* 阴影 */
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

/* 过渡效果 */
.transition-all { transition: var(--transition-all); }
.transition-colors { transition: var(--transition-colors); }

/* 显示/隐藏 */
.hidden { display: none; }
.block { display: block; }

@media (min-width: 768px) {
  .md\:hidden { display: none; }
  .md\:block { display: block; }
  .md\:flex { display: flex; }
}

/* 位置 */
.relative { position: relative; }
.absolute { position: absolute; }
.sticky { position: sticky; }
.fixed { position: fixed; }

.top-0 { top: 0; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }

.z-10 { z-index: 10; }
.z-50 { z-index: 50; }

/* 宽高 */
.w-full { width: 100%; }
.h-full { height: 100%; }
.h-4 { height: var(--spacing-4); }
.h-10 { height: 2.5rem; }
.h-12 { height: 3rem; }
.h-14 { height: 3.5rem; }
.h-16 { height: 4rem; }
.h-48 { height: 12rem; }
.h-96 { height: 24rem; }
.h-screen { height: 100vh; }

.min-h-screen { min-height: 100vh; }

.max-w-2xl { max-width: 42rem; }
.max-w-3xl { max-width: 48rem; }
.max-w-4xl { max-width: 56rem; }
.max-w-5xl { max-width: 64rem; }
.max-w-6xl { max-width: 72rem; }
.max-w-7xl { max-width: 80rem; }
.max-w-8xl { max-width: 88rem; }

.mx-auto { margin-left: auto; margin-right: auto; }

/* 溢出 */
.overflow-hidden { overflow: hidden; }

/* 对象适配 */
.object-cover { object-fit: cover; }

/* 透明度 */
.opacity-90 { opacity: 0.9; }

/* 变换 */
.transform { transform: translateZ(0); }
.scale-105 { transform: scale(1.05); }
.scale-110 { transform: scale(1.1); }
.-translate-x-1\/2 { transform: translateX(-50%); }
.translate-y-\[-2px\] { transform: translateY(-2px); }
.translate-y-\[-5px\] { transform: translateY(-5px); }

/* 悬停效果 */
.hover\:bg-gray-50:hover { background-color: var(--color-gray-50); }
.hover\:bg-gray-100:hover { background-color: var(--color-gray-100); }
.hover\:bg-gray-200:hover { background-color: var(--color-gray-200); }
.hover\:bg-blue-700:hover { background-color: var(--color-blue-700); }
.hover\:text-blue-600:hover { color: var(--color-blue-600); }
.hover\:text-blue-700:hover { color: var(--color-blue-700); }
.hover\:shadow-xl:hover { box-shadow: var(--shadow-xl); }
.hover\:scale-105:hover { transform: scale(1.05); }
.hover\:scale-110:hover { transform: scale(1.1); }

/* 焦点效果 */
.focus\:outline-none:focus { outline: none; }
.focus\:ring-2:focus { box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1); }
.focus\:border-transparent:focus { border-color: transparent; }

/* 行高 */
.leading-tight { line-height: 1.25; }
.leading-relaxed { line-height: 1.625; }

/* 文本装饰 */
.no-underline { text-decoration: none; }

/* 光标 */
.cursor-pointer { cursor: pointer; }

/* 用户选择 */
.select-none { user-select: none; }

/* 背景 */
.bg-cover { background-size: cover; }
.bg-center { background-position: center; }

/* 渐变背景 */
.bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)); }

/* 文本渐变 */
.text-transparent { color: transparent; }
.bg-clip-text { background-clip: text; -webkit-background-clip: text; }

/* 纵横比 */
.aspect-square { aspect-ratio: 1 / 1; }
.aspect-video { aspect-ratio: 16 / 9; }

/* 弹性 */
.flex-shrink-0 { flex-shrink: 0; }
.flex-grow { flex-grow: 1; }

/* 间隙 */
.gap-2 { gap: var(--spacing-2); }
.gap-3 { gap: var(--spacing-3); }
.gap-4 { gap: var(--spacing-4); }
.gap-6 { gap: var(--spacing-6); }
.gap-8 { gap: var(--spacing-8); }

/* 当红路线样式 */
.routes-carousel-container {
    position: relative;
    overflow: hidden;
}

.routes-carousel {
    display: flex;
    gap: 2rem;
    overflow-x: auto;
    scroll-behavior: smooth;
    padding-bottom: 1rem;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.routes-carousel::-webkit-scrollbar {
    display: none;
}

.route-card {
    flex: 0 0 auto;
    width: 320px;
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.route-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
}

.route-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.route-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.route-card:hover .route-image img {
    transform: scale(1.05);
}

.route-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    color: white;
}

.route-badge.hot {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.route-badge.special {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.route-badge.recommended {
    background: linear-gradient(135deg, #10b981, #059669);
}

.route-content {
    padding: 1.5rem;
}

.route-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.75rem;
}

.route-highlights {
    color: #6b7280;
    font-size: 0.875rem;
    margin-bottom: 1rem;
}

.route-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.route-days {
    color: #6b7280;
    font-size: 0.875rem;
}

.route-price {
    font-size: 1.125rem;
    font-weight: 600;
    color: #3b82f6;
}

.route-book-btn {
    width: 100%;
}

.carousel-controls {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
}

.carousel-btn {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    background: white;
    border: 2px solid #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #6b7280;
}

.carousel-btn:hover {
    background: #3b82f6;
    border-color: #3b82f6;
    color: white;
}

@media (max-width: 768px) {
    .routes-carousel-container {
        overflow: visible;
    }

    .routes-carousel {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        overflow: visible;
        padding-bottom: 0;
    }

    .route-card {
        width: auto;
        flex: none;
    }

    .carousel-controls {
        display: none;
    }
}

/* 响应式工具类 */
.responsive-py-20 {
    padding-top: 5rem;
    padding-bottom: 5rem;
}

@media (max-width: 768px) {
    .responsive-py-20 {
        padding-top: 3rem;
        padding-bottom: 3rem;
    }
}
