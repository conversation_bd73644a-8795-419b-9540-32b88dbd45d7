/* Sky Mirror World Tour - 响应式样式 */

/* 基础响应式断点 */
@media (min-width: 640px) {
  /* sm: 小屏幕平板 */
  .sm\:px-6 { padding-left: var(--spacing-6); padding-right: var(--spacing-6); }
  .sm\:flex-row { flex-direction: row; }
  .sm\:gap-4 { gap: var(--spacing-4); }
  .sm\:text-base { font-size: var(--text-base); }
  .sm\:col-span-2 { grid-column: span 2 / span 2; }
}

@media (min-width: 768px) {
  /* md: 平板 */
  .md\:text-xl { font-size: var(--text-xl); }
  .md\:text-2xl { font-size: var(--text-2xl); }
  .md\:text-3xl { font-size: var(--text-3xl); }
  .md\:text-4xl { font-size: var(--text-4xl); }
  .md\:text-5xl { font-size: var(--text-5xl); }
  .md\:text-6xl { font-size: var(--text-6xl); }
  
  .md\:p-6 { padding: var(--spacing-6); }
  .md\:px-6 { padding-left: var(--spacing-6); padding-right: var(--spacing-6); }
  .md\:py-16 { padding-top: var(--spacing-16); padding-bottom: var(--spacing-16); }
  
  .md\:mb-4 { margin-bottom: var(--spacing-4); }
  .md\:mb-6 { margin-bottom: var(--spacing-6); }
  .md\:mb-8 { margin-bottom: var(--spacing-8); }
  .md\:mb-10 { margin-bottom: var(--spacing-10); }
  .md\:mb-12 { margin-bottom: var(--spacing-12); }
  
  .md\:gap-6 { gap: var(--spacing-6); }
  .md\:gap-8 { gap: var(--spacing-8); }
  
  .md\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .md\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
  
  .md\:flex { display: flex; }
  .md\:hidden { display: none; }
  .md\:block { display: block; }
  
  .md\:w-6 { width: 1.5rem; }
  .md\:h-6 { height: 1.5rem; }
  .md\:w-7 { width: 1.75rem; }
  .md\:h-7 { height: 1.75rem; }
}

@media (min-width: 1024px) {
  /* lg: 桌面 */
  .lg\:text-2xl { font-size: var(--text-2xl); }
  .lg\:text-4xl { font-size: var(--text-4xl); }
  .lg\:text-5xl { font-size: var(--text-5xl); }
  .lg\:text-7xl { font-size: var(--text-7xl); }
  
  .lg\:px-8 { padding-left: var(--spacing-8); padding-right: var(--spacing-8); }
  .lg\:px-6 { padding-left: var(--spacing-6); padding-right: var(--spacing-6); }
  
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
  
  .lg\:col-span-1 { grid-column: span 1 / span 1; }
}

@media (min-width: 1280px) {
  /* xl: 大桌面 */
  .xl\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .xl\:col-span-2 { grid-column: span 2 / span 2; }
  .xl\:col-span-1 { grid-column: span 1 / span 1; }
}

/* Hero 区域响应式 */
.hero-section {
  position: relative;
  height: 85vh;
  min-height: 600px;
  max-height: 800px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

@media (max-width: 767px) {
  .hero-section {
    height: 70vh;
    min-height: 500px;
    max-height: 600px;
  }
}

.hero-background {
  position: absolute;
  inset: 0;
  background-size: cover;
  background-position: center;
}

.hero-overlay {
  position: absolute;
  inset: 0;
  background: var(--gradient-primary);
  opacity: 0.9;
}

.hero-content {
  position: relative;
  z-index: 10;
  text-align: center;
  color: var(--color-white);
  padding: 0 var(--spacing-4);
  max-width: 80rem;
  margin: 0 auto;
}

/* 导航栏响应式 */
.navbar {
  background: var(--color-white);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 50;
  transition: var(--transition-all);
}

.navbar.scrolled {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.nav-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 4rem;
}

@media (min-width: 640px) {
  .nav-container {
    padding: 0 var(--spacing-6);
  }
}

@media (min-width: 1024px) {
  .nav-container {
    padding: 0 var(--spacing-8);
  }
}

.nav-menu {
  display: none;
  align-items: center;
  gap: var(--spacing-8);
}

@media (min-width: 768px) {
  .nav-menu {
    display: flex;
  }
}

.mobile-menu-btn {
  display: block;
  background: none;
  border: none;
  color: var(--color-gray-700);
  cursor: pointer;
  padding: var(--spacing-2);
}

@media (min-width: 768px) {
  .mobile-menu-btn {
    display: none;
  }
}

.mobile-menu {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--color-white);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: var(--spacing-4);
}

.mobile-menu.show {
  display: block;
}

@media (min-width: 768px) {
  .mobile-menu {
    display: none !important;
  }
}

/* 服务卡片网格响应式 */
.services-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-4);
}

@media (min-width: 768px) {
  .services-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-6);
  }
}

@media (min-width: 1024px) {
  .services-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* 特色功能网格响应式 */
.features-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-6);
}

@media (min-width: 640px) {
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .features-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-8);
  }
}

/* 联系信息网格响应式 */
.contact-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-8);
}

@media (min-width: 768px) {
  .contact-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .contact-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 表单布局响应式 */
.form-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-6);
}

@media (min-width: 768px) {
  .form-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.form-row {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-6);
}

@media (min-width: 768px) {
  .form-row {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 时间线响应式 */
.timeline {
  position: relative;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 2px;
  height: 100%;
  background: var(--color-blue-200);
}

@media (max-width: 767px) {
  .timeline::before {
    left: var(--spacing-4);
    transform: none;
  }
}

.timeline-item {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-8);
}

@media (max-width: 767px) {
  .timeline-item {
    flex-direction: row;
    padding-left: var(--spacing-12);
  }
  
  .timeline-item:nth-child(even) {
    flex-direction: row;
  }
}

.timeline-content {
  width: calc(50% - var(--spacing-8));
  background: var(--color-white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  padding: var(--spacing-6);
}

@media (max-width: 767px) {
  .timeline-content {
    width: 100%;
    margin: 0;
    text-align: left;
  }
}

.timeline-item:nth-child(odd) .timeline-content {
  margin-right: var(--spacing-8);
  text-align: right;
}

.timeline-item:nth-child(even) .timeline-content {
  margin-left: var(--spacing-8);
  text-align: left;
}

@media (max-width: 767px) {
  .timeline-item:nth-child(odd) .timeline-content,
  .timeline-item:nth-child(even) .timeline-content {
    margin: 0;
    text-align: left;
  }
}

.timeline-dot {
  position: relative;
  z-index: 10;
  width: var(--spacing-4);
  height: var(--spacing-4);
  background: var(--color-blue-600);
  border-radius: 50%;
  border: 4px solid var(--color-white);
  box-shadow: var(--shadow-lg);
  flex-shrink: 0;
}

@media (max-width: 767px) {
  .timeline-dot {
    position: absolute;
    left: var(--spacing-2);
  }
}

/* 团队成员网格响应式 */
.team-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-8);
}

@media (min-width: 768px) {
  .team-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .team-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 菜单网格响应式 */
.menu-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-8);
}

@media (min-width: 768px) {
  .menu-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* 按钮组响应式 */
.button-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
  justify-content: center;
}

@media (min-width: 640px) {
  .button-group {
    flex-direction: row;
    gap: var(--spacing-4);
  }
}

/* 文本大小响应式调整 */
.responsive-text-sm {
  font-size: var(--text-sm);
}

@media (min-width: 768px) {
  .responsive-text-sm {
    font-size: var(--text-base);
  }
}

.responsive-text-base {
  font-size: var(--text-base);
}

@media (min-width: 768px) {
  .responsive-text-base {
    font-size: var(--text-lg);
  }
}

.responsive-text-lg {
  font-size: var(--text-lg);
}

@media (min-width: 768px) {
  .responsive-text-lg {
    font-size: var(--text-xl);
  }
}

.responsive-text-xl {
  font-size: var(--text-xl);
}

@media (min-width: 768px) {
  .responsive-text-xl {
    font-size: var(--text-2xl);
  }
}

/* 间距响应式调整 */
.responsive-py-12 {
  padding-top: var(--spacing-12);
  padding-bottom: var(--spacing-12);
}

@media (min-width: 768px) {
  .responsive-py-12 {
    padding-top: var(--spacing-16);
    padding-bottom: var(--spacing-16);
  }
}

.responsive-py-16 {
  padding-top: var(--spacing-12);
  padding-bottom: var(--spacing-12);
}

@media (min-width: 768px) {
  .responsive-py-16 {
    padding-top: var(--spacing-16);
    padding-bottom: var(--spacing-16);
  }
}

.responsive-py-20 {
  padding-top: var(--spacing-16);
  padding-bottom: var(--spacing-16);
}

@media (min-width: 768px) {
  .responsive-py-20 {
    padding-top: var(--spacing-20);
    padding-bottom: var(--spacing-20);
  }
}

/* 容器最大宽度响应式 */
.responsive-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--spacing-3);
}

@media (min-width: 640px) {
  .responsive-container {
    padding: 0 var(--spacing-4);
  }
}

@media (min-width: 1024px) {
  .responsive-container {
    padding: 0 var(--spacing-6);
  }
}

/* 图片响应式 */
.responsive-image {
  width: 100%;
  height: auto;
  object-fit: cover;
}

.responsive-image.aspect-square {
  aspect-ratio: 1 / 1;
}

.responsive-image.aspect-video {
  aspect-ratio: 16 / 9;
}

/* 隐藏/显示响应式工具类 */
@media (max-width: 639px) {
  .hidden-mobile { display: none !important; }
}

@media (min-width: 640px) and (max-width: 767px) {
  .hidden-tablet { display: none !important; }
}

@media (min-width: 768px) {
  .hidden-desktop { display: none !important; }
}

@media (max-width: 767px) {
  .mobile-only { display: block !important; }
}

@media (min-width: 768px) {
  .mobile-only { display: none !important; }
}

@media (min-width: 768px) {
  .desktop-only { display: block !important; }
}

@media (max-width: 767px) {
  .desktop-only { display: none !important; }
}

/* 移动端布局修复 */
@media (max-width: 767px) {
  /* 确保内容不会溢出 */
  * {
    max-width: 100%;
    box-sizing: border-box;
  }

  /* 修复移动端文字大小 */
  .hero-content h1 {
    font-size: var(--text-3xl) !important;
    line-height: 1.2;
  }

  .hero-content p {
    font-size: var(--text-base) !important;
    line-height: 1.6;
  }

  /* 修复按钮在移动端的显示 */
  .btn {
    width: 100%;
    justify-content: center;
    padding: var(--spacing-3) var(--spacing-4);
    font-size: var(--text-base);
  }

  .button-group .btn {
    margin-bottom: var(--spacing-2);
  }

  /* 修复卡片在移动端的间距 */
  .card {
    margin-bottom: var(--spacing-4);
  }

  .card-body {
    padding: var(--spacing-4);
  }

  /* 修复网格在移动端的显示 */
  .services-grid,
  .features-grid {
    grid-template-columns: 1fr !important;
    gap: var(--spacing-4);
  }

  /* 修复表单在移动端的显示 */
  .form-input,
  .form-select,
  .form-textarea {
    font-size: 16px; /* 防止 iOS 缩放 */
    padding: var(--spacing-3);
  }

  /* 修复导航在移动端的显示 */
  .mobile-menu {
    padding: var(--spacing-4);
  }

  .mobile-nav-link {
    padding: var(--spacing-3) 0;
    font-size: var(--text-base);
  }

  /* 修复图片画廊在移动端的显示 */
  .photo-gallery {
    grid-template-columns: 1fr 1fr !important;
    gap: var(--spacing-2);
  }

  /* 修复时间线在移动端的显示 */
  .timeline-content {
    padding: var(--spacing-4);
    font-size: var(--text-sm);
  }

  /* 修复统计数据在移动端的显示 */
  .grid.grid-cols-2.md\\:grid-cols-4 {
    grid-template-columns: 1fr 1fr !important;
    gap: var(--spacing-4);
    text-align: center;
  }

  /* 修复 Hero 区域在移动端的高度 */
  .hero-section {
    min-height: 60vh;
    padding: var(--spacing-8) 0;
  }

  /* 修复容器在移动端的内边距 */
  .container {
    padding-left: var(--spacing-4);
    padding-right: var(--spacing-4);
  }

  /* 修复文字对齐 */
  .text-center {
    text-align: center;
  }

  /* 修复间距 */
  .responsive-py-20 {
    padding-top: var(--spacing-12);
    padding-bottom: var(--spacing-12);
  }

  .responsive-py-16 {
    padding-top: var(--spacing-10);
    padding-bottom: var(--spacing-10);
  }

  /* 修复评价卡片 */
  .grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3 {
    grid-template-columns: 1fr !important;
    gap: var(--spacing-4);
  }
}
