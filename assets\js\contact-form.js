/**
 * Sky Mirror World Tour - 联系表单功能
 * 处理联系表单的验证、提交和用户反馈
 */

class ContactForm {
    constructor() {
        this.form = null;
        this.formData = {};
        this.isSubmitting = false;
        
        this.init();
    }

    /**
     * 初始化联系表单
     */
    init() {
        this.form = document.getElementById('contact-form');
        
        if (!this.form) {
            console.warn('联系表单未找到');
            return;
        }

        // 绑定表单事件
        this.bindFormEvents();
        
        // 设置日期输入最小值
        this.setupDateInput();
        
        console.log('联系表单初始化完成');
    }

    /**
     * 绑定表单事件
     */
    bindFormEvents() {
        // 表单提交事件
        this.form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleSubmit();
        });

        // 实时验证
        const inputs = this.form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('blur', () => {
                this.validateField(input);
            });

            input.addEventListener('input', () => {
                this.clearFieldError(input);
            });
        });
    }

    /**
     * 设置日期输入
     */
    setupDateInput() {
        const dateInput = document.getElementById('travelDate');
        if (dateInput) {
            // 设置最小日期为今天
            const today = new Date().toISOString().split('T')[0];
            dateInput.min = today;
        }
    }

    /**
     * 处理表单提交
     */
    async handleSubmit() {
        if (this.isSubmitting) {
            return;
        }

        // 收集表单数据
        this.collectFormData();

        // 验证表单
        if (!this.validateForm()) {
            return;
        }

        // 显示提交状态
        this.setSubmittingState(true);

        try {
            // 模拟提交过程
            await this.submitForm();
            
            // 显示成功消息
            this.showSuccessMessage();
            
            // 重置表单
            this.resetForm();
            
        } catch (error) {
            console.error('表单提交失败:', error);
            this.showErrorMessage('提交失败，请稍后重试或直接联系我们。');
        } finally {
            this.setSubmittingState(false);
        }
    }

    /**
     * 收集表单数据
     */
    collectFormData() {
        const formData = new FormData(this.form);
        this.formData = {};
        
        for (let [key, value] of formData.entries()) {
            this.formData[key] = value.trim();
        }
    }

    /**
     * 验证整个表单
     */
    validateForm() {
        let isValid = true;
        const requiredFields = ['firstName', 'email', 'message'];
        
        // 验证必填字段
        requiredFields.forEach(fieldName => {
            const field = document.getElementById(fieldName);
            if (field && !this.validateField(field)) {
                isValid = false;
            }
        });

        return isValid;
    }

    /**
     * 验证单个字段
     */
    validateField(field) {
        const value = field.value.trim();
        const fieldName = field.name;
        let isValid = true;
        let errorMessage = '';

        // 清除之前的错误状态
        this.clearFieldError(field);

        // 必填字段验证
        if (field.required && !value) {
            errorMessage = '此字段为必填项';
            isValid = false;
        }
        // 邮箱格式验证
        else if (fieldName === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                errorMessage = '请输入有效的邮箱地址';
                isValid = false;
            }
        }
        // 电话号码验证
        else if (fieldName === 'phone' && value) {
            const phoneRegex = /^[\+]?[0-9\s\-\(\)]{8,}$/;
            if (!phoneRegex.test(value)) {
                errorMessage = '请输入有效的电话号码';
                isValid = false;
            }
        }
        // 姓名长度验证
        else if (fieldName === 'firstName' && value) {
            if (value.length < 2) {
                errorMessage = '姓名至少需要2个字符';
                isValid = false;
            }
        }
        // 消息长度验证
        else if (fieldName === 'message' && value) {
            if (value.length < 10) {
                errorMessage = '请详细描述您的需求（至少10个字符）';
                isValid = false;
            }
        }

        // 显示错误信息
        if (!isValid) {
            this.showFieldError(field, errorMessage);
        }

        return isValid;
    }

    /**
     * 显示字段错误
     */
    showFieldError(field, message) {
        field.classList.add('error');
        
        // 移除现有错误消息
        const existingError = field.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }

        // 添加错误消息
        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error';
        errorDiv.textContent = message;
        errorDiv.style.cssText = `
            color: #ef4444;
            font-size: 0.875rem;
            margin-top: 0.25rem;
        `;
        
        field.parentNode.appendChild(errorDiv);
    }

    /**
     * 清除字段错误
     */
    clearFieldError(field) {
        field.classList.remove('error');
        
        const errorDiv = field.parentNode.querySelector('.field-error');
        if (errorDiv) {
            errorDiv.remove();
        }
    }

    /**
     * 模拟表单提交
     */
    async submitForm() {
        // 模拟网络请求延迟
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 这里应该是实际的表单提交逻辑
        console.log('提交的表单数据:', this.formData);
        
        // 模拟成功提交
        return { success: true };
    }

    /**
     * 设置提交状态
     */
    setSubmittingState(isSubmitting) {
        this.isSubmitting = isSubmitting;
        const submitButton = this.form.querySelector('button[type="submit"]');
        
        if (submitButton) {
            if (isSubmitting) {
                submitButton.disabled = true;
                submitButton.innerHTML = `
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="animate-spin">
                        <path d="M21 12a9 9 0 11-6.219-8.56"/>
                    </svg>
                    发送中...
                `;
            } else {
                submitButton.disabled = false;
                submitButton.innerHTML = `
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                        <polyline points="22,6 12,13 2,6"/>
                    </svg>
                    发送消息
                `;
            }
        }
    }

    /**
     * 显示成功消息
     */
    showSuccessMessage() {
        const message = `感谢您的咨询！

我们已收到您的消息，我们的专业团队将在24小时内与您联系。

如有紧急需求，请直接拨打我们的客服热线：
+60 12-345-6789

期待为您提供优质的旅游服务！`;

        this.showAlert(message, 'success');
    }

    /**
     * 显示错误消息
     */
    showErrorMessage(message) {
        this.showAlert(message, 'error');
    }

    /**
     * 显示提示信息
     */
    showAlert(message, type = 'info') {
        // 创建提示框
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert-notification alert-${type}`;
        alertDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1.5rem;
            border-radius: 0.5rem;
            color: white;
            font-weight: 600;
            z-index: 1000;
            max-width: 400px;
            word-wrap: break-word;
            white-space: pre-line;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            ${type === 'success' ? 'background: #10b981;' : ''}
            ${type === 'error' ? 'background: #ef4444;' : ''}
            ${type === 'warning' ? 'background: #f59e0b;' : ''}
            ${type === 'info' ? 'background: #3b82f6;' : ''}
        `;
        
        alertDiv.textContent = message;
        document.body.appendChild(alertDiv);
        
        // 添加进入动画
        alertDiv.style.transform = 'translateX(100%)';
        alertDiv.style.transition = 'transform 0.3s ease';
        
        setTimeout(() => {
            alertDiv.style.transform = 'translateX(0)';
        }, 10);
        
        // 自动移除
        const duration = type === 'success' ? 8000 : 5000;
        setTimeout(() => {
            alertDiv.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 300);
        }, duration);
    }

    /**
     * 重置表单
     */
    resetForm() {
        this.form.reset();
        this.formData = {};
        
        // 清除所有错误状态
        const fields = this.form.querySelectorAll('input, select, textarea');
        fields.forEach(field => {
            this.clearFieldError(field);
        });
        
        // 重新设置日期最小值
        this.setupDateInput();
    }

    /**
     * 获取表单数据
     */
    getFormData() {
        return { ...this.formData };
    }
}

// 添加旋转动画样式
if (!document.getElementById('contact-form-styles')) {
    const style = document.createElement('style');
    style.id = 'contact-form-styles';
    style.textContent = `
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .animate-spin {
            animation: spin 1s linear infinite;
        }
        
        .form-input.error,
        .form-select.error,
        .form-textarea.error {
            border-color: #ef4444;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }
        
        .field-error {
            animation: fadeIn 0.3s ease;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-5px); }
            to { opacity: 1; transform: translateY(0); }
        }
    `;
    document.head.appendChild(style);
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new ContactForm();
});

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ContactForm;
} else {
    window.ContactForm = ContactForm;
}
