/**
 * Sky Mirror World Tour - 数据加载器
 * 负责从CMS系统加载数据并缓存
 */

class DataLoader {
    constructor() {
        this.cache = new Map();
        this.cacheExpiry = new Map();
        this.defaultCacheDuration = 5 * 60 * 1000; // 5分钟缓存
        this.dataVersion = null;
        this.isOnline = navigator.onLine;
        
        this.init();
    }

    /**
     * 初始化数据加载器
     */
    init() {
        // 监听网络状态变化
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.refreshAllData();
        });
        
        window.addEventListener('offline', () => {
            this.isOnline = false;
        });

        // 定期检查数据更新
        setInterval(() => {
            this.checkForUpdates();
        }, 30000); // 每30秒检查一次

        // 监听storage事件（来自CMS的更新通知）
        window.addEventListener('storage', (e) => {
            if (e.key === 'frontend_data_updated' && e.newValue === 'true') {
                this.handleFrontendDataUpdate();
            }
        });
    }

    /**
     * 加载路线数据
     * @returns {Promise<Array>} 路线数组
     */
    async loadRoutes() {
        try {
            const cacheKey = 'routes';
            
            // 检查缓存
            if (this.isCacheValid(cacheKey)) {
                return this.cache.get(cacheKey);
            }

            // 尝试从CMS加载
            let routes = await this.loadFromCMS('routes');
            
            // 如果CMS加载失败，使用备用数据
            if (!routes) {
                routes = this.getFallbackRoutes();
            }

            // 缓存数据
            this.setCache(cacheKey, routes);
            
            return routes;
        } catch (error) {
            console.error('加载路线数据失败:', error);
            return this.getFallbackRoutes();
        }
    }

    /**
     * 加载套餐数据
     * @returns {Promise<Array>} 套餐数组
     */
    async loadPackages() {
        try {
            const cacheKey = 'packages';
            
            // 检查缓存
            if (this.isCacheValid(cacheKey)) {
                return this.cache.get(cacheKey);
            }

            // 尝试从CMS加载
            let packages = await this.loadFromCMS('packages');
            
            // 如果CMS加载失败，使用备用数据
            if (!packages) {
                packages = this.getFallbackPackages();
            }

            // 缓存数据
            this.setCache(cacheKey, packages);
            
            return packages;
        } catch (error) {
            console.error('加载套餐数据失败:', error);
            return this.getFallbackPackages();
        }
    }

    /**
     * 加载图片数据
     * @returns {Promise<Array>} 图片数组
     */
    async loadImages() {
        try {
            const cacheKey = 'images';
            
            // 检查缓存
            if (this.isCacheValid(cacheKey)) {
                return this.cache.get(cacheKey);
            }

            // 尝试从CMS加载
            let images = await this.loadFromCMS('images');
            
            // 如果CMS加载失败，使用备用数据
            if (!images) {
                images = this.getFallbackImages();
            }

            // 缓存数据
            this.setCache(cacheKey, images);
            
            return images;
        } catch (error) {
            console.error('加载图片数据失败:', error);
            return this.getFallbackImages();
        }
    }

    /**
     * 从CMS系统加载数据
     * @param {string} type - 数据类型
     * @returns {Promise<Array|null>} 数据数组或null
     */
    async loadFromCMS(type) {
        try {
            // 首先尝试从前台同步数据读取
            const frontendData = localStorage.getItem(`frontend_${type}`);
            if (frontendData) {
                const parsedData = JSON.parse(frontendData);
                if (parsedData && parsedData[type]) {
                    return parsedData[type];
                }
            }

            // 然后尝试从CMS localStorage读取
            const cmsData = localStorage.getItem(`cms_${type}`);
            if (cmsData) {
                const parsedData = JSON.parse(cmsData);
                if (parsedData && parsedData[type]) {
                    // 只返回已发布的数据
                    return parsedData[type].filter(item => item.status === 'active');
                }
            }

            // 最后尝试从JSON文件加载
            const response = await fetch(`admin/data/${type}.json`);
            if (response.ok) {
                const data = await response.json();
                if (data && data[type]) {
                    return data[type].filter(item => item.status === 'active');
                }
            }

            return null;
        } catch (error) {
            console.error(`从CMS加载${type}数据失败:`, error);
            return null;
        }
    }

    /**
     * 处理前台数据更新
     */
    async handleFrontendDataUpdate() {
        console.log('检测到前台数据更新，正在刷新...');

        // 清除缓存
        this.clearCache();

        // 重新加载所有数据
        await this.refreshAllData();

        // 清除更新标记
        localStorage.removeItem('frontend_data_updated');

        // 触发页面刷新事件
        window.dispatchEvent(new CustomEvent('dataUpdated', {
            detail: { timestamp: new Date().toISOString() }
        }));

        console.log('前台数据刷新完成');
    }

    /**
     * 检查缓存是否有效
     * @param {string} key - 缓存键
     * @returns {boolean} 是否有效
     */
    isCacheValid(key) {
        if (!this.cache.has(key)) {
            return false;
        }

        const expiry = this.cacheExpiry.get(key);
        if (!expiry || Date.now() > expiry) {
            this.cache.delete(key);
            this.cacheExpiry.delete(key);
            return false;
        }

        return true;
    }

    /**
     * 设置缓存
     * @param {string} key - 缓存键
     * @param {*} data - 数据
     * @param {number} duration - 缓存时长（毫秒）
     */
    setCache(key, data, duration = this.defaultCacheDuration) {
        this.cache.set(key, data);
        this.cacheExpiry.set(key, Date.now() + duration);
    }

    /**
     * 清除缓存
     * @param {string} key - 缓存键（可选）
     */
    clearCache(key = null) {
        if (key) {
            this.cache.delete(key);
            this.cacheExpiry.delete(key);
        } else {
            this.cache.clear();
            this.cacheExpiry.clear();
        }
    }

    /**
     * 检查数据更新
     */
    async checkForUpdates() {
        if (!this.isOnline) return;

        try {
            // 检查CMS数据版本
            const cmsRoutes = localStorage.getItem('cms_routes');
            const cmsPackages = localStorage.getItem('cms_packages');
            const cmsImages = localStorage.getItem('cms_images');

            if (cmsRoutes || cmsPackages || cmsImages) {
                // 如果有CMS数据更新，清除缓存
                this.clearCache();
                console.log('检测到CMS数据更新，已清除缓存');
            }
        } catch (error) {
            console.error('检查数据更新失败:', error);
        }
    }

    /**
     * 刷新所有数据
     */
    async refreshAllData() {
        this.clearCache();
        await Promise.all([
            this.loadRoutes(),
            this.loadPackages(),
            this.loadImages()
        ]);
        console.log('所有数据已刷新');
    }

    /**
     * 获取备用路线数据
     */
    getFallbackRoutes() {
        return [
            {
                id: 'japan-sakura-fallback',
                title: '日本樱花季经典游',
                description: '体验日本春季最美的樱花盛景，游览东京、京都、大阪等经典城市。',
                image: 'https://images.pexels.com/photos/2070033/pexels-photo-2070033.jpeg?auto=compress&cs=tinysrgb&w=400&h=250&fit=crop',
                highlights: ['东京', '京都', '大阪', '富士山'],
                duration: '7天6夜',
                price: { from: 3299, currency: 'RM' },
                badge: { type: 'hot', text: '热门', color: '#ef4444' },
                category: 'international',
                status: 'active'
            }
        ];
    }

    /**
     * 获取备用套餐数据
     */
    getFallbackPackages() {
        return [
            {
                id: 'sky-mirror-fallback',
                title: '天空之镜基础套餐',
                type: 'signature-tickets',
                description: '体验天空之镜的神奇魅力，在这里天空与海水完美融合。',
                duration: '4-5小时',
                location: '瓜拉雪兰莪',
                pricing: { adult: 150, child: 100, currency: 'RM' },
                status: 'active'
            }
        ];
    }

    /**
     * 获取备用图片数据
     */
    getFallbackImages() {
        return [
            {
                id: 'fallback-image',
                url: 'https://images.pexels.com/photos/1680247/pexels-photo-1680247.jpeg',
                alt: '天空之镜',
                title: '天空之镜',
                category: 'packages',
                status: 'active'
            }
        ];
    }

    /**
     * 获取数据统计
     */
    getStats() {
        return {
            cacheSize: this.cache.size,
            isOnline: this.isOnline,
            lastUpdate: new Date().toISOString()
        };
    }
}

// 创建全局数据加载器实例
window.dataLoader = new DataLoader();

// 导出类
window.DataLoader = DataLoader;
