/**
 * Sky Mirror World Tour - 懒加载模块
 * 实现图片和内容的懒加载功能，提升页面性能
 */

class LazyLoader {
    constructor() {
        this.imageObserver = null;
        this.contentObserver = null;
        this.isSupported = 'IntersectionObserver' in window;
        
        this.init();
    }

    /**
     * 初始化懒加载功能
     */
    init() {
        if (!this.isSupported) {
            console.warn('IntersectionObserver 不支持，使用降级方案');
            this.fallbackLoad();
            return;
        }

        // 初始化图片懒加载
        this.initImageLazyLoading();
        
        // 初始化内容懒加载
        this.initContentLazyLoading();
        
        console.log('懒加载模块初始化完成');
    }

    /**
     * 初始化图片懒加载
     */
    initImageLazyLoading() {
        // 创建图片观察器
        this.imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.loadImage(entry.target);
                    this.imageObserver.unobserve(entry.target);
                }
            });
        }, {
            rootMargin: '50px 0px',
            threshold: 0.01
        });

        // 观察所有懒加载图片
        this.observeImages();
    }

    /**
     * 初始化内容懒加载
     */
    initContentLazyLoading() {
        // 创建内容观察器
        this.contentObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.loadContent(entry.target);
                    this.contentObserver.unobserve(entry.target);
                }
            });
        }, {
            rootMargin: '100px 0px',
            threshold: 0.1
        });

        // 观察所有懒加载内容
        this.observeContent();
    }

    /**
     * 观察图片元素
     */
    observeImages() {
        // 查找所有需要懒加载的图片
        const lazyImages = document.querySelectorAll('img[data-src], img[loading="lazy"]');
        
        lazyImages.forEach(img => {
            // 如果图片还没有加载
            if (!img.src || img.src === img.dataset.placeholder) {
                this.imageObserver.observe(img);
            }
        });

        // 查找背景图片
        const lazyBackgrounds = document.querySelectorAll('[data-bg]');
        lazyBackgrounds.forEach(element => {
            this.imageObserver.observe(element);
        });
    }

    /**
     * 观察内容元素
     */
    observeContent() {
        // 查找所有需要懒加载的内容
        const lazyContent = document.querySelectorAll('[data-lazy-content]');
        
        lazyContent.forEach(element => {
            this.contentObserver.observe(element);
        });
    }

    /**
     * 加载图片
     */
    loadImage(img) {
        // 处理普通图片
        if (img.tagName === 'IMG') {
            const src = img.dataset.src || img.src;
            
            if (src && src !== img.src) {
                // 创建新图片对象预加载
                const imageLoader = new Image();
                
                imageLoader.onload = () => {
                    // 添加淡入效果
                    img.style.opacity = '0';
                    img.style.transition = 'opacity 0.3s ease';
                    
                    img.src = src;
                    img.removeAttribute('data-src');
                    
                    // 淡入显示
                    setTimeout(() => {
                        img.style.opacity = '1';
                    }, 10);
                    
                    // 添加加载完成类
                    img.classList.add('loaded');
                };
                
                imageLoader.onerror = () => {
                    console.error('图片加载失败:', src);
                    img.classList.add('error');
                };
                
                imageLoader.src = src;
            }
        }
        // 处理背景图片
        else if (img.dataset.bg) {
            const bgUrl = img.dataset.bg;
            
            // 预加载背景图片
            const imageLoader = new Image();
            
            imageLoader.onload = () => {
                img.style.backgroundImage = `url(${bgUrl})`;
                img.removeAttribute('data-bg');
                img.classList.add('bg-loaded');
                
                // 添加淡入效果
                img.style.opacity = '0';
                img.style.transition = 'opacity 0.5s ease';
                
                setTimeout(() => {
                    img.style.opacity = '1';
                }, 10);
            };
            
            imageLoader.onerror = () => {
                console.error('背景图片加载失败:', bgUrl);
                img.classList.add('bg-error');
            };
            
            imageLoader.src = bgUrl;
        }
    }

    /**
     * 加载内容
     */
    loadContent(element) {
        const contentType = element.dataset.lazyContent;
        
        switch (contentType) {
            case 'fade-in':
                this.fadeInContent(element);
                break;
            case 'slide-up':
                this.slideUpContent(element);
                break;
            case 'scale':
                this.scaleContent(element);
                break;
            default:
                this.defaultContentLoad(element);
        }
    }

    /**
     * 淡入内容
     */
    fadeInContent(element) {
        element.style.opacity = '0';
        element.style.transition = 'opacity 0.6s ease';
        
        setTimeout(() => {
            element.style.opacity = '1';
            element.classList.add('content-loaded');
        }, 100);
    }

    /**
     * 向上滑入内容
     */
    slideUpContent(element) {
        element.style.opacity = '0';
        element.style.transform = 'translateY(30px)';
        element.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        
        setTimeout(() => {
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
            element.classList.add('content-loaded');
        }, 100);
    }

    /**
     * 缩放内容
     */
    scaleContent(element) {
        element.style.opacity = '0';
        element.style.transform = 'scale(0.9)';
        element.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        
        setTimeout(() => {
            element.style.opacity = '1';
            element.style.transform = 'scale(1)';
            element.classList.add('content-loaded');
        }, 100);
    }

    /**
     * 默认内容加载
     */
    defaultContentLoad(element) {
        element.classList.add('content-loaded');
    }

    /**
     * 降级方案 - 直接加载所有内容
     */
    fallbackLoad() {
        // 加载所有图片
        const lazyImages = document.querySelectorAll('img[data-src]');
        lazyImages.forEach(img => {
            if (img.dataset.src) {
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
            }
        });

        // 加载所有背景图片
        const lazyBackgrounds = document.querySelectorAll('[data-bg]');
        lazyBackgrounds.forEach(element => {
            if (element.dataset.bg) {
                element.style.backgroundImage = `url(${element.dataset.bg})`;
                element.removeAttribute('data-bg');
            }
        });

        // 显示所有内容
        const lazyContent = document.querySelectorAll('[data-lazy-content]');
        lazyContent.forEach(element => {
            element.classList.add('content-loaded');
        });
    }

    /**
     * 手动触发图片加载
     */
    loadImageNow(img) {
        if (this.imageObserver) {
            this.imageObserver.unobserve(img);
        }
        this.loadImage(img);
    }

    /**
     * 手动触发内容加载
     */
    loadContentNow(element) {
        if (this.contentObserver) {
            this.contentObserver.unobserve(element);
        }
        this.loadContent(element);
    }

    /**
     * 重新扫描页面中的懒加载元素
     */
    refresh() {
        if (!this.isSupported) {
            this.fallbackLoad();
            return;
        }

        // 重新观察新添加的图片
        this.observeImages();
        
        // 重新观察新添加的内容
        this.observeContent();
    }

    /**
     * 销毁懒加载实例
     */
    destroy() {
        if (this.imageObserver) {
            this.imageObserver.disconnect();
            this.imageObserver = null;
        }

        if (this.contentObserver) {
            this.contentObserver.disconnect();
            this.contentObserver = null;
        }

        console.log('懒加载模块已销毁');
    }
}

// 工具函数：为图片添加懒加载属性
function setupLazyImage(img, src, placeholder = '') {
    if (placeholder) {
        img.src = placeholder;
    }
    img.dataset.src = src;
    img.loading = 'lazy';
}

// 工具函数：为元素添加懒加载内容
function setupLazyContent(element, type = 'fade-in') {
    element.dataset.lazyContent = type;
}

// 导出类和工具函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { LazyLoader, setupLazyImage, setupLazyContent };
} else {
    window.LazyLoader = LazyLoader;
    window.setupLazyImage = setupLazyImage;
    window.setupLazyContent = setupLazyContent;
}
