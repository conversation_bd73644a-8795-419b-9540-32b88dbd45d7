/**
 * Sky Mirror World Tour - 主 JavaScript 文件
 * 负责应用的初始化和全局功能
 */

// 应用主类
class SkyMirrorApp {
    constructor() {
        this.modules = new Map();
        this.isInitialized = false;
        this.init();
    }

    /**
     * 初始化应用
     */
    async init() {
        try {
            // 等待 DOM 加载完成
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.initializeApp());
            } else {
                this.initializeApp();
            }
        } catch (error) {
            console.error('应用初始化失败:', error);
        }
    }

    /**
     * 初始化应用核心功能
     */
    async initializeApp() {
        console.log('Sky Mirror World Tour 应用正在启动...');

        // 初始化核心模块
        await this.loadCoreModules();

        // 初始化页面特定功能
        this.initializePageSpecificFeatures();

        // 初始化全局事件监听
        this.initializeGlobalEvents();

        // 初始化性能监控
        this.initializePerformanceMonitoring();

        this.isInitialized = true;
        console.log('Sky Mirror World Tour 应用启动完成');
    }

    /**
     * 加载核心模块
     */
    async loadCoreModules() {
        // 数据加载器模块
        if (typeof DataLoader !== 'undefined' && window.dataLoader) {
            this.modules.set('dataLoader', window.dataLoader);
        }

        // 导航模块
        if (typeof Navigation !== 'undefined') {
            this.modules.set('navigation', new Navigation());
        }

        // 懒加载模块
        if (typeof LazyLoader !== 'undefined') {
            this.modules.set('lazyLoader', new LazyLoader());
        }

        // 平滑滚动模块
        this.modules.set('smoothScroll', new SmoothScroll());
    }

    /**
     * 初始化页面特定功能
     */
    initializePageSpecificFeatures() {
        const currentPage = this.getCurrentPage();

        switch (currentPage) {
            case 'home':
                this.initializeHomePage();
                break;
            case 'signature-tickets':
                this.initializeSignatureTicketsPage();
                break;
            case 'contact':
                this.initializeContactPage();
                break;
            case 'sky-pier-cafe':
                this.initializeSkyPierCafePage();
                break;
            default:
                this.initializeDefaultPage();
        }
    }

    /**
     * 获取当前页面标识
     */
    getCurrentPage() {
        const path = window.location.pathname;
        
        if (path === '/' || path === '/index.html') {
            return 'home';
        }
        
        if (path.includes('signature-tickets')) {
            return 'signature-tickets';
        }
        
        if (path.includes('contact')) {
            return 'contact';
        }
        
        if (path.includes('sky-pier-cafe')) {
            return 'sky-pier-cafe';
        }
        
        return 'default';
    }

    /**
     * 初始化首页功能
     */
    initializeHomePage() {
        console.log('初始化首页功能');

        // 初始化服务卡片动画
        this.initializeServiceCards();

        // 初始化 CTA 按钮
        this.initializeCTAButtons();

        // 初始化当红路线轮播
        this.initializeRoutesCarousel();
    }

    /**
     * 初始化特色船票页面
     */
    initializeSignatureTicketsPage() {
        console.log('初始化特色船票页面功能');
        
        if (typeof SignatureTicketsPage !== 'undefined') {
            this.modules.set('signatureTickets', new SignatureTicketsPage());
        }
    }

    /**
     * 初始化联系我们页面
     */
    initializeContactPage() {
        console.log('初始化联系我们页面功能');
        
        if (typeof ContactForm !== 'undefined') {
            this.modules.set('contactForm', new ContactForm());
        }
    }

    /**
     * 初始化天空号船咖啡厅页面
     */
    initializeSkyPierCafePage() {
        console.log('初始化天空号船咖啡厅页面功能');
        
        // 初始化平滑滚动到故事区域
        const storyButton = document.querySelector('[data-scroll-to="story"]');
        if (storyButton) {
            storyButton.addEventListener('click', (e) => {
                e.preventDefault();
                const storySection = document.getElementById('story');
                if (storySection) {
                    storySection.scrollIntoView({ behavior: 'smooth' });
                }
            });
        }
    }

    /**
     * 初始化默认页面功能
     */
    initializeDefaultPage() {
        console.log('初始化默认页面功能');
        
        // 通用功能初始化
        this.initializeCommonFeatures();
    }

    /**
     * 初始化通用功能
     */
    initializeCommonFeatures() {
        // 初始化所有按钮的点击效果
        this.initializeButtonEffects();
        
        // 初始化卡片悬停效果
        this.initializeCardEffects();
    }

    /**
     * 初始化服务卡片
     */
    initializeServiceCards() {
        const serviceCards = document.querySelectorAll('.service-card');
        
        serviceCards.forEach(card => {
            // 添加进入视口动画
            this.observeElement(card, () => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 100);
            });
        });
    }

    /**
     * 初始化 CTA 按钮
     */
    initializeCTAButtons() {
        const ctaButtons = document.querySelectorAll('.btn-primary, .btn-secondary');

        ctaButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                // 添加点击波纹效果
                this.createRippleEffect(e, button);
            });
        });
    }

    /**
     * 初始化当红路线轮播
     */
    async initializeRoutesCarousel() {
        const carousel = document.getElementById('routesCarousel');
        if (!carousel) return;

        try {
            // 加载路线数据
            const dataLoader = this.modules.get('dataLoader');
            if (dataLoader) {
                const routes = await dataLoader.loadRoutes();
                this.renderRoutesCarousel(carousel, routes);
            }

            // 设置轮播功能
            this.setupCarouselControls(carousel);

            // 设置自动播放（可选）
            this.setupAutoPlay(carousel);

            // 设置触摸滑动（移动端）
            this.setupTouchSwipe(carousel);
        } catch (error) {
            console.error('初始化路线轮播失败:', error);
        }
    }

    /**
     * 渲染路线轮播
     */
    renderRoutesCarousel(carousel, routes) {
        if (!routes || routes.length === 0) return;

        // 只显示前6个路线
        const displayRoutes = routes.slice(0, 6);

        const routeCards = displayRoutes.map(route => this.createRouteCard(route)).join('');
        carousel.innerHTML = routeCards;
    }

    /**
     * 创建路线卡片
     */
    createRouteCard(route) {
        const badgeHtml = route.badge ?
            `<div class="route-badge ${route.badge.type}" style="background-color: ${route.badge.color}">
                ${route.badge.text}
            </div>` : '';

        return `
            <div class="route-card">
                <div class="route-image">
                    <img src="${route.image}" alt="${route.title}" loading="lazy">
                    ${badgeHtml}
                </div>
                <div class="route-content">
                    <h3 class="route-title">${route.title}</h3>
                    <div class="route-highlights">
                        ${route.highlights.map(highlight => `<span>${highlight}</span>`).join(' • ')}
                    </div>
                    <div class="route-details">
                        <span class="route-days">${route.duration}</span>
                        <div class="route-price">
                            从 <strong>${route.price.currency} ${route.price.from}</strong> 起
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 设置轮播控制
     */
    setupCarouselControls(carousel) {
        // 创建全局函数供HTML调用
        window.scrollCarousel = (direction) => {
            const scrollAmount = 340; // 卡片宽度 + 间距
            const currentScroll = carousel.scrollLeft;

            if (direction === 'left') {
                carousel.scrollTo({
                    left: currentScroll - scrollAmount,
                    behavior: 'smooth'
                });
            } else {
                carousel.scrollTo({
                    left: currentScroll + scrollAmount,
                    behavior: 'smooth'
                });
            }
        };

        // 创建路线预订函数
        window.bookRoute = (routeId) => {
            console.log('预订路线:', routeId);
            // 这里可以添加实际的预订逻辑
            alert(`正在为您预订 ${routeId} 路线，请稍候...`);
        };

        // 创建路线详情跳转函数
        window.goToRouteDetail = (routeId) => {
            console.log('跳转到路线详情:', routeId);
            // 跳转到国际旅游页面的对应产品详情部分
            const routeMapping = {
                'japan-sakura': 'japan-tours',
                'korea-seoul': 'korea-tours',
                'thailand-bangkok': 'thailand-tours',
                'vietnam-saigon': 'vietnam-tours',
                'singapore-classic': 'singapore-tours',
                'indonesia-bali': 'indonesia-tours'
            };

            const targetSection = routeMapping[routeId] || 'tours';
            window.location.href = `pages/international-tours.html#${targetSection}`;
        };
    }

    /**
     * 设置自动播放
     */
    setupAutoPlay(carousel) {
        let autoPlayInterval;
        const autoPlayDelay = 5000; // 5秒

        const startAutoPlay = () => {
            autoPlayInterval = setInterval(() => {
                const scrollAmount = 340;
                const maxScroll = carousel.scrollWidth - carousel.clientWidth;

                if (carousel.scrollLeft >= maxScroll) {
                    // 回到开始
                    carousel.scrollTo({ left: 0, behavior: 'smooth' });
                } else {
                    // 滚动到下一个
                    carousel.scrollTo({
                        left: carousel.scrollLeft + scrollAmount,
                        behavior: 'smooth'
                    });
                }
            }, autoPlayDelay);
        };

        const stopAutoPlay = () => {
            if (autoPlayInterval) {
                clearInterval(autoPlayInterval);
            }
        };

        // 鼠标悬停时停止自动播放
        carousel.addEventListener('mouseenter', stopAutoPlay);
        carousel.addEventListener('mouseleave', startAutoPlay);

        // 开始自动播放
        startAutoPlay();
    }

    /**
     * 设置触摸滑动
     */
    setupTouchSwipe(carousel) {
        let startX = 0;
        let scrollLeft = 0;
        let isDown = false;

        carousel.addEventListener('touchstart', (e) => {
            isDown = true;
            startX = e.touches[0].pageX - carousel.offsetLeft;
            scrollLeft = carousel.scrollLeft;
        });

        carousel.addEventListener('touchmove', (e) => {
            if (!isDown) return;
            e.preventDefault();
            const x = e.touches[0].pageX - carousel.offsetLeft;
            const walk = (x - startX) * 2;
            carousel.scrollLeft = scrollLeft - walk;
        });

        carousel.addEventListener('touchend', () => {
            isDown = false;
        });
    }

    /**
     * 初始化按钮效果
     */
    initializeButtonEffects() {
        const buttons = document.querySelectorAll('.btn');
        
        buttons.forEach(button => {
            button.addEventListener('mouseenter', () => {
                button.style.transform = 'translateY(-2px)';
            });
            
            button.addEventListener('mouseleave', () => {
                button.style.transform = 'translateY(0)';
            });
        });
    }

    /**
     * 初始化卡片效果
     */
    initializeCardEffects() {
        const cards = document.querySelectorAll('.card, .service-card, .feature-card');
        
        cards.forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-5px)';
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0)';
            });
        });
    }

    /**
     * 创建波纹效果
     */
    createRippleEffect(event, element) {
        const ripple = document.createElement('span');
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;
        
        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s ease-out;
            pointer-events: none;
        `;
        
        // 添加波纹动画样式
        if (!document.getElementById('ripple-styles')) {
            const style = document.createElement('style');
            style.id = 'ripple-styles';
            style.textContent = `
                @keyframes ripple {
                    to {
                        transform: scale(2);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        }
        
        element.style.position = 'relative';
        element.style.overflow = 'hidden';
        element.appendChild(ripple);
        
        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    /**
     * 观察元素进入视口
     */
    observeElement(element, callback) {
        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        callback();
                        observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '50px'
            });
            
            observer.observe(element);
        } else {
            // 降级处理
            callback();
        }
    }

    /**
     * 初始化全局事件监听
     */
    initializeGlobalEvents() {
        // 窗口大小改变事件
        window.addEventListener('resize', this.debounce(() => {
            this.handleWindowResize();
        }, 250));

        // 滚动事件
        window.addEventListener('scroll', this.throttle(() => {
            this.handleScroll();
        }, 100));

        // 页面可见性变化
        document.addEventListener('visibilitychange', () => {
            this.handleVisibilityChange();
        });
    }

    /**
     * 处理窗口大小改变
     */
    handleWindowResize() {
        // 更新导航状态
        const navigation = this.modules.get('navigation');
        if (navigation && navigation.handleResize) {
            navigation.handleResize();
        }
    }

    /**
     * 处理滚动事件
     */
    handleScroll() {
        // 更新导航栏背景
        const navbar = document.querySelector('.navbar');
        if (navbar) {
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        }
    }

    /**
     * 处理页面可见性变化
     */
    handleVisibilityChange() {
        if (document.hidden) {
            console.log('页面隐藏');
        } else {
            console.log('页面显示');
        }
    }

    /**
     * 初始化性能监控
     */
    initializePerformanceMonitoring() {
        if (typeof PerformanceMonitor !== 'undefined') {
            this.modules.set('performanceMonitor', new PerformanceMonitor());
        }
    }

    /**
     * 防抖函数
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 节流函数
     */
    throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * 获取模块实例
     */
    getModule(name) {
        return this.modules.get(name);
    }

    /**
     * 检查应用是否已初始化
     */
    isReady() {
        return this.isInitialized;
    }
}

// 平滑滚动类
class SmoothScroll {
    constructor() {
        this.init();
    }

    init() {
        // 为所有锚点链接添加平滑滚动
        const anchorLinks = document.querySelectorAll('a[href^="#"]');
        
        anchorLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                const href = link.getAttribute('href');
                if (href === '#') return;
                
                e.preventDefault();
                const target = document.querySelector(href);
                
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }
}

// 全局应用实例
let app;

// 初始化应用
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        app = new SkyMirrorApp();
    });
} else {
    app = new SkyMirrorApp();
}

// 导出到全局作用域
window.SkyMirrorApp = SkyMirrorApp;
window.app = app;
