/**
 * Sky Mirror World Tour - 导航功能模块
 * 处理导航栏的所有交互功能
 */

class Navigation {
    constructor() {
        this.mobileMenuBtn = null;
        this.mobileMenu = null;
        this.menuIcon = null;
        this.closeIcon = null;
        this.isMenuOpen = false;
        this.currentPage = '';
        
        this.init();
    }

    /**
     * 初始化导航功能
     */
    init() {
        // 获取DOM元素
        this.getDOMElements();
        
        // 绑定事件监听器
        this.bindEvents();
        
        // 设置当前页面高亮
        this.setActiveNavLink();
        
        // 初始化滚动监听
        this.initScrollListener();
        
        console.log('导航模块初始化完成');
    }

    /**
     * 获取DOM元素
     */
    getDOMElements() {
        this.mobileMenuBtn = document.getElementById('mobileMenuBtn');
        this.mobileMenu = document.getElementById('mobileMenu');
        this.menuIcon = document.getElementById('menuIcon');
        this.closeIcon = document.getElementById('closeIcon');
        this.navbar = document.querySelector('.navbar');
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 移动菜单切换按钮
        if (this.mobileMenuBtn) {
            this.mobileMenuBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleMobileMenu();
            });
        }

        // 移动菜单链接点击
        const mobileLinks = document.querySelectorAll('.mobile-nav-link');
        mobileLinks.forEach(link => {
            link.addEventListener('click', () => {
                this.closeMobileMenu();
            });
        });

        // 点击外部关闭菜单
        document.addEventListener('click', (e) => {
            if (this.isMenuOpen && !e.target.closest('.navbar')) {
                this.closeMobileMenu();
            }
        });

        // ESC键关闭菜单
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isMenuOpen) {
                this.closeMobileMenu();
            }
        });

        // 窗口大小改变时关闭移动菜单
        window.addEventListener('resize', () => {
            if (window.innerWidth >= 768 && this.isMenuOpen) {
                this.closeMobileMenu();
            }
        });

        // 导航链接点击追踪
        const navLinks = document.querySelectorAll('.nav-link, .mobile-nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                this.trackNavigation(link.getAttribute('href'), link.textContent.trim());
            });
        });
    }

    /**
     * 切换移动菜单
     */
    toggleMobileMenu() {
        this.isMenuOpen = !this.isMenuOpen;
        
        if (this.isMenuOpen) {
            this.openMobileMenu();
        } else {
            this.closeMobileMenu();
        }
    }

    /**
     * 打开移动菜单
     */
    openMobileMenu() {
        this.isMenuOpen = true;
        
        if (this.mobileMenu) {
            this.mobileMenu.classList.add('show');
        }
        
        // 切换图标
        if (this.menuIcon && this.closeIcon) {
            this.menuIcon.style.display = 'none';
            this.closeIcon.style.display = 'block';
        }
        
        // 防止背景滚动
        document.body.style.overflow = 'hidden';
        
        // 设置焦点到第一个菜单项
        const firstMenuItem = this.mobileMenu?.querySelector('.mobile-nav-link');
        if (firstMenuItem) {
            firstMenuItem.focus();
        }
        
        // 添加动画类
        if (this.mobileMenu) {
            this.mobileMenu.style.animation = 'slideDown 0.3s ease-out';
        }
    }

    /**
     * 关闭移动菜单
     */
    closeMobileMenu() {
        this.isMenuOpen = false;
        
        if (this.mobileMenu) {
            this.mobileMenu.classList.remove('show');
        }
        
        // 切换图标
        if (this.menuIcon && this.closeIcon) {
            this.menuIcon.style.display = 'block';
            this.closeIcon.style.display = 'none';
        }
        
        // 恢复背景滚动
        document.body.style.overflow = '';
        
        // 将焦点返回到菜单按钮
        if (this.mobileMenuBtn) {
            this.mobileMenuBtn.focus();
        }
    }

    /**
     * 设置当前页面的导航高亮
     */
    setActiveNavLink() {
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.nav-link, .mobile-nav-link');
        
        // 确定当前页面
        this.currentPage = this.getCurrentPageFromPath(currentPath);
        
        navLinks.forEach(link => {
            const linkPage = link.getAttribute('data-page');
            const linkPath = link.getAttribute('href');
            
            // 移除所有活动状态
            link.classList.remove('active');
            
            // 设置当前页面的活动状态
            if (this.isCurrentPage(currentPath, linkPath, linkPage)) {
                link.classList.add('active');
            }
        });
    }

    /**
     * 从路径获取当前页面标识
     */
    getCurrentPageFromPath(path) {
        if (path === '/' || path === '/index.html' || path.endsWith('/')) {
            return 'home';
        }
        
        const pathSegments = path.split('/');
        const fileName = pathSegments[pathSegments.length - 1];
        
        if (fileName.includes('.html')) {
            return fileName.replace('.html', '');
        }
        
        return pathSegments[pathSegments.length - 1] || 'home';
    }

    /**
     * 判断是否为当前页面
     */
    isCurrentPage(currentPath, linkPath, linkPage) {
        // 首页特殊处理
        if ((currentPath === '/' || currentPath === '/index.html') && linkPage === 'home') {
            return true;
        }
        
        // 精确匹配
        if (currentPath === linkPath) {
            return true;
        }
        
        // 页面标识匹配
        if (linkPage && this.currentPage === linkPage) {
            return true;
        }
        
        return false;
    }

    /**
     * 初始化滚动监听
     */
    initScrollListener() {
        let ticking = false;
        
        const updateNavbar = () => {
            if (this.navbar) {
                if (window.scrollY > 50) {
                    this.navbar.classList.add('scrolled');
                } else {
                    this.navbar.classList.remove('scrolled');
                }
            }
            ticking = false;
        };
        
        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(updateNavbar);
                ticking = true;
            }
        });
    }

    /**
     * 追踪导航点击
     */
    trackNavigation(href, linkText) {
        // 发送分析数据
        if (typeof gtag !== 'undefined') {
            gtag('event', 'navigation_click', {
                'link_url': href,
                'link_text': linkText,
                'current_page': this.currentPage
            });
        }
        
        console.log('导航点击:', { href, linkText, currentPage: this.currentPage });
    }

    /**
     * 处理窗口大小改变
     */
    handleResize() {
        // 如果切换到桌面视图，关闭移动菜单
        if (window.innerWidth >= 768 && this.isMenuOpen) {
            this.closeMobileMenu();
        }
    }

    /**
     * 获取当前页面标识
     */
    getCurrentPage() {
        return this.currentPage;
    }

    /**
     * 手动设置活动导航项
     */
    setActiveNavItem(pageId) {
        const navLinks = document.querySelectorAll('.nav-link, .mobile-nav-link');
        
        navLinks.forEach(link => {
            const linkPage = link.getAttribute('data-page');
            
            if (linkPage === pageId) {
                link.classList.add('active');
            } else {
                link.classList.remove('active');
            }
        });
        
        this.currentPage = pageId;
    }

    /**
     * 添加导航动画样式
     */
    addAnimationStyles() {
        if (!document.getElementById('nav-animations')) {
            const style = document.createElement('style');
            style.id = 'nav-animations';
            style.textContent = `
                @keyframes slideDown {
                    from {
                        opacity: 0;
                        transform: translateY(-10px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }
                
                @keyframes fadeIn {
                    from { opacity: 0; }
                    to { opacity: 1; }
                }
                
                .mobile-menu {
                    animation: fadeIn 0.3s ease-out;
                }
                
                .mobile-nav-link {
                    transition: all 0.2s ease;
                }
                
                .mobile-nav-link:hover {
                    transform: translateX(4px);
                }
                
                .nav-link svg {
                    transition: transform 0.2s ease;
                }
                
                .nav-link:hover svg {
                    transform: scale(1.1);
                }
            `;
            document.head.appendChild(style);
        }
    }

    /**
     * 销毁导航实例
     */
    destroy() {
        // 移除事件监听器
        if (this.mobileMenuBtn) {
            this.mobileMenuBtn.removeEventListener('click', this.toggleMobileMenu);
        }
        
        // 关闭移动菜单
        this.closeMobileMenu();
        
        // 清理引用
        this.mobileMenuBtn = null;
        this.mobileMenu = null;
        this.menuIcon = null;
        this.closeIcon = null;
        this.navbar = null;
        
        console.log('导航模块已销毁');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Navigation;
} else {
    window.Navigation = Navigation;
}
