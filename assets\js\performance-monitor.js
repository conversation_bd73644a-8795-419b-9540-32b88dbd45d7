/**
 * Sky Mirror World Tour - 性能监控模块
 * 监控页面性能指标，提供性能优化建议
 */

class PerformanceMonitor {
    constructor() {
        this.metrics = {};
        this.isSupported = 'performance' in window;
        this.observers = [];
        
        this.init();
    }

    /**
     * 初始化性能监控
     */
    init() {
        if (!this.isSupported) {
            console.warn('Performance API 不支持');
            return;
        }

        // 监控页面加载性能
        this.monitorPageLoad();
        
        // 监控核心 Web 指标
        this.monitorCoreWebVitals();
        
        // 监控资源加载
        this.monitorResourceLoading();
        
        // 监控用户交互
        this.monitorUserInteractions();
        
        console.log('性能监控模块初始化完成');
    }

    /**
     * 监控页面加载性能
     */
    monitorPageLoad() {
        window.addEventListener('load', () => {
            setTimeout(() => {
                this.collectPageLoadMetrics();
            }, 0);
        });
    }

    /**
     * 收集页面加载指标
     */
    collectPageLoadMetrics() {
        const navigation = performance.getEntriesByType('navigation')[0];
        
        if (navigation) {
            this.metrics.pageLoad = {
                // DNS 查询时间
                dnsLookup: navigation.domainLookupEnd - navigation.domainLookupStart,
                
                // TCP 连接时间
                tcpConnect: navigation.connectEnd - navigation.connectStart,
                
                // 请求响应时间
                requestResponse: navigation.responseEnd - navigation.requestStart,
                
                // DOM 解析时间
                domParsing: navigation.domContentLoadedEventEnd - navigation.responseEnd,
                
                // 资源加载时间
                resourceLoading: navigation.loadEventStart - navigation.domContentLoadedEventEnd,
                
                // 总加载时间
                totalLoad: navigation.loadEventEnd - navigation.navigationStart,
                
                // 首字节时间 (TTFB)
                ttfb: navigation.responseStart - navigation.navigationStart,
                
                // DOM 准备时间
                domReady: navigation.domContentLoadedEventEnd - navigation.navigationStart
            };
            
            this.analyzePageLoadPerformance();
        }
    }

    /**
     * 监控核心 Web 指标
     */
    monitorCoreWebVitals() {
        // 监控 LCP (Largest Contentful Paint)
        this.observeLCP();
        
        // 监控 FID (First Input Delay)
        this.observeFID();
        
        // 监控 CLS (Cumulative Layout Shift)
        this.observeCLS();
        
        // 监控 FCP (First Contentful Paint)
        this.observeFCP();
    }

    /**
     * 观察 LCP
     */
    observeLCP() {
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                const lastEntry = entries[entries.length - 1];
                
                this.metrics.lcp = {
                    value: lastEntry.startTime,
                    element: lastEntry.element,
                    url: lastEntry.url
                };
                
                this.analyzeLCP();
            });
            
            observer.observe({ entryTypes: ['largest-contentful-paint'] });
            this.observers.push(observer);
        }
    }

    /**
     * 观察 FID
     */
    observeFID() {
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                
                entries.forEach(entry => {
                    this.metrics.fid = {
                        value: entry.processingStart - entry.startTime,
                        name: entry.name,
                        startTime: entry.startTime
                    };
                    
                    this.analyzeFID();
                });
            });
            
            observer.observe({ entryTypes: ['first-input'] });
            this.observers.push(observer);
        }
    }

    /**
     * 观察 CLS
     */
    observeCLS() {
        if ('PerformanceObserver' in window) {
            let clsValue = 0;
            let sessionValue = 0;
            let sessionEntries = [];
            
            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                
                entries.forEach(entry => {
                    if (!entry.hadRecentInput) {
                        const firstSessionEntry = sessionEntries[0];
                        const lastSessionEntry = sessionEntries[sessionEntries.length - 1];
                        
                        if (sessionValue && 
                            entry.startTime - lastSessionEntry.startTime < 1000 &&
                            entry.startTime - firstSessionEntry.startTime < 5000) {
                            sessionValue += entry.value;
                            sessionEntries.push(entry);
                        } else {
                            sessionValue = entry.value;
                            sessionEntries = [entry];
                        }
                        
                        if (sessionValue > clsValue) {
                            clsValue = sessionValue;
                        }
                    }
                });
                
                this.metrics.cls = {
                    value: clsValue,
                    entries: sessionEntries
                };
                
                this.analyzeCLS();
            });
            
            observer.observe({ entryTypes: ['layout-shift'] });
            this.observers.push(observer);
        }
    }

    /**
     * 观察 FCP
     */
    observeFCP() {
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                
                entries.forEach(entry => {
                    if (entry.name === 'first-contentful-paint') {
                        this.metrics.fcp = {
                            value: entry.startTime
                        };
                        
                        this.analyzeFCP();
                    }
                });
            });
            
            observer.observe({ entryTypes: ['paint'] });
            this.observers.push(observer);
        }
    }

    /**
     * 监控资源加载
     */
    monitorResourceLoading() {
        window.addEventListener('load', () => {
            const resources = performance.getEntriesByType('resource');
            
            this.metrics.resources = {
                total: resources.length,
                images: resources.filter(r => r.initiatorType === 'img').length,
                scripts: resources.filter(r => r.initiatorType === 'script').length,
                stylesheets: resources.filter(r => r.initiatorType === 'link').length,
                slowResources: resources.filter(r => r.duration > 1000)
            };
            
            this.analyzeResourcePerformance();
        });
    }

    /**
     * 监控用户交互
     */
    monitorUserInteractions() {
        let interactionCount = 0;
        
        ['click', 'keydown', 'scroll'].forEach(eventType => {
            document.addEventListener(eventType, () => {
                interactionCount++;
            }, { passive: true });
        });
        
        // 每30秒记录一次交互数据
        setInterval(() => {
            this.metrics.interactions = {
                count: interactionCount,
                rate: interactionCount / 30 // 每秒交互次数
            };
            interactionCount = 0;
        }, 30000);
    }

    /**
     * 分析页面加载性能
     */
    analyzePageLoadPerformance() {
        const { pageLoad } = this.metrics;
        const suggestions = [];
        
        if (pageLoad.ttfb > 600) {
            suggestions.push('TTFB 过高，考虑优化服务器响应时间');
        }
        
        if (pageLoad.domParsing > 1000) {
            suggestions.push('DOM 解析时间过长，考虑减少 DOM 复杂度');
        }
        
        if (pageLoad.resourceLoading > 2000) {
            suggestions.push('资源加载时间过长，考虑优化资源大小或使用 CDN');
        }
        
        this.metrics.pageLoad.suggestions = suggestions;
        this.logPerformanceData('页面加载', pageLoad);
    }

    /**
     * 分析 LCP
     */
    analyzeLCP() {
        const { lcp } = this.metrics;
        let rating = 'good';
        
        if (lcp.value > 4000) {
            rating = 'poor';
        } else if (lcp.value > 2500) {
            rating = 'needs-improvement';
        }
        
        lcp.rating = rating;
        this.logPerformanceData('LCP', lcp);
    }

    /**
     * 分析 FID
     */
    analyzeFID() {
        const { fid } = this.metrics;
        let rating = 'good';
        
        if (fid.value > 300) {
            rating = 'poor';
        } else if (fid.value > 100) {
            rating = 'needs-improvement';
        }
        
        fid.rating = rating;
        this.logPerformanceData('FID', fid);
    }

    /**
     * 分析 CLS
     */
    analyzeCLS() {
        const { cls } = this.metrics;
        let rating = 'good';
        
        if (cls.value > 0.25) {
            rating = 'poor';
        } else if (cls.value > 0.1) {
            rating = 'needs-improvement';
        }
        
        cls.rating = rating;
        this.logPerformanceData('CLS', cls);
    }

    /**
     * 分析 FCP
     */
    analyzeFCP() {
        const { fcp } = this.metrics;
        let rating = 'good';
        
        if (fcp.value > 3000) {
            rating = 'poor';
        } else if (fcp.value > 1800) {
            rating = 'needs-improvement';
        }
        
        fcp.rating = rating;
        this.logPerformanceData('FCP', fcp);
    }

    /**
     * 分析资源性能
     */
    analyzeResourcePerformance() {
        const { resources } = this.metrics;
        const suggestions = [];
        
        if (resources.slowResources.length > 0) {
            suggestions.push(`发现 ${resources.slowResources.length} 个加载缓慢的资源`);
        }
        
        if (resources.images > 20) {
            suggestions.push('图片数量较多，考虑使用懒加载');
        }
        
        if (resources.scripts > 10) {
            suggestions.push('JavaScript 文件较多，考虑合并或按需加载');
        }
        
        resources.suggestions = suggestions;
        this.logPerformanceData('资源加载', resources);
    }

    /**
     * 记录性能数据
     */
    logPerformanceData(type, data) {
        if (process.env.NODE_ENV === 'development') {
            console.group(`🚀 性能监控 - ${type}`);
            console.log(data);
            console.groupEnd();
        }
        
        // 在生产环境中，可以将数据发送到分析服务
        this.sendToAnalytics(type, data);
    }

    /**
     * 发送数据到分析服务
     */
    sendToAnalytics(type, data) {
        // 这里可以集成 Google Analytics、百度统计等
        if (typeof gtag !== 'undefined') {
            gtag('event', 'performance_metric', {
                metric_type: type,
                metric_value: JSON.stringify(data)
            });
        }
    }

    /**
     * 获取性能报告
     */
    getPerformanceReport() {
        return {
            timestamp: new Date().toISOString(),
            url: window.location.href,
            userAgent: navigator.userAgent,
            metrics: this.metrics
        };
    }

    /**
     * 销毁性能监控
     */
    destroy() {
        this.observers.forEach(observer => {
            observer.disconnect();
        });
        this.observers = [];
        this.metrics = {};
        
        console.log('性能监控模块已销毁');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PerformanceMonitor;
} else {
    window.PerformanceMonitor = PerformanceMonitor;
}
