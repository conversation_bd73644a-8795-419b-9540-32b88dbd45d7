/**
 * Sky Mirror World Tour - 特色船票页面交互功能
 * 处理选项卡切换、表单处理、预订功能等
 */

class SignatureTicketsPage {
    constructor() {
        this.activeTab = 'sky-mirror';
        this.selectedDate = '';
        this.selectedTime = '';
        this.experiences = {
            'sky-mirror': {
                title: '天空之镜',
                description: '在这里，天空与海水完美融合，创造出令人叹为观止的镜面效果。',
                image: 'https://images.pexels.com/photos/1680247/pexels-photo-1680247.jpeg',
                duration: '4-5小时',
                bestTime: '早上6:00-10:00',
                location: '瓜拉雪兰莪',
                includes: ['往返船票', '专业向导', '救生衣', '拍照服务'],
                excludes: ['餐食', '个人消费', '小费'],
                tips: ['建议穿着鲜艳色彩的衣服', '请准备防晒用品', '建议携带帽子和太阳镜'],
                timeSlots: ['06:00', '07:00', '08:00', '09:00']
            },
            'eagle-feeding': {
                title: '喂食老鹰',
                description: '观看壮观的老鹰觅食场面，感受大自然的原始魅力。',
                image: 'https://images.pexels.com/photos/792416/pexels-photo-792416.jpeg',
                duration: '2-3小时',
                bestTime: '下午3:00-6:00',
                location: '瓜拉雪兰莪红树林',
                includes: ['往返船票', '专业向导', '救生衣', '老鹰食物'],
                excludes: ['餐食', '个人消费', '小费'],
                tips: ['请保持安静，不要惊扰老鹰', '建议携带望远镜', '请勿使用闪光灯拍照'],
                timeSlots: ['15:00', '16:00']
            },
            'firefly': {
                title: '萤火虫观赏',
                description: '夜晚的红树林中，成千上万的萤火虫如繁星般闪烁。',
                image: 'https://images.pexels.com/photos/1366919/pexels-photo-1366919.jpeg',
                duration: '2-3小时',
                bestTime: '晚上7:30-9:30',
                location: '瓜拉雪兰莪红树林',
                includes: ['往返船票', '专业向导', '救生衣', '手电筒'],
                excludes: ['餐食', '个人消费', '小费'],
                tips: ['请勿使用闪光灯或强光', '建议穿着长袖衣物防蚊', '请保持安静'],
                timeSlots: ['19:30', '20:00']
            },
            'mangrove': {
                title: '红树林探索',
                description: '探索神秘的红树林生态系统，了解这片独特湿地的生物多样性。',
                image: 'https://images.pexels.com/photos/1624438/pexels-photo-1624438.jpeg',
                duration: '3-4小时',
                bestTime: '上午9:00-下午5:00',
                location: '瓜拉雪兰莪红树林',
                includes: ['往返船票', '专业向导', '救生衣', '生态讲解'],
                excludes: ['餐食', '个人消费', '小费'],
                tips: ['建议穿着舒适的鞋子', '请准备防蚊用品', '建议携带饮用水'],
                timeSlots: ['09:00', '10:00', '14:00', '15:00']
            }
        };
        
        this.init();
    }

    /**
     * 初始化页面功能
     */
    init() {
        // 设置选项卡导航
        this.setupTabNavigation();
        
        // 设置预订表单
        this.setupBookingForm();
        
        // 更新体验内容
        this.updateExperienceContent();
        
        // 设置日期输入
        this.setupDateInput();
        
        // 设置时间选择
        this.setupTimeSelection();
        
        console.log('特色船票页面初始化完成');
    }

    /**
     * 设置选项卡导航
     */
    setupTabNavigation() {
        const tabButtons = document.querySelectorAll('[data-tab]');
        
        tabButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const tabId = button.getAttribute('data-tab');
                this.switchTab(tabId);
            });
        });
    }

    /**
     * 切换选项卡
     */
    switchTab(tabId) {
        if (this.experiences[tabId]) {
            this.activeTab = tabId;
            this.selectedTime = ''; // 重置时间选择
            
            // 更新选项卡按钮状态
            this.updateTabButtons();
            
            // 更新体验内容
            this.updateExperienceContent();
            
            // 更新时间选择
            this.updateTimeSlots();
            
            // 更新背景图片
            this.updateHeroBackground();
        }
    }

    /**
     * 更新选项卡按钮状态
     */
    updateTabButtons() {
        const tabButtons = document.querySelectorAll('[data-tab]');
        
        tabButtons.forEach(button => {
            const tabId = button.getAttribute('data-tab');
            if (tabId === this.activeTab) {
                button.classList.add('active');
            } else {
                button.classList.remove('active');
            }
        });
    }

    /**
     * 更新体验内容
     */
    updateExperienceContent() {
        const experience = this.experiences[this.activeTab];
        
        // 更新标题和描述
        const titleElement = document.getElementById('experience-title');
        const descriptionElement = document.getElementById('experience-description');
        
        if (titleElement) titleElement.textContent = experience.title;
        if (descriptionElement) descriptionElement.textContent = experience.description;
        
        // 更新关键信息
        this.updateKeyInfo(experience);
        
        // 更新包含/不包含列表
        this.updateIncludesExcludes(experience);
        
        // 更新温馨提示
        this.updateTips(experience);
        
        // 更新图片画廊
        this.updatePhotoGallery(experience);
    }

    /**
     * 更新关键信息
     */
    updateKeyInfo(experience) {
        const durationElement = document.getElementById('experience-duration');
        const bestTimeElement = document.getElementById('experience-best-time');
        const locationElement = document.getElementById('experience-location');
        
        if (durationElement) durationElement.textContent = experience.duration;
        if (bestTimeElement) bestTimeElement.textContent = experience.bestTime;
        if (locationElement) locationElement.textContent = experience.location;
    }

    /**
     * 更新包含/不包含列表
     */
    updateIncludesExcludes(experience) {
        const includesList = document.getElementById('includes-list');
        const excludesList = document.getElementById('excludes-list');
        
        if (includesList) {
            includesList.innerHTML = experience.includes.map(item => 
                `<li class="flex items-center space-x-2">
                    <div class="list-dot emerald"></div>
                    <span class="list-text">${item}</span>
                </li>`
            ).join('');
        }
        
        if (excludesList) {
            excludesList.innerHTML = experience.excludes.map(item => 
                `<li class="flex items-center space-x-2">
                    <div class="list-dot red"></div>
                    <span class="list-text">${item}</span>
                </li>`
            ).join('');
        }
    }

    /**
     * 更新温馨提示
     */
    updateTips(experience) {
        const tipsList = document.getElementById('tips-list');
        
        if (tipsList) {
            tipsList.innerHTML = experience.tips.map(tip => 
                `<li class="flex items-start space-x-2">
                    <div class="list-dot yellow"></div>
                    <span class="list-text">${tip}</span>
                </li>`
            ).join('');
        }
    }

    /**
     * 更新图片画廊
     */
    updatePhotoGallery(experience) {
        const gallery = document.getElementById('photo-gallery');
        
        if (gallery) {
            gallery.innerHTML = [1, 2, 3].map(i => 
                `<div class="gallery-item">
                    <img src="${experience.image}?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop" 
                         alt="${experience.title} ${i}"
                         class="gallery-image">
                </div>`
            ).join('');
        }
    }

    /**
     * 更新时间选择
     */
    updateTimeSlots() {
        const timeSlotsContainer = document.getElementById('time-slots');
        const experience = this.experiences[this.activeTab];
        
        if (timeSlotsContainer) {
            timeSlotsContainer.innerHTML = experience.timeSlots.map(time => 
                `<button type="button" 
                         class="time-slot-btn ${this.selectedTime === time ? 'selected' : ''}" 
                         data-time="${time}">
                    ${time}
                </button>`
            ).join('');
            
            // 重新绑定时间选择事件
            this.setupTimeSelection();
        }
    }

    /**
     * 设置时间选择
     */
    setupTimeSelection() {
        const timeButtons = document.querySelectorAll('.time-slot-btn');
        
        timeButtons.forEach(button => {
            button.addEventListener('click', () => {
                const time = button.getAttribute('data-time');
                this.selectTime(time);
            });
        });
    }

    /**
     * 选择时间
     */
    selectTime(time) {
        this.selectedTime = time;
        
        // 更新按钮状态
        const timeButtons = document.querySelectorAll('.time-slot-btn');
        timeButtons.forEach(button => {
            const buttonTime = button.getAttribute('data-time');
            if (buttonTime === time) {
                button.classList.add('selected');
            } else {
                button.classList.remove('selected');
            }
        });
    }

    /**
     * 更新Hero背景
     */
    updateHeroBackground() {
        const heroBackground = document.getElementById('hero-background');
        const experience = this.experiences[this.activeTab];
        
        if (heroBackground) {
            heroBackground.style.backgroundImage = `url(${experience.image})`;
        }
    }

    /**
     * 设置预订表单
     */
    setupBookingForm() {
        const bookingForm = document.getElementById('booking-form');
        const dateInput = document.getElementById('booking-date');
        
        if (bookingForm) {
            bookingForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleBooking();
            });
        }
        
        if (dateInput) {
            dateInput.addEventListener('change', (e) => {
                this.selectedDate = e.target.value;
            });
        }
    }

    /**
     * 设置日期输入
     */
    setupDateInput() {
        const dateInput = document.getElementById('booking-date');
        
        if (dateInput) {
            // 设置最小日期为今天
            const today = new Date().toISOString().split('T')[0];
            dateInput.min = today;
        }
    }

    /**
     * 处理预订
     */
    handleBooking() {
        // 获取表单数据
        const formData = this.getFormData();
        
        // 验证表单
        if (!this.validateForm(formData)) {
            return;
        }
        
        // 显示预订确认
        this.showBookingConfirmation(formData);
        
        // 重置表单
        this.resetBookingForm();
    }

    /**
     * 获取表单数据
     */
    getFormData() {
        const experience = this.experiences[this.activeTab];
        
        return {
            experience: experience.title,
            date: document.getElementById('booking-date')?.value || '',
            time: this.selectedTime,
            passengerCount: document.getElementById('passenger-count')?.value || '1',
            contactName: document.getElementById('contact-name')?.value || '',
            contactPhone: document.getElementById('contact-phone')?.value || '',
            specialRequirements: document.getElementById('special-requirements')?.value || ''
        };
    }

    /**
     * 验证表单
     */
    validateForm(formData) {
        if (!formData.date) {
            this.showAlert('请选择日期', 'error');
            return false;
        }
        
        if (!formData.time) {
            this.showAlert('请选择时间', 'error');
            return false;
        }
        
        if (!formData.contactName) {
            this.showAlert('请填写联系人姓名', 'error');
            return false;
        }
        
        if (!formData.contactPhone) {
            this.showAlert('请填写联系电话', 'error');
            return false;
        }
        
        return true;
    }

    /**
     * 显示预订确认
     */
    showBookingConfirmation(formData) {
        const message = `预订成功！
        
体验项目：${formData.experience}
日期：${formData.date}
时间：${formData.time}
人数：${formData.passengerCount}人
联系人：${formData.contactName}
联系电话：${formData.contactPhone}

我们将尽快与您联系确认详情。`;
        
        this.showAlert(message, 'success');
    }

    /**
     * 重置预订表单
     */
    resetBookingForm() {
        this.selectedDate = '';
        this.selectedTime = '';
        
        const form = document.getElementById('booking-form');
        if (form) {
            form.reset();
        }
        
        // 清除时间选择
        const timeButtons = document.querySelectorAll('.time-slot-btn');
        timeButtons.forEach(button => button.classList.remove('selected'));
        
        // 重新设置日期最小值
        this.setupDateInput();
    }

    /**
     * 显示提示信息
     */
    showAlert(message, type = 'info') {
        // 创建提示框
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert-notification alert-${type}`;
        alertDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 1.5rem;
            border-radius: 0.5rem;
            color: white;
            font-weight: 600;
            z-index: 1000;
            max-width: 400px;
            word-wrap: break-word;
            white-space: pre-line;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            ${type === 'success' ? 'background: #10b981;' : ''}
            ${type === 'error' ? 'background: #ef4444;' : ''}
            ${type === 'warning' ? 'background: #f59e0b;' : ''}
            ${type === 'info' ? 'background: #3b82f6;' : ''}
        `;
        
        alertDiv.textContent = message;
        document.body.appendChild(alertDiv);
        
        // 添加进入动画
        alertDiv.style.transform = 'translateX(100%)';
        alertDiv.style.transition = 'transform 0.3s ease';
        
        setTimeout(() => {
            alertDiv.style.transform = 'translateX(0)';
        }, 10);
        
        // 5秒后自动移除
        setTimeout(() => {
            alertDiv.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 300);
        }, 5000);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new SignatureTicketsPage();
});

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SignatureTicketsPage;
} else {
    window.SignatureTicketsPage = SignatureTicketsPage;
}
