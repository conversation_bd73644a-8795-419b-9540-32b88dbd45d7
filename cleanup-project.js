/**
 * Sky Mirror World Tour - 项目清理脚本
 * 清理不再需要的文件和目录
 */

const fs = require('fs');
const path = require('path');

// 需要删除的文件和目录
const filesToDelete = [
    // React 源文件
    'src/',
    
    // 构建工具配置
    'vite.config.ts',
    'tsconfig.json',
    'tsconfig.app.json',
    'tsconfig.node.json',
    'tailwind.config.js',
    'postcss.config.js',
    'eslint.config.js',
    
    // 包管理文件
    'package.json',
    'package-lock.json',
    
    // 示例文件
    'navigation-example.html',
    'signature-tickets-example.js',
    
    // 重复的文档文件（已移动到 docs/ 目录）
    'css-conversion-guide.md',
    'deployment-guide.md',
    'performance-optimization.md',
    'testing-checklist.md',
    
    // 临时脚本文件
    'fix-navigation.js',
    'cleanup-react-files.js'
];

// 需要保留的文件
const filesToKeep = [
    'index.html',
    'assets/',
    'pages/',
    'components/',
    'docs/',
    'sw.js',
    'performance-optimization.config.js',
    'PROJECT-COMPLETION-SUMMARY.md',
    'cleanup-project.js'
];

/**
 * 递归删除目录
 */
function deleteDirectory(dirPath) {
    if (!fs.existsSync(dirPath)) {
        return;
    }
    
    const files = fs.readdirSync(dirPath);
    
    for (const file of files) {
        const filePath = path.join(dirPath, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory()) {
            deleteDirectory(filePath);
        } else {
            fs.unlinkSync(filePath);
            console.log(`删除文件: ${filePath}`);
        }
    }
    
    fs.rmdirSync(dirPath);
    console.log(`删除目录: ${dirPath}`);
}

/**
 * 删除文件
 */
function deleteFile(filePath) {
    if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        console.log(`删除文件: ${filePath}`);
    }
}

/**
 * 检查文件是否应该被删除
 */
function shouldDelete(filePath) {
    const relativePath = path.relative(process.cwd(), filePath);
    
    // 检查是否在保留列表中
    for (const keepPattern of filesToKeep) {
        if (relativePath.startsWith(keepPattern) || 
            relativePath === keepPattern ||
            (keepPattern.endsWith('/') && relativePath.startsWith(keepPattern))) {
            return false;
        }
    }
    
    // 检查是否在删除列表中
    for (const deletePattern of filesToDelete) {
        if (relativePath.startsWith(deletePattern) || 
            relativePath === deletePattern ||
            (deletePattern.endsWith('/') && relativePath.startsWith(deletePattern))) {
            return true;
        }
    }
    
    return false;
}

/**
 * 扫描并删除文件
 */
function scanAndDelete(dirPath = process.cwd()) {
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
        const itemPath = path.join(dirPath, item);
        
        // 跳过隐藏文件和当前脚本
        if (item.startsWith('.') || item === 'cleanup-project.js') {
            continue;
        }
        
        if (shouldDelete(itemPath)) {
            const stat = fs.statSync(itemPath);
            
            if (stat.isDirectory()) {
                deleteDirectory(itemPath);
            } else {
                deleteFile(itemPath);
            }
        }
    }
}

/**
 * 创建新的 README.md
 */
function createNewReadme() {
    const readmeContent = `# Sky Mirror World Tour - 静态网站

## 🌊 项目简介

Sky Mirror World Tour 是一个专业的马来西亚旅游服务网站，提供天空之镜、境内用车、海岛旅游等多样化服务。

本项目已从 React 应用重构为高性能的静态网站，提供更快的加载速度和更好的 SEO 表现。

## 🚀 快速开始

### 本地开发

\`\`\`bash
# 使用 Python 启动本地服务器
python -m http.server 8000

# 或使用 Node.js serve
npx serve .

# 或使用 PHP
php -S localhost:8000
\`\`\`

访问 http://localhost:8000 查看网站。

### 部署

项目可以部署到任何静态文件托管服务：

- **Netlify**: 拖拽文件夹到 Netlify
- **Vercel**: 连接 Git 仓库自动部署
- **GitHub Pages**: 推送到 gh-pages 分支
- **传统服务器**: 上传所有文件到 web 目录

## 📁 项目结构

\`\`\`
sky-mirror-world-tour/
├── index.html                 # 首页
├── assets/                    # 静态资源
│   ├── css/                   # 样式文件
│   │   ├── main.css          # 主样式
│   │   ├── components.css    # 组件样式
│   │   └── responsive.css    # 响应式样式
│   └── js/                   # JavaScript 文件
│       ├── main.js           # 主应用逻辑
│       ├── navigation.js     # 导航功能
│       ├── signature-tickets.js  # 特色船票功能
│       ├── contact-form.js   # 联系表单
│       ├── lazy-loader.js    # 懒加载模块
│       └── performance-monitor.js  # 性能监控
├── pages/                    # 页面文件
│   ├── signature-tickets.html
│   ├── contact.html
│   ├── about.html
│   ├── domestic-transport.html
│   └── sky-pier-cafe.html
├── components/               # 组件模板
│   ├── navbar.html
│   └── footer.html
├── docs/                     # 文档
│   ├── responsive-testing-guide.md
│   └── functionality-testing-guide.md
├── sw.js                     # Service Worker
└── performance-optimization.config.js  # 性能配置
\`\`\`

## ✨ 主要功能

- 🎯 **响应式设计**: 完美适配所有设备
- ⚡ **高性能**: 优化的加载速度和用户体验
- 🔍 **SEO 友好**: 静态 HTML 结构，搜索引擎友好
- 📱 **移动优先**: 移动端优先的设计理念
- 🎨 **现代 UI**: 美观的用户界面设计
- 🚀 **PWA 支持**: Service Worker 缓存策略
- 📊 **性能监控**: 内置性能监控和分析

## 🛠️ 技术栈

- **HTML5**: 语义化标签和现代 HTML 特性
- **CSS3**: 自定义 CSS 框架，替代 Tailwind
- **JavaScript ES6+**: 现代 JavaScript 特性
- **Service Worker**: 缓存策略和离线支持
- **响应式设计**: 移动端优先的设计方法

## 📋 浏览器支持

- Chrome (最新版本)
- Firefox (最新版本)
- Safari (最新版本)
- Edge (最新版本)
- 移动端浏览器

## 📞 联系我们

- **网站**: https://skymirror.com
- **邮箱**: <EMAIL>
- **电话**: +60 12-345-6789

## 📄 许可证

MIT License

## 🎉 重构完成

本项目已成功从 React 应用重构为静态网站，实现了：
- ✅ 100% 功能保留
- ✅ 性能显著提升
- ✅ SEO 优化
- ✅ 维护成本降低
- ✅ 部署简化
`;

    fs.writeFileSync('README.md', readmeContent, 'utf8');
    console.log('创建新的 README.md');
}

/**
 * 创建 .gitignore 文件
 */
function createGitignore() {
    const gitignoreContent = `# 依赖
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 构建输出
dist/
build/

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 编辑器
.vscode/
.idea/
*.swp
*.swo

# 操作系统
.DS_Store
Thumbs.db

# 日志
*.log

# 临时文件
*.tmp
*.temp

# 缓存
.cache/
.parcel-cache/

# 测试覆盖率
coverage/

# 备份文件
*.bak
*.backup
`;

    fs.writeFileSync('.gitignore', gitignoreContent, 'utf8');
    console.log('创建新的 .gitignore');
}

/**
 * 主清理函数
 */
function cleanup() {
    console.log('🧹 开始清理项目文件...\n');
    
    try {
        // 扫描并删除文件
        scanAndDelete();
        
        console.log('\n📝 创建新的项目文件...\n');
        
        // 创建新的项目文件
        createNewReadme();
        createGitignore();
        
        console.log('\n✅ 项目清理完成！');
        console.log('\n📋 清理总结:');
        console.log('- 删除了所有 React 相关文件');
        console.log('- 删除了构建工具配置文件');
        console.log('- 删除了重复的文档文件');
        console.log('- 删除了临时脚本文件');
        console.log('- 保留了静态网站核心文件');
        console.log('- 创建了新的项目配置文件');
        
        console.log('\n🚀 项目已准备好部署！');
        
    } catch (error) {
        console.error('❌ 清理过程中出现错误:', error);
        process.exit(1);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    console.log('⚠️  警告: 此操作将删除不再需要的文件！');
    console.log('请确保您已经备份了重要文件。');
    console.log('');
    console.log('将要删除的文件类型:');
    filesToDelete.forEach(file => console.log(`  - ${file}`));
    console.log('');
    
    // 执行清理
    cleanup();
}

module.exports = {
    cleanup,
    shouldDelete,
    filesToDelete,
    filesToKeep
};
