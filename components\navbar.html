<!-- Sky Mirror World Tour - 导航栏组件 -->
<nav class="navbar">
    <div class="nav-container">
        <!-- 品牌Logo -->
        <a href="/" class="nav-brand">
            <div class="nav-logo">S</div>
            <span class="nav-title">Sky Mirror World Tour</span>
        </a>

        <!-- 桌面导航菜单 -->
        <div class="nav-menu">
            <a href="/" class="nav-link" data-page="home">
                <span>首页</span>
            </a>
            <a href="/pages/domestic-transport.html" class="nav-link" data-page="domestic-transport">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9L18 10V6c0-2-2-4-4-4H4c-2 0-4 2-4 4v10c0 .6.4 1 1 1h2"/>
                    <circle cx="7" cy="17" r="2"/>
                    <path d="M9 17h6"/>
                    <circle cx="17" cy="17" r="2"/>
                </svg>
                <span>境内用车</span>
            </a>
            <a href="/pages/signature-tickets.html" class="nav-link" data-page="signature-tickets">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M2 9a3 3 0 0 1 0 6v2a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-2a3 3 0 0 1 0-6V7a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v2Z"/>
                    <path d="M13 5v2"/>
                    <path d="M13 17v2"/>
                    <path d="M13 11v2"/>
                </svg>
                <span>特色船票</span>
            </a>
            <a href="/pages/international-tours.html" class="nav-link" data-page="international-tours">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="12" cy="12" r="10"/>
                    <path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20"/>
                    <path d="M2 12h20"/>
                </svg>
                <span>国际旅游</span>
            </a>
            <a href="/pages/business-reception.html" class="nav-link" data-page="business-reception">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z"/>
                    <path d="M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2"/>
                    <path d="M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2"/>
                    <path d="M10 6h4"/>
                    <path d="M10 10h4"/>
                    <path d="M10 14h4"/>
                    <path d="M10 18h4"/>
                </svg>
                <span>商务接待</span>
            </a>
            <a href="/pages/island-hopping.html" class="nav-link" data-page="island-hopping">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M2 6s1.5-2 5-2 5 2 5 2v14s-1.5-2-5-2-5 2-5 2V6Z"/>
                    <path d="M12 6s1.5-2 5-2 5 2 5 2v14s-1.5-2-5-2-5 2-5 2V6Z"/>
                </svg>
                <span>海岛旅游</span>
            </a>
            <a href="/pages/sky-pier-cafe.html" class="nav-link" data-page="sky-pier-cafe">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M18 8h1a4 4 0 0 1 0 8h-1"/>
                    <path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8Z"/>
                    <line x1="6" y1="1" x2="6" y2="4"/>
                    <line x1="10" y1="1" x2="10" y2="4"/>
                    <line x1="14" y1="1" x2="14" y2="4"/>
                </svg>
                <span>天空号船咖啡厅</span>
            </a>
            <a href="/pages/about.html" class="nav-link" data-page="about">关于我们</a>
            <a href="/pages/contact.html" class="nav-cta">联系我们</a>
        </div>

        <!-- 移动端菜单按钮 -->
        <button class="mobile-menu-btn" id="mobileMenuBtn" aria-label="打开菜单">
            <svg id="menuIcon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="4" y1="6" x2="20" y2="6"/>
                <line x1="4" y1="12" x2="20" y2="12"/>
                <line x1="4" y1="18" x2="20" y2="18"/>
            </svg>
            <svg id="closeIcon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="display: none;">
                <path d="M18 6 6 18"/>
                <path d="m6 6 12 12"/>
            </svg>
        </button>
    </div>

    <!-- 移动端菜单 -->
    <div class="mobile-menu" id="mobileMenu">
        <a href="/" class="mobile-nav-link" data-page="home">
            <span>首页</span>
        </a>
        <a href="/pages/domestic-transport.html" class="mobile-nav-link" data-page="domestic-transport">
            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9L18 10V6c0-2-2-4-4-4H4c-2 0-4 2-4 4v10c0 .6.4 1 1 1h2"/>
                <circle cx="7" cy="17" r="2"/>
                <path d="M9 17h6"/>
                <circle cx="17" cy="17" r="2"/>
            </svg>
            <span>境内用车</span>
        </a>
        <a href="/pages/signature-tickets.html" class="mobile-nav-link" data-page="signature-tickets">
            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M2 9a3 3 0 0 1 0 6v2a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-2a3 3 0 0 1 0-6V7a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v2Z"/>
                <path d="M13 5v2"/>
                <path d="M13 17v2"/>
                <path d="M13 11v2"/>
            </svg>
            <span>特色船票</span>
        </a>
        <a href="/pages/international-tours.html" class="mobile-nav-link" data-page="international-tours">
            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"/>
                <path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20"/>
                <path d="M2 12h20"/>
            </svg>
            <span>国际旅游</span>
        </a>
        <a href="/pages/business-reception.html" class="mobile-nav-link" data-page="business-reception">
            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z"/>
                <path d="M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2"/>
                <path d="M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2"/>
                <path d="M10 6h4"/>
                <path d="M10 10h4"/>
                <path d="M10 14h4"/>
                <path d="M10 18h4"/>
            </svg>
            <span>商务接待</span>
        </a>
        <a href="/pages/island-hopping.html" class="mobile-nav-link" data-page="island-hopping">
            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M2 6s1.5-2 5-2 5 2 5 2v14s-1.5-2-5-2-5 2-5 2V6Z"/>
                <path d="M12 6s1.5-2 5-2 5 2 5 2v14s-1.5-2-5-2-5 2-5 2V6Z"/>
            </svg>
            <span>海岛旅游</span>
        </a>
        <a href="/pages/sky-pier-cafe.html" class="mobile-nav-link" data-page="sky-pier-cafe">
            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M18 8h1a4 4 0 0 1 0 8h-1"/>
                <path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8Z"/>
                <line x1="6" y1="1" x2="6" y2="4"/>
                <line x1="10" y1="1" x2="10" y2="4"/>
                <line x1="14" y1="1" x2="14" y2="4"/>
            </svg>
            <span>天空号船咖啡厅</span>
        </a>
        <a href="/pages/about.html" class="mobile-nav-link" data-page="about">
            关于我们
        </a>
        <a href="/pages/contact.html" class="mobile-nav-link nav-cta-mobile">
            联系我们
        </a>
    </div>
</nav>

<style>
/* 导航栏样式 */
.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    text-decoration: none;
    color: var(--color-gray-800);
}

.nav-logo {
    width: 2.5rem;
    height: 2.5rem;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-white);
    font-weight: 700;
    font-size: var(--text-lg);
}

.nav-title {
    font-size: var(--text-xl);
    font-weight: 700;
    color: var(--color-gray-800);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    padding: var(--spacing-2) var(--spacing-3);
    border-radius: var(--radius-md);
    text-decoration: none;
    color: var(--color-gray-700);
    font-weight: 500;
    font-size: var(--text-sm);
    transition: var(--transition-colors);
}

.nav-link:hover {
    color: var(--color-blue-600);
    background-color: var(--color-gray-50);
}

.nav-link.active {
    color: var(--color-blue-600);
    background-color: var(--color-blue-50);
}

.nav-cta {
    background: var(--color-blue-600);
    color: var(--color-white);
    padding: var(--spacing-2) var(--spacing-4);
    border-radius: var(--radius-md);
    text-decoration: none;
    font-weight: 600;
    font-size: var(--text-sm);
    transition: var(--transition-colors);
}

.nav-cta:hover {
    background: var(--color-blue-700);
}

.mobile-menu-btn:hover {
    color: var(--color-blue-600);
}

.mobile-nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3);
    border-radius: var(--radius-md);
    text-decoration: none;
    color: var(--color-gray-700);
    font-weight: 500;
    transition: var(--transition-colors);
    margin-bottom: var(--spacing-1);
}

.mobile-nav-link:hover,
.mobile-nav-link.active {
    color: var(--color-blue-600);
    background-color: var(--color-blue-50);
}

.nav-cta-mobile {
    background: var(--color-blue-600);
    color: var(--color-white);
    font-weight: 600;
    margin-top: var(--spacing-2);
}

.nav-cta-mobile:hover {
    background: var(--color-blue-700);
    color: var(--color-white);
}
</style>
