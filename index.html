<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Sky Mirror World Tour - 您的专属马来西亚旅行伙伴，提供天空之镜、境内用车、海岛旅游等专业服务">
    <meta name="keywords" content="天空之镜,马来西亚旅游,境内用车,海岛旅游,瓜拉雪兰莪">
    <meta name="author" content="Sky Mirror World Tour">

    <!-- Open Graph -->
    <meta property="og:title" content="Sky Mirror World Tour - 发现马来西亚的天空之镜">
    <meta property="og:description" content="您的专属旅行伙伴，为您打造难忘的马来西亚之旅">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://skymirror.com">
    <meta property="og:image" content="https://images.pexels.com/photos/1271619/pexels-photo-1271619.jpeg">

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ctext y='.9em' font-size='90'%3E🌊%3C/text%3E%3C/svg%3E">

    <title>Sky Mirror World Tour - 发现马来西亚的天空之镜</title>

    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <link rel="stylesheet" href="assets/css/responsive.css">

    <!-- Preload critical resources -->
    <link rel="preconnect" href="https://images.pexels.com">
    <link rel="dns-prefetch" href="//images.pexels.com">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <!-- 品牌Logo -->
            <a href="index.html" class="nav-brand">
                <div class="nav-logo">S</div>
                <span class="nav-title">Sky Mirror World Tour</span>
            </a>

            <!-- 桌面导航菜单 -->
            <div class="nav-menu">
                <a href="index.html" class="nav-link active" data-page="home">
                    <span>首页</span>
                </a>
                <a href="pages/domestic-transport.html" class="nav-link" data-page="domestic-transport">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9L18 10V6c0-2-2-4-4-4H4c-2 0-4 2-4 4v10c0 .6.4 1 1 1h2"/>
                        <circle cx="7" cy="17" r="2"/>
                        <path d="M9 17h6"/>
                        <circle cx="17" cy="17" r="2"/>
                    </svg>
                    <span>境内用车</span>
                </a>
                <a href="pages/signature-tickets.html" class="nav-link" data-page="signature-tickets">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M2 9a3 3 0 0 1 0 6v2a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-2a3 3 0 0 1 0-6V7a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v2Z"/>
                        <path d="M13 5v2"/>
                        <path d="M13 17v2"/>
                        <path d="M13 11v2"/>
                    </svg>
                    <span>特色船票</span>
                </a>
                <a href="pages/international-tours.html" class="nav-link" data-page="international-tours">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="10"/>
                        <path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20"/>
                        <path d="M2 12h20"/>
                    </svg>
                    <span>国际旅游</span>
                </a>
                <a href="pages/business-reception.html" class="nav-link" data-page="business-reception">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z"/>
                        <path d="M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2"/>
                        <path d="M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2"/>
                        <path d="M10 6h4"/>
                        <path d="M10 10h4"/>
                        <path d="M10 14h4"/>
                        <path d="M10 18h4"/>
                    </svg>
                    <span>商务接待</span>
                </a>
                <a href="pages/island-hopping.html" class="nav-link" data-page="island-hopping">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M2 6s1.5-2 5-2 5 2 5 2v14s-1.5-2-5-2-5 2-5 2V6Z"/>
                        <path d="M12 6s1.5-2 5-2 5 2 5 2v14s-1.5-2-5-2-5 2-5 2V6Z"/>
                    </svg>
                    <span>海岛旅游</span>
                </a>
                <a href="pages/sky-pier-cafe.html" class="nav-link" data-page="sky-pier-cafe">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M18 8h1a4 4 0 0 1 0 8h-1"/>
                        <path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8Z"/>
                        <line x1="6" y1="1" x2="6" y2="4"/>
                        <line x1="10" y1="1" x2="10" y2="4"/>
                        <line x1="14" y1="1" x2="14" y2="4"/>
                    </svg>
                    <span>天空号船咖啡厅</span>
                </a>
                <a href="pages/about.html" class="nav-link" data-page="about">关于我们</a>
                <a href="pages/contact.html" class="nav-cta">联系我们</a>
            </div>

            <!-- 移动端菜单按钮 -->
            <button type="button" class="mobile-menu-btn" id="mobileMenuBtn" aria-label="打开菜单">
                <svg id="menuIcon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="4" y1="6" x2="20" y2="6"/>
                    <line x1="4" y1="12" x2="20" y2="12"/>
                    <line x1="4" y1="18" x2="20" y2="18"/>
                </svg>
                <svg id="closeIcon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="hidden">
                    <path d="M18 6 6 18"/>
                    <path d="m6 6 12 12"/>
                </svg>
            </button>
        </div>

        <!-- 移动端菜单 -->
        <div class="mobile-menu" id="mobileMenu">
            <a href="index.html" class="mobile-nav-link active" data-page="home">
                <span>首页</span>
            </a>
            <a href="pages/domestic-transport.html" class="mobile-nav-link" data-page="domestic-transport">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9L18 10V6c0-2-2-4-4-4H4c-2 0-4 2-4 4v10c0 .6.4 1 1 1h2"/>
                    <circle cx="7" cy="17" r="2"/>
                    <path d="M9 17h6"/>
                    <circle cx="17" cy="17" r="2"/>
                </svg>
                <span>境内用车</span>
            </a>
            <a href="pages/signature-tickets.html" class="mobile-nav-link" data-page="signature-tickets">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M2 9a3 3 0 0 1 0 6v2a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-2a3 3 0 0 1 0-6V7a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v2Z"/>
                    <path d="M13 5v2"/>
                    <path d="M13 17v2"/>
                    <path d="M13 11v2"/>
                </svg>
                <span>特色船票</span>
            </a>
            <a href="pages/international-tours.html" class="mobile-nav-link" data-page="international-tours">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="12" cy="12" r="10"/>
                    <path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20"/>
                    <path d="M2 12h20"/>
                </svg>
                <span>国际旅游</span>
            </a>
            <a href="pages/business-reception.html" class="mobile-nav-link" data-page="business-reception">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z"/>
                    <path d="M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2"/>
                    <path d="M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2"/>
                    <path d="M10 6h4"/>
                    <path d="M10 10h4"/>
                    <path d="M10 14h4"/>
                    <path d="M10 18h4"/>
                </svg>
                <span>商务接待</span>
            </a>
            <a href="pages/island-hopping.html" class="mobile-nav-link" data-page="island-hopping">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M2 6s1.5-2 5-2 5 2 5 2v14s-1.5-2-5-2-5 2-5 2V6Z"/>
                    <path d="M12 6s1.5-2 5-2 5 2 5 2v14s-1.5-2-5-2-5 2-5 2V6Z"/>
                </svg>
                <span>海岛旅游</span>
            </a>
            <a href="pages/sky-pier-cafe.html" class="mobile-nav-link" data-page="sky-pier-cafe">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M18 8h1a4 4 0 0 1 0 8h-1"/>
                    <path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8Z"/>
                    <line x1="6" y1="1" x2="6" y2="4"/>
                    <line x1="10" y1="1" x2="10" y2="4"/>
                    <line x1="14" y1="1" x2="14" y2="4"/>
                </svg>
                <span>天空号船咖啡厅</span>
            </a>
            <a href="pages/about.html" class="mobile-nav-link" data-page="about">
                关于我们
            </a>
            <a href="pages/contact.html" class="mobile-nav-link nav-cta-mobile">
                联系我们
            </a>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main>
        <!-- Hero 区域 -->
        <section class="hero-section" id="hero">
            <div class="hero-background" style="background-image: url('https://images.pexels.com/photos/1271619/pexels-photo-1271619.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop')"></div>
            <div class="hero-overlay"></div>
            <div class="hero-content">
                <h1 class="text-4xl md:text-5xl lg:text-7xl font-bold mb-6 leading-tight">
                    发现马来西亚的
                    <span class="block text-transparent bg-clip-text" style="background: linear-gradient(45deg, #ffffff, #a7f3d0);">天空之镜</span>
                </h1>
                <p class="text-lg md:text-xl mb-8 max-w-3xl mx-auto leading-relaxed opacity-90">
                    在这片神奇的土地上，天空与海水完美融合，创造出令人叹为观止的镜面效果。
                    让我们带您探索马来西亚最美的自然奇观，体验一次难忘的旅程。
                </p>
                <div class="button-group">
                    <a href="pages/signature-tickets.html" class="btn btn-primary btn-lg">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M2 9a3 3 0 0 1 0 6v2a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-2a3 3 0 0 1 0-6V7a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v2Z"/>
                            <path d="M13 5v2"/>
                            <path d="M13 17v2"/>
                            <path d="M13 11v2"/>
                        </svg>
                        立即预订
                    </a>
                    <a href="#services" class="btn btn-outline btn-lg">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M7 17L17 7"/>
                            <path d="M7 7h10v10"/>
                        </svg>
                        了解更多
                    </a>
                </div>
            </div>
        </section>

        <!-- 当红路线 -->
        <section class="responsive-py-20" id="hot-routes">
            <div class="container">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                        当红路线
                    </h2>
                    <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                        精选热门目的地，带您探索世界之美，体验不同文化的独特魅力
                    </p>
                </div>

                <div class="routes-carousel-container">
                    <div class="routes-carousel" id="routesCarousel">
                        <!-- 日本樱花季 -->
                        <div class="route-card">
                            <div class="route-image">
                                <img src="https://images.pexels.com/photos/2070033/pexels-photo-2070033.jpeg?auto=compress&cs=tinysrgb&w=400&h=250&fit=crop" alt="日本樱花季">
                                <div class="route-badge hot">热门</div>
                            </div>
                            <div class="route-content">
                                <h3 class="route-title">日本樱花季经典游</h3>
                                <div class="route-highlights">
                                    <span>东京</span> • <span>京都</span> • <span>大阪</span> • <span>富士山</span>
                                </div>
                                <div class="route-details">
                                    <span class="route-days">7天6夜</span>
                                    <div class="route-price">
                                        从 <strong>RM 3,299</strong> 起
                                    </div>
                                </div>
                                <button class="btn btn-primary btn-sm route-book-btn" onclick="goToRouteDetail('japan-sakura')">
                                    立即预订
                                </button>
                            </div>
                        </div>

                        <!-- 韩国首尔济州岛 -->
                        <div class="route-card">
                            <div class="route-image">
                                <img src="https://images.pexels.com/photos/237211/pexels-photo-237211.jpeg?auto=compress&cs=tinysrgb&w=400&h=250&fit=crop" alt="韩国首尔">
                                <div class="route-badge special">特价</div>
                            </div>
                            <div class="route-content">
                                <h3 class="route-title">韩国首尔济州岛</h3>
                                <div class="route-highlights">
                                    <span>首尔</span> • <span>济州岛</span> • <span>明洞</span> • <span>汉江</span>
                                </div>
                                <div class="route-details">
                                    <span class="route-days">5天4夜</span>
                                    <div class="route-price">
                                        从 <strong>RM 2,199</strong> 起
                                    </div>
                                </div>
                                <button class="btn btn-primary btn-sm route-book-btn" onclick="goToRouteDetail('korea-seoul')">
                                    立即预订
                                </button>
                            </div>
                        </div>

                        <!-- 泰国曼谷芭提雅 -->
                        <div class="route-card">
                            <div class="route-image">
                                <img src="https://images.pexels.com/photos/1007426/pexels-photo-1007426.jpeg?auto=compress&cs=tinysrgb&w=400&h=250&fit=crop" alt="泰国曼谷">
                                <div class="route-badge recommended">推荐</div>
                            </div>
                            <div class="route-content">
                                <h3 class="route-title">泰国曼谷芭提雅</h3>
                                <div class="route-highlights">
                                    <span>曼谷</span> • <span>芭提雅</span> • <span>大皇宫</span> • <span>水上市场</span>
                                </div>
                                <div class="route-details">
                                    <span class="route-days">6天5夜</span>
                                    <div class="route-price">
                                        从 <strong>RM 1,899</strong> 起
                                    </div>
                                </div>
                                <button class="btn btn-primary btn-sm route-book-btn" onclick="goToRouteDetail('thailand-bangkok')">
                                    立即预订
                                </button>
                            </div>
                        </div>

                        <!-- 越南胡志明河内 -->
                        <div class="route-card">
                            <div class="route-image">
                                <img src="https://images.pexels.com/photos/1371360/pexels-photo-1371360.jpeg?auto=compress&cs=tinysrgb&w=400&h=250&fit=crop" alt="越南胡志明市">
                            </div>
                            <div class="route-content">
                                <h3 class="route-title">越南胡志明河内</h3>
                                <div class="route-highlights">
                                    <span>胡志明市</span> • <span>河内</span> • <span>下龙湾</span> • <span>美奈</span>
                                </div>
                                <div class="route-details">
                                    <span class="route-days">8天7夜</span>
                                    <div class="route-price">
                                        从 <strong>RM 2,599</strong> 起
                                    </div>
                                </div>
                                <button class="btn btn-primary btn-sm route-book-btn" onclick="goToRouteDetail('vietnam-saigon')">
                                    立即预订
                                </button>
                            </div>
                        </div>

                        <!-- 新加坡精华游 -->
                        <div class="route-card">
                            <div class="route-image">
                                <img src="https://images.pexels.com/photos/1366919/pexels-photo-1366919.jpeg?auto=compress&cs=tinysrgb&w=400&h=250&fit=crop" alt="新加坡">
                            </div>
                            <div class="route-content">
                                <h3 class="route-title">新加坡精华游</h3>
                                <div class="route-highlights">
                                    <span>滨海湾</span> • <span>圣淘沙</span> • <span>环球影城</span> • <span>牛车水</span>
                                </div>
                                <div class="route-details">
                                    <span class="route-days">4天3夜</span>
                                    <div class="route-price">
                                        从 <strong>RM 1,699</strong> 起
                                    </div>
                                </div>
                                <button class="btn btn-primary btn-sm route-book-btn" onclick="goToRouteDetail('singapore-classic')">
                                    立即预订
                                </button>
                            </div>
                        </div>

                        <!-- 印尼巴厘岛 -->
                        <div class="route-card">
                            <div class="route-image">
                                <img src="https://images.pexels.com/photos/1271619/pexels-photo-1271619.jpeg?auto=compress&cs=tinysrgb&w=400&h=250&fit=crop" alt="印尼巴厘岛">
                            </div>
                            <div class="route-content">
                                <h3 class="route-title">印尼巴厘岛度假</h3>
                                <div class="route-highlights">
                                    <span>乌布</span> • <span>库塔</span> • <span>圣泉寺</span> • <span>金巴兰</span>
                                </div>
                                <div class="route-details">
                                    <span class="route-days">6天5夜</span>
                                    <div class="route-price">
                                        从 <strong>RM 2,399</strong> 起
                                    </div>
                                </div>
                                <button class="btn btn-primary btn-sm route-book-btn" onclick="goToRouteDetail('indonesia-bali')">
                                    立即预订
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 轮播控制按钮 -->
                    <div class="carousel-controls">
                        <button class="carousel-btn prev-btn" onclick="scrollCarousel('left')" aria-label="向左滚动">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="15,18 9,12 15,6"/>
                            </svg>
                        </button>
                        <button class="carousel-btn next-btn" onclick="scrollCarousel('right')" aria-label="向右滚动">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="9,18 15,12 9,6"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- 服务展示区域 -->
        <section class="responsive-py-20 bg-gray-50" id="services">
            <div class="container">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                        我们的专业服务
                    </h2>
                    <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                        从天空之镜的神奇体验到全方位的旅游服务，我们为您提供专业、贴心的一站式旅行解决方案
                    </p>
                </div>

                <div class="services-grid">
                    <!-- 特色船票 -->
                    <div class="service-card">
                        <div class="p-6">
                            <div class="service-icon" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                    <path d="M2 9a3 3 0 0 1 0 6v2a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-2a3 3 0 0 1 0-6V7a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v2Z"/>
                                    <path d="M13 5v2"/>
                                    <path d="M13 17v2"/>
                                    <path d="M13 11v2"/>
                                </svg>
                            </div>
                            <h3 class="service-title">特色船票体验</h3>
                            <p class="service-description">
                                体验天空之镜的神奇魅力，观赏老鹰觅食，探索萤火虫的奇妙世界，深入红树林生态系统。
                            </p>
                            <a href="pages/signature-tickets.html" class="service-link">
                                了解详情
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M7 17L17 7"/>
                                    <path d="M7 7h10v10"/>
                                </svg>
                            </a>
                        </div>
                    </div>

                    <!-- 境内用车 -->
                    <div class="service-card">
                        <div class="p-6">
                            <div class="service-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                    <path d="M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9L18 10V6c0-2-2-4-4-4H4c-2 0-4 2-4 4v10c0 .6.4 1 1 1h2"/>
                                    <circle cx="7" cy="17" r="2"/>
                                    <path d="M9 17h6"/>
                                    <circle cx="17" cy="17" r="2"/>
                                </svg>
                            </div>
                            <h3 class="service-title">境内用车服务</h3>
                            <p class="service-description">
                                提供机场接送、半天包车、全天包车等多样化交通服务，让您的旅程更加便捷舒适。
                            </p>
                            <a href="pages/domestic-transport.html" class="service-link">
                                了解详情
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M7 17L17 7"/>
                                    <path d="M7 7h10v10"/>
                                </svg>
                            </a>
                        </div>
                    </div>

                    <!-- 国际旅游 -->
                    <div class="service-card">
                        <div class="p-6">
                            <div class="service-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"/>
                                    <path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20"/>
                                    <path d="M2 12h20"/>
                                </svg>
                            </div>
                            <h3 class="service-title">国际旅游</h3>
                            <p class="service-description">
                                精心策划的国际旅游路线，带您探索世界各地的美景，体验不同文化的魅力。
                            </p>
                            <a href="pages/international-tours.html" class="service-link">
                                了解详情
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M7 17L17 7"/>
                                    <path d="M7 7h10v10"/>
                                </svg>
                            </a>
                        </div>
                    </div>

                    <!-- 商务接待 -->
                    <div class="service-card">
                        <div class="p-6">
                            <div class="service-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                    <path d="M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z"/>
                                    <path d="M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2"/>
                                    <path d="M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2"/>
                                    <path d="M10 6h4"/>
                                    <path d="M10 10h4"/>
                                    <path d="M10 14h4"/>
                                    <path d="M10 18h4"/>
                                </svg>
                            </div>
                            <h3 class="service-title">商务接待</h3>
                            <p class="service-description">
                                专业的商务接待服务，为您的商务活动提供全方位支持，确保每一个细节都完美无缺。
                            </p>
                            <a href="pages/business-reception.html" class="service-link">
                                了解详情
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M7 17L17 7"/>
                                    <path d="M7 7h10v10"/>
                                </svg>
                            </a>
                        </div>
                    </div>

                    <!-- 海岛旅游 -->
                    <div class="service-card">
                        <div class="p-6">
                            <div class="service-icon" style="background: linear-gradient(135deg, #06b6d4, #0891b2);">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                    <path d="M2 6s1.5-2 5-2 5 2 5 2v14s-1.5-2-5-2-5 2-5 2V6Z"/>
                                    <path d="M12 6s1.5-2 5-2 5 2 5 2v14s-1.5-2-5-2-5 2-5 2V6Z"/>
                                </svg>
                            </div>
                            <h3 class="service-title">海岛旅游</h3>
                            <p class="service-description">
                                探索马来西亚美丽的海岛，享受阳光沙滩，体验水上活动，感受热带海岛的无限魅力。
                            </p>
                            <a href="pages/island-hopping.html" class="service-link">
                                了解详情
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M7 17L17 7"/>
                                    <path d="M7 7h10v10"/>
                                </svg>
                            </a>
                        </div>
                    </div>

                    <!-- 天空号船咖啡厅 -->
                    <div class="service-card">
                        <div class="p-6">
                            <div class="service-icon" style="background: linear-gradient(135deg, #ef4444, #dc2626);">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                    <path d="M18 8h1a4 4 0 0 1 0 8h-1"/>
                                    <path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8Z"/>
                                    <line x1="6" y1="1" x2="6" y2="4"/>
                                    <line x1="10" y1="1" x2="10" y2="4"/>
                                    <line x1="14" y1="1" x2="14" y2="4"/>
                                </svg>
                            </div>
                            <h3 class="service-title">天空号船咖啡厅</h3>
                            <p class="service-description">
                                独特的船舶改造咖啡厅，在海上享受精品咖啡，体验别样的海上休闲时光。
                            </p>
                            <a href="pages/sky-pier-cafe.html" class="service-link">
                                了解详情
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M7 17L17 7"/>
                                    <path d="M7 7h10v10"/>
                                </svg>
                            </a>
                        </div>
                    </div>

                    <!-- 酒店代订 -->
                    <div class="service-card">
                        <div class="p-6">
                            <div class="service-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                    <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                                    <polyline points="9,22 9,12 15,12 15,22"/>
                                </svg>
                            </div>
                            <h3 class="service-title">酒店代订</h3>
                            <p class="service-description">
                                全球优质酒店预订服务，从经济实惠到奢华享受，为您找到最适合的住宿选择。
                            </p>
                            <a href="pages/hotel-booking.html" class="service-link">
                                了解详情
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M7 17L17 7"/>
                                    <path d="M7 7h10v10"/>
                                </svg>
                            </a>
                        </div>
                    </div>

                    <!-- 机票代订 -->
                    <div class="service-card">
                        <div class="p-6">
                            <div class="service-icon" style="background: linear-gradient(135deg, #3b82f6, #2563eb);">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                    <path d="M17.8 19.2 16 11l3.5-3.5C21 6 21 4 19 4s-2 2-3.5 3.5L11 16l-8.2 1.8c-.5.1-.8.6-.8 1.1s.3 1 .8 1.1L11 21.8c.5.1 1-.3 1.1-.8L14 13l3.5 3.5c1.5 1.5 3.5 1.5 3.5-1.5s-2-2-3.5-3.5Z"/>
                                </svg>
                            </div>
                            <h3 class="service-title">机票代订</h3>
                            <p class="service-description">
                                国内外航线机票预订，与多家航空公司合作，为您提供优惠价格和专业服务。
                            </p>
                            <a href="pages/flight-booking.html" class="service-link">
                                了解详情
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M7 17L17 7"/>
                                    <path d="M7 7h10v10"/>
                                </svg>
                            </a>
                        </div>
                    </div>

                    <!-- 学生团服务 -->
                    <div class="service-card">
                        <div class="p-6">
                            <div class="service-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                    <path d="M22 10v6M2 10l10-5 10 5-10 5z"/>
                                    <path d="M6 12v5c3 3 9 3 12 0v-5"/>
                                </svg>
                            </div>
                            <h3 class="service-title">学生团服务</h3>
                            <p class="service-description">
                                专业的教育旅游服务，游学、研学、考察、团康活动，寓教于乐，安全第一。
                            </p>
                            <a href="pages/student-groups.html" class="service-link">
                                了解详情
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M7 17L17 7"/>
                                    <path d="M7 7h10v10"/>
                                </svg>
                            </a>
                        </div>
                    </div>

                    <!-- 医疗旅游 -->
                    <div class="service-card">
                        <div class="p-6">
                            <div class="service-icon" style="background: linear-gradient(135deg, #ec4899, #db2777);">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                    <path d="M22 12h-4l-3 9L9 3l-3 9H2"/>
                                </svg>
                            </div>
                            <h3 class="service-title">医疗旅游</h3>
                            <p class="service-description">
                                结合旅游与医疗的专业服务，健康检查、医疗美容、康复疗养等全方位体验。
                            </p>
                            <a href="pages/medical-tourism.html" class="service-link">
                                了解详情
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M7 17L17 7"/>
                                    <path d="M7 7h10v10"/>
                                </svg>
                            </a>
                        </div>
                    </div>

                    <!-- 公司团建 -->
                    <div class="service-card">
                        <div class="p-6">
                            <div class="service-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
                                    <circle cx="9" cy="7" r="4"/>
                                    <path d="M22 21v-2a4 4 0 0 0-3-3.87"/>
                                    <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                                </svg>
                            </div>
                            <h3 class="service-title">公司团建</h3>
                            <p class="service-description">
                                专业的企业团队建设服务，增强团队凝聚力，提升员工满意度和企业竞争力。
                            </p>
                            <a href="pages/corporate-team-building.html" class="service-link">
                                了解详情
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M7 17L17 7"/>
                                    <path d="M7 7h10v10"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 特色功能区域 -->
        <section class="responsive-py-20">
            <div class="container">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                        为什么选择我们
                    </h2>
                    <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                        专业的团队，贴心的服务，让您的每一次旅行都成为美好的回忆
                    </p>
                </div>

                <div class="features-grid">
                    <!-- 专业团队 -->
                    <div class="feature-card">
                        <div class="feature-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
                                <circle cx="9" cy="7" r="4"/>
                                <path d="M22 21v-2a4 4 0 0 0-3-3.87"/>
                                <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                            </svg>
                        </div>
                        <h3 class="feature-title">专业团队</h3>
                        <p class="feature-description">
                            经验丰富的导游团队，为您提供专业的旅游服务和深度的文化体验
                        </p>
                    </div>

                    <!-- 安全保障 -->
                    <div class="feature-card">
                        <div class="feature-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10"/>
                                <path d="M9 12l2 2 4-4"/>
                            </svg>
                        </div>
                        <h3 class="feature-title">安全保障</h3>
                        <p class="feature-description">
                            完善的安全措施和保险保障，让您的旅程安心无忧
                        </p>
                    </div>

                    <!-- 贴心服务 -->
                    <div class="feature-card">
                        <div class="feature-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
                            </svg>
                        </div>
                        <h3 class="feature-title">贴心服务</h3>
                        <p class="feature-description">
                            24小时客服支持，从行程规划到旅途中的每一个细节都为您精心安排
                        </p>
                    </div>

                    <!-- 性价比高 -->
                    <div class="feature-card">
                        <div class="feature-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                <line x1="12" y1="1" x2="12" y2="23"/>
                                <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/>
                            </svg>
                        </div>
                        <h3 class="feature-title">性价比高</h3>
                        <p class="feature-description">
                            合理的价格，优质的服务，让您享受超值的旅游体验
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 客户评价区域 -->
        <section class="responsive-py-20 bg-gray-50">
            <div class="container">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                        客户真实评价
                    </h2>
                    <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                        听听我们的客户怎么说，他们的满意是我们最大的动力
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- 评价 1 -->
                    <div class="card">
                        <div class="card-body">
                            <div class="flex items-center mb-4">
                                <div class="flex text-yellow-400">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                    </svg>
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                    </svg>
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                    </svg>
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                    </svg>
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                    </svg>
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4 leading-relaxed">
                                "天空之镜真的太美了！导游非常专业，服务也很贴心。整个行程安排得很合理，让我们充分体验了马来西亚的自然美景。强烈推荐！"
                            </p>
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-blue-600 font-semibold">张</span>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900">张女士</h4>
                                    <p class="text-sm text-gray-500">来自北京 · 2024年11月</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 评价 2 -->
                    <div class="card">
                        <div class="card-body">
                            <div class="flex items-center mb-4">
                                <div class="flex text-yellow-400">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                    </svg>
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                    </svg>
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                    </svg>
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                    </svg>
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                    </svg>
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4 leading-relaxed">
                                "用车服务非常棒！司机准时到达，车辆干净舒适。从机场到酒店的路上，司机还为我们介绍了当地的风土人情，非常贴心。"
                            </p>
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-green-600 font-semibold">李</span>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900">李先生</h4>
                                    <p class="text-sm text-gray-500">来自上海 · 2024年10月</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 评价 3 -->
                    <div class="card">
                        <div class="card-body">
                            <div class="flex items-center mb-4">
                                <div class="flex text-yellow-400">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                    </svg>
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                    </svg>
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                    </svg>
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                    </svg>
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                    </svg>
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4 leading-relaxed">
                                "天空号船咖啡厅的体验太独特了！在海上喝咖啡，看日落，感觉就像在梦境中一样。工作人员服务态度也很好，值得推荐！"
                            </p>
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-purple-600 font-semibold">王</span>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900">王小姐</h4>
                                    <p class="text-sm text-gray-500">来自广州 · 2024年12月</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 统计数据 -->
                <div class="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8">
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-blue-600 mb-2">10,000+</div>
                        <p class="text-gray-600">满意客户</p>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-green-600 mb-2">98%</div>
                        <p class="text-gray-600">好评率</p>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-purple-600 mb-2">9年</div>
                        <p class="text-gray-600">专业经验</p>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-orange-600 mb-2">24/7</div>
                        <p class="text-gray-600">客服支持</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 精彩瞬间画廊 -->
        <section class="responsive-py-20">
            <div class="container">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                        精彩瞬间
                    </h2>
                    <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                        记录每一个美好时刻，分享旅途中的精彩瞬间
                    </p>
                </div>

                <div class="photo-gallery">
                    <div class="gallery-item">
                        <img src="https://images.pexels.com/photos/1271619/pexels-photo-1271619.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop"
                             alt="天空之镜美景" class="gallery-image">
                        <div class="gallery-overlay">
                            <h3 class="text-white font-semibold mb-2">天空之镜</h3>
                            <p class="text-white text-sm opacity-90">完美的镜面效果</p>
                        </div>
                    </div>
                    <div class="gallery-item">
                        <img src="https://images.pexels.com/photos/1366919/pexels-photo-1366919.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop"
                             alt="萤火虫观赏" class="gallery-image">
                        <div class="gallery-overlay">
                            <h3 class="text-white font-semibold mb-2">萤火虫奇观</h3>
                            <p class="text-white text-sm opacity-90">夜晚的魔法时刻</p>
                        </div>
                    </div>
                    <div class="gallery-item">
                        <img src="https://images.pexels.com/photos/302899/pexels-photo-302899.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop"
                             alt="老鹰觅食" class="gallery-image">
                        <div class="gallery-overlay">
                            <h3 class="text-white font-semibold mb-2">老鹰觅食</h3>
                            <p class="text-white text-sm opacity-90">壮观的自然景象</p>
                        </div>
                    </div>
                    <div class="gallery-item">
                        <img src="https://images.pexels.com/photos/1271619/pexels-photo-1271619.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop"
                             alt="红树林探索" class="gallery-image">
                        <div class="gallery-overlay">
                            <h3 class="text-white font-semibold mb-2">红树林</h3>
                            <p class="text-white text-sm opacity-90">生态系统探索</p>
                        </div>
                    </div>
                    <div class="gallery-item">
                        <img src="https://images.pexels.com/photos/1366919/pexels-photo-1366919.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop"
                             alt="天空号咖啡厅" class="gallery-image">
                        <div class="gallery-overlay">
                            <h3 class="text-white font-semibold mb-2">天空号咖啡厅</h3>
                            <p class="text-white text-sm opacity-90">海上咖啡体验</p>
                        </div>
                    </div>
                    <div class="gallery-item">
                        <img src="https://images.pexels.com/photos/302899/pexels-photo-302899.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop"
                             alt="海岛风光" class="gallery-image">
                        <div class="gallery-overlay">
                            <h3 class="text-white font-semibold mb-2">海岛风光</h3>
                            <p class="text-white text-sm opacity-90">热带海岛之美</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- CTA 区域 -->
        <section class="responsive-py-20 bg-gradient-to-r from-blue-600 to-emerald-600">
            <div class="container">
                <div class="text-center text-white">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
                        准备开始您的马来西亚之旅了吗？
                    </h2>
                    <p class="text-lg md:text-xl mb-8 max-w-3xl mx-auto leading-relaxed opacity-90">
                        联系我们，让专业的团队为您定制专属的旅行方案，创造难忘的回忆
                    </p>
                    <div class="button-group">
                        <a href="pages/contact.html" class="btn btn-secondary btn-lg">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                                <polyline points="22,6 12,13 2,6"/>
                            </svg>
                            立即联系
                        </a>
                        <a href="pages/signature-tickets.html" class="btn btn-outline btn-lg">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M2 9a3 3 0 0 1 0 6v2a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-2a3 3 0 0 1 0-6V7a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v2Z"/>
                                <path d="M13 5v2"/>
                                <path d="M13 17v2"/>
                                <path d="M13 11v2"/>
                            </svg>
                            查看行程
                        </a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 页脚 -->
    <footer class="bg-gray-900 text-white responsive-py-16">
        <div class="container">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
                <!-- 公司信息 -->
                <div>
                    <div class="flex items-center gap-2 mb-4">
                        <div class="nav-logo">S</div>
                        <span class="text-xl font-bold">Sky Mirror World Tour</span>
                    </div>
                    <p class="text-gray-400 mb-4 leading-relaxed">
                        您的专属马来西亚旅行伙伴，为您打造难忘的旅行体验。
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white transition-colors" aria-label="Facebook">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors" aria-label="Instagram">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987c6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297L3.323 17.49c.875.807 2.026 1.297 3.323 1.297h7.137c1.297 0 2.448-.49 3.323-1.297l-1.803-1.799c-.875.807-2.026 1.297-3.323 1.297H8.449z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors" aria-label="WhatsApp">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.864 3.488"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- 快速链接 -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">快速链接</h3>
                    <ul class="space-y-2">
                        <li><a href="/" class="text-gray-400 hover:text-white transition-colors">首页</a></li>
                        <li><a href="pages/signature-tickets.html" class="text-gray-400 hover:text-white transition-colors">特色船票</a></li>
                        <li><a href="pages/domestic-transport.html" class="text-gray-400 hover:text-white transition-colors">境内用车</a></li>
                        <li><a href="pages/international-tours.html" class="text-gray-400 hover:text-white transition-colors">国际旅游</a></li>
                        <li><a href="pages/about.html" class="text-gray-400 hover:text-white transition-colors">关于我们</a></li>
                    </ul>
                </div>

                <!-- 服务项目 -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">服务项目</h3>
                    <ul class="space-y-2">
                        <li><a href="pages/business-reception.html" class="text-gray-400 hover:text-white transition-colors">商务接待</a></li>
                        <li><a href="pages/island-hopping.html" class="text-gray-400 hover:text-white transition-colors">海岛旅游</a></li>
                        <li><a href="pages/sky-pier-cafe.html" class="text-gray-400 hover:text-white transition-colors">天空号船咖啡厅</a></li>
                        <li><a href="pages/airport-transfer.html" class="text-gray-400 hover:text-white transition-colors">机场接送</a></li>
                        <li><a href="pages/chartered-car.html" class="text-gray-400 hover:text-white transition-colors">包车服务</a></li>
                    </ul>
                </div>

                <!-- 联系信息 -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">联系我们</h3>
                    <div class="space-y-3">
                        <div class="flex items-start gap-2">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mt-1 flex-shrink-0">
                                <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                                <circle cx="12" cy="10" r="3"/>
                            </svg>
                            <span class="text-gray-400 text-sm">马来西亚 雪兰莪州 瓜拉雪兰莪</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="flex-shrink-0">
                                <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                            </svg>
                            <span class="text-gray-400 text-sm">+60 12-345-6789</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="flex-shrink-0">
                                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                                <polyline points="22,6 12,13 2,6"/>
                            </svg>
                            <span class="text-gray-400 text-sm"><EMAIL></span>
                        </div>
                        <div class="flex items-start gap-2">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mt-1 flex-shrink-0">
                                <circle cx="12" cy="12" r="10"/>
                                <polyline points="12,6 12,12 16,14"/>
                            </svg>
                            <div class="text-gray-400 text-sm">
                                <div>营业时间：</div>
                                <div>周一至周日 8:00-20:00</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 版权信息 -->
            <div class="border-t border-gray-800 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-gray-400 text-sm mb-4 md:mb-0">
                        © 2024 Sky Mirror World Tour. 保留所有权利。
                    </p>
                    <div class="flex space-x-6 text-sm">
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">隐私政策</a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">服务条款</a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">网站地图</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="assets/js/data-loader.js"></script>
    <script src="assets/js/navigation.js"></script>
    <script src="assets/js/main.js"></script>
</body>
</html>
