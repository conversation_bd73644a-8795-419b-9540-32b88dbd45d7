<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Sky Mirror World Tour 商务接待服务 - 机场接送、会议安排、商务用车、酒店预订等专业服务">
    <meta name="keywords" content="商务接待,机场接送,会议安排,商务用车,酒店预订,企业服务">
    
    <title>商务接待服务 - Sky Mirror World Tour</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/components.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <!-- 品牌Logo -->
            <a href="../index.html" class="nav-brand">
                <div class="nav-logo">S</div>
                <span class="nav-title">Sky Mirror World Tour</span>
            </a>

            <!-- 桌面导航菜单 -->
            <div class="nav-menu">
                <a href="../index.html" class="nav-link" data-page="home">首页</a>
                <a href="domestic-transport.html" class="nav-link" data-page="domestic-transport">境内用车</a>
                <a href="signature-tickets.html" class="nav-link" data-page="signature-tickets">特色船票</a>
                <a href="international-tours.html" class="nav-link" data-page="international-tours">国际旅游</a>
                <a href="business-reception.html" class="nav-link active" data-page="business-reception">商务接待</a>
                <a href="island-hopping.html" class="nav-link" data-page="island-hopping">海岛旅游</a>
                <a href="sky-pier-cafe.html" class="nav-link" data-page="sky-pier-cafe">天空号船咖啡厅</a>
                <a href="about.html" class="nav-link" data-page="about">关于我们</a>
                <a href="contact.html" class="nav-cta">联系我们</a>
            </div>

            <!-- 移动端菜单按钮 -->
            <button type="button" class="mobile-menu-btn" id="mobileMenuBtn" aria-label="打开菜单">
                <svg id="menuIcon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="4" y1="6" x2="20" y2="6"/>
                    <line x1="4" y1="12" x2="20" y2="12"/>
                    <line x1="4" y1="18" x2="20" y2="18"/>
                </svg>
                <svg id="closeIcon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="hidden">
                    <path d="M18 6 6 18"/>
                    <path d="m6 6 12 12"/>
                </svg>
            </button>
        </div>

        <!-- 移动端菜单 -->
        <div class="mobile-menu" id="mobileMenu">
            <a href="../index.html" class="mobile-nav-link" data-page="home">首页</a>
            <a href="domestic-transport.html" class="mobile-nav-link" data-page="domestic-transport">境内用车</a>
            <a href="signature-tickets.html" class="mobile-nav-link" data-page="signature-tickets">特色船票</a>
            <a href="international-tours.html" class="mobile-nav-link" data-page="international-tours">国际旅游</a>
            <a href="business-reception.html" class="mobile-nav-link active" data-page="business-reception">商务接待</a>
            <a href="island-hopping.html" class="mobile-nav-link" data-page="island-hopping">海岛旅游</a>
            <a href="sky-pier-cafe.html" class="mobile-nav-link" data-page="sky-pier-cafe">天空号船咖啡厅</a>
            <a href="about.html" class="mobile-nav-link" data-page="about">关于我们</a>
            <a href="contact.html" class="mobile-nav-link nav-cta-mobile">联系我们</a>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main>
        <!-- Hero 区域 -->
        <section class="hero-section">
            <div class="hero-background" style="background-image: url('https://images.pexels.com/photos/302899/pexels-photo-302899.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop')"></div>
            <div class="hero-overlay"></div>
            <div class="hero-content">
                <h1 class="text-4xl md:text-5xl lg:text-7xl font-bold mb-6 leading-tight">
                    专业商务接待服务
                </h1>
                <p class="text-lg md:text-xl mb-8 max-w-3xl mx-auto leading-relaxed opacity-90">
                    为企业客户提供全方位的商务接待服务，让您的商务活动更加顺利和专业
                </p>
                <div class="button-group">
                    <a href="#services" class="btn btn-primary btn-lg">
                        查看服务
                    </a>
                    <a href="#contact-form" class="btn btn-outline btn-lg">
                        立即咨询
                    </a>
                </div>
            </div>
        </section>

        <!-- 服务概览区域 -->
        <section class="responsive-py-20" id="services">
            <div class="container">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                        商务接待服务
                    </h2>
                    <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                        专业的商务接待团队，为您的企业活动提供全方位支持
                    </p>
                </div>

                <div class="services-grid">
                    <!-- 机场接送服务 -->
                    <div class="service-card">
                        <div class="p-6">
                            <div class="service-icon" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                    <path d="M17.8 19.2 16 11l3.5-3.5C21 6 21 4 19 4s-2 2-3.5 3.5L11 16l-8.2 1.8c-.5.1-.8.6-.8 1.1s.3 1 .8 1.1L11 21.8c.5.1 1-.3 1.1-.8L14 13l3.5 3.5c1.5 1.5 3.5 1.5 3.5-1.5s-2-2-3.5-3.5Z"/>
                                </svg>
                            </div>
                            <h3 class="service-title">VIP机场接送</h3>
                            <p class="service-description">
                                豪华车队机场接送，专业司机服务，确保您的客户享受尊贵的到达体验。
                            </p>
                            <ul class="feature-list mt-4">
                                <li class="flex items-center space-x-2">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">豪华车型选择</span>
                                </li>
                                <li class="flex items-center space-x-2">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">专业司机服务</span>
                                </li>
                                <li class="flex items-center space-x-2">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">航班动态跟踪</span>
                                </li>
                            </ul>
                            <div class="mt-4 text-2xl font-bold text-blue-600">
                                RM 200 起
                            </div>
                        </div>
                    </div>

                    <!-- 会议安排服务 -->
                    <div class="service-card">
                        <div class="p-6">
                            <div class="service-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                    <rect width="18" height="18" x="3" y="4" rx="2" ry="2"/>
                                    <line x1="16" y1="2" x2="16" y2="6"/>
                                    <line x1="8" y1="2" x2="8" y2="6"/>
                                    <line x1="3" y1="10" x2="21" y2="10"/>
                                </svg>
                            </div>
                            <h3 class="service-title">会议场地安排</h3>
                            <p class="service-description">
                                协助安排会议场地、设备租赁、茶歇服务等，让您的会议更加专业。
                            </p>
                            <ul class="feature-list mt-4">
                                <li class="flex items-center space-x-2">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">场地预订</span>
                                </li>
                                <li class="flex items-center space-x-2">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">设备租赁</span>
                                </li>
                                <li class="flex items-center space-x-2">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">茶歇服务</span>
                                </li>
                            </ul>
                            <div class="mt-4 text-2xl font-bold text-blue-600">
                                RM 500 起
                            </div>
                        </div>
                    </div>

                    <!-- 商务用车服务 -->
                    <div class="service-card">
                        <div class="p-6">
                            <div class="service-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                    <path d="M8 6v6h8V6"/>
                                    <path d="M4 6h16l2 6v6a2 2 0 0 1-2 2h-2a2 2 0 0 1-2-2v-2H8v2a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-6l2-6Z"/>
                                    <circle cx="8" cy="16" r="2"/>
                                    <circle cx="16" cy="16" r="2"/>
                                </svg>
                            </div>
                            <h3 class="service-title">商务用车</h3>
                            <p class="service-description">
                                提供商务级别的用车服务，包括会议接送、商务考察、客户拜访等。
                            </p>
                            <ul class="feature-list mt-4">
                                <li class="flex items-center space-x-2">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">商务级车型</span>
                                </li>
                                <li class="flex items-center space-x-2">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">灵活时间安排</span>
                                </li>
                                <li class="flex items-center space-x-2">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">多点接送</span>
                                </li>
                            </ul>
                            <div class="mt-4 text-2xl font-bold text-blue-600">
                                RM 150 起/小时
                            </div>
                        </div>
                    </div>

                    <!-- 酒店预订服务 -->
                    <div class="service-card">
                        <div class="p-6">
                            <div class="service-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                    <path d="M2 17h20v2H2z"/>
                                    <path d="M6 17V9a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v8"/>
                                    <path d="M10 5V3a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v2"/>
                                    <path d="M6 13h12"/>
                                    <path d="M6 9h12"/>
                                </svg>
                            </div>
                            <h3 class="service-title">酒店预订</h3>
                            <p class="service-description">
                                协助预订商务级酒店，确保您的客户享受舒适的住宿体验。
                            </p>
                            <ul class="feature-list mt-4">
                                <li class="flex items-center space-x-2">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">商务酒店推荐</span>
                                </li>
                                <li class="flex items-center space-x-2">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">团体预订优惠</span>
                                </li>
                                <li class="flex items-center space-x-2">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">特殊需求安排</span>
                                </li>
                            </ul>
                            <div class="mt-4 text-2xl font-bold text-blue-600">
                                协商价格
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 商务套餐区域 -->
        <section class="responsive-py-20 bg-gray-50">
            <div class="container">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                        商务接待套餐
                    </h2>
                    <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                        根据不同的商务需求，我们提供多种套餐选择
                    </p>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- 基础套餐 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="text-2xl font-bold text-gray-900">基础套餐</h3>
                            <div class="text-3xl font-bold text-blue-600 mt-2">
                                RM 800
                                <span class="text-base font-normal text-gray-500">/天</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <ul class="space-y-3 mb-6">
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">机场接送（往返）</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">4小时商务用车</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">酒店推荐服务</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">基础翻译服务</span>
                                </li>
                            </ul>
                            <button class="btn btn-primary w-full" onclick="selectPackage('basic')">
                                选择基础套餐
                            </button>
                        </div>
                    </div>

                    <!-- 标准套餐 -->
                    <div class="card border-2 border-blue-500">
                        <div class="card-header">
                            <div class="flex items-center justify-between">
                                <h3 class="text-2xl font-bold text-gray-900">标准套餐</h3>
                                <div class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                                    推荐
                                </div>
                            </div>
                            <div class="text-3xl font-bold text-blue-600 mt-2">
                                RM 1,500
                                <span class="text-base font-normal text-gray-500">/天</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <ul class="space-y-3 mb-6">
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">VIP机场接送</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">8小时商务用车</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">会议室预订</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">商务酒店预订</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">专业翻译服务</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">商务餐饮安排</span>
                                </li>
                            </ul>
                            <button class="btn btn-primary w-full" onclick="selectPackage('standard')">
                                选择标准套餐
                            </button>
                        </div>
                    </div>

                    <!-- 高级套餐 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="text-2xl font-bold text-gray-900">高级套餐</h3>
                            <div class="text-3xl font-bold text-blue-600 mt-2">
                                RM 2,800
                                <span class="text-base font-normal text-gray-500">/天</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <ul class="space-y-3 mb-6">
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">豪华车队接送</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">全天商务用车</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">高级会议场地</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">五星级酒店</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">专属翻译陪同</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">高端商务餐饮</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">24小时专属服务</span>
                                </li>
                            </ul>
                            <button class="btn btn-primary w-full" onclick="selectPackage('premium')">
                                选择高级套餐
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 企业客户案例 -->
        <section class="responsive-py-20">
            <div class="container">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                        企业客户案例
                    </h2>
                    <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                        我们已为众多知名企业提供专业的商务接待服务
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- 案例 1 -->
                    <div class="card">
                        <div class="card-body">
                            <div class="flex items-center mb-4">
                                <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-blue-600">
                                        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                                        <polyline points="9,22 9,12 15,12 15,22"/>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="font-bold text-gray-900">某知名地产公司</h3>
                                    <p class="text-sm text-gray-500">年度董事会议</p>
                                </div>
                            </div>
                            <p class="text-gray-600 text-sm leading-relaxed">
                                为期3天的董事会议，包括20位高管的机场接送、五星级酒店住宿安排、
                                会议场地布置和高端商务餐饮服务。客户对我们的专业服务给予高度评价。
                            </p>
                            <div class="mt-4 flex items-center text-sm text-gray-500">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                    <circle cx="12" cy="12" r="10"/>
                                    <polyline points="12,6 12,12 16,14"/>
                                </svg>
                                服务时间：3天
                            </div>
                        </div>
                    </div>

                    <!-- 案例 2 -->
                    <div class="card">
                        <div class="card-body">
                            <div class="flex items-center mb-4">
                                <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-green-600">
                                        <circle cx="12" cy="12" r="10"/>
                                        <path d="M8 14s1.5 2 4 2 4-2 4-2"/>
                                        <line x1="9" y1="9" x2="9.01" y2="9"/>
                                        <line x1="15" y1="9" x2="15.01" y2="9"/>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="font-bold text-gray-900">国际科技公司</h3>
                                    <p class="text-sm text-gray-500">产品发布会</p>
                                </div>
                            </div>
                            <p class="text-gray-600 text-sm leading-relaxed">
                                协助举办新产品发布会，包括媒体接待、会场布置、设备租赁、
                                同声传译服务等。活动圆满成功，获得客户一致好评。
                            </p>
                            <div class="mt-4 flex items-center text-sm text-gray-500">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                    <circle cx="12" cy="12" r="10"/>
                                    <polyline points="12,6 12,12 16,14"/>
                                </svg>
                                服务时间：2天
                            </div>
                        </div>
                    </div>

                    <!-- 案例 3 -->
                    <div class="card">
                        <div class="card-body">
                            <div class="flex items-center mb-4">
                                <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mr-4">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-purple-600">
                                        <path d="M12 2L2 7l10 5 10-5-10-5z"/>
                                        <path d="M2 17l10 5 10-5"/>
                                        <path d="M2 12l10 5 10-5"/>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="font-bold text-gray-900">跨国制造企业</h3>
                                    <p class="text-sm text-gray-500">商务考察团</p>
                                </div>
                            </div>
                            <p class="text-gray-600 text-sm leading-relaxed">
                                为15人的商务考察团提供为期一周的全程服务，包括工厂参观安排、
                                商务洽谈场地、文化体验活动等。客户对服务质量非常满意。
                            </p>
                            <div class="mt-4 flex items-center text-sm text-gray-500">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                    <circle cx="12" cy="12" r="10"/>
                                    <polyline points="12,6 12,12 16,14"/>
                                </svg>
                                服务时间：7天
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 服务流程 -->
        <section class="responsive-py-20 bg-gray-50">
            <div class="container">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                        服务流程
                    </h2>
                    <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                        专业的服务流程，确保每个环节都完美执行
                    </p>
                </div>

                <div class="max-w-4xl mx-auto">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                        <!-- 步骤 1 -->
                        <div class="text-center">
                            <div class="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                                <span class="text-white font-bold text-xl">1</span>
                            </div>
                            <h3 class="text-lg font-bold text-gray-900 mb-2">需求咨询</h3>
                            <p class="text-gray-600 text-sm">
                                详细了解您的商务活动需求，制定初步方案
                            </p>
                        </div>

                        <!-- 步骤 2 -->
                        <div class="text-center">
                            <div class="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                                <span class="text-white font-bold text-xl">2</span>
                            </div>
                            <h3 class="text-lg font-bold text-gray-900 mb-2">方案定制</h3>
                            <p class="text-gray-600 text-sm">
                                根据需求定制专属服务方案，确认服务细节
                            </p>
                        </div>

                        <!-- 步骤 3 -->
                        <div class="text-center">
                            <div class="w-16 h-16 bg-orange-600 rounded-full flex items-center justify-center mx-auto mb-4">
                                <span class="text-white font-bold text-xl">3</span>
                            </div>
                            <h3 class="text-lg font-bold text-gray-900 mb-2">服务执行</h3>
                            <p class="text-gray-600 text-sm">
                                专业团队全程执行，确保每个环节完美无缺
                            </p>
                        </div>

                        <!-- 步骤 4 -->
                        <div class="text-center">
                            <div class="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                                <span class="text-white font-bold text-xl">4</span>
                            </div>
                            <h3 class="text-lg font-bold text-gray-900 mb-2">跟踪反馈</h3>
                            <p class="text-gray-600 text-sm">
                                服务结束后跟踪反馈，持续改进服务质量
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 企业联系表单 -->
        <section class="responsive-py-20" id="contact-form">
            <div class="container">
                <div class="max-w-4xl mx-auto">
                    <div class="text-center mb-12">
                        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                            企业服务咨询
                        </h2>
                        <p class="text-lg text-gray-600 leading-relaxed">
                            填写下方表单，我们的商务顾问将在2小时内与您联系，为您提供专业的商务接待方案
                        </p>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <form id="business-contact-form" class="space-y-6">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="companyName" class="form-label">公司名称 *</label>
                                        <input type="text" id="companyName" name="companyName" class="form-input" required>
                                    </div>
                                    <div>
                                        <label for="contactPerson" class="form-label">联系人 *</label>
                                        <input type="text" id="contactPerson" name="contactPerson" class="form-input" required>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="position" class="form-label">职位</label>
                                        <input type="text" id="position" name="position" class="form-input">
                                    </div>
                                    <div>
                                        <label for="email" class="form-label">邮箱 *</label>
                                        <input type="email" id="email" name="email" class="form-input" required>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="phone" class="form-label">电话号码 *</label>
                                        <input type="tel" id="phone" name="phone" class="form-input" required>
                                    </div>
                                    <div>
                                        <label for="serviceType" class="form-label">服务类型 *</label>
                                        <select id="serviceType" name="serviceType" class="form-select" required>
                                            <option value="">请选择服务类型</option>
                                            <option value="airport-transfer">机场接送</option>
                                            <option value="meeting-arrangement">会议安排</option>
                                            <option value="business-car">商务用车</option>
                                            <option value="hotel-booking">酒店预订</option>
                                            <option value="package">套餐服务</option>
                                            <option value="custom">定制服务</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="eventDate" class="form-label">活动日期</label>
                                        <input type="date" id="eventDate" name="eventDate" class="form-input">
                                    </div>
                                    <div>
                                        <label for="participantCount" class="form-label">参与人数</label>
                                        <select id="participantCount" name="participantCount" class="form-select">
                                            <option value="">请选择人数</option>
                                            <option value="1-5">1-5人</option>
                                            <option value="6-10">6-10人</option>
                                            <option value="11-20">11-20人</option>
                                            <option value="21-50">21-50人</option>
                                            <option value="50+">50人以上</option>
                                        </select>
                                    </div>
                                </div>

                                <div>
                                    <label for="requirements" class="form-label">具体需求</label>
                                    <textarea id="requirements" name="requirements" rows="4" class="form-textarea"
                                              placeholder="请详细描述您的商务活动需求，包括活动性质、特殊要求、预算范围等..."></textarea>
                                </div>

                                <div class="text-center">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                                            <polyline points="22,6 12,13 2,6"/>
                                        </svg>
                                        提交咨询申请
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- CTA 区域 -->
        <section class="responsive-py-20 bg-gradient-to-r from-gray-900 to-gray-700">
            <div class="container">
                <div class="text-center text-white">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
                        专业商务接待，成就您的商业成功
                    </h2>
                    <p class="text-lg md:text-xl mb-8 max-w-3xl mx-auto leading-relaxed opacity-90">
                        选择我们，让您的商务活动更加专业、高效、成功
                    </p>
                    <div class="button-group">
                        <a href="contact.html" class="btn btn-secondary btn-lg">
                            联系我们
                        </a>
                        <a href="tel:+60123456789" class="btn btn-outline btn-lg">
                            立即咨询
                        </a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- JavaScript -->
    <script src="../assets/js/navigation.js"></script>
    <script src="../assets/js/main.js"></script>
    <script>
        // 商务接待页面特定功能
        function selectPackage(packageType) {
            // 滚动到联系表单
            document.getElementById('contact-form').scrollIntoView({
                behavior: 'smooth'
            });

            // 根据套餐类型预填表单
            const serviceSelect = document.getElementById('serviceType');
            serviceSelect.value = 'package';

            // 在需求文本框中添加套餐信息
            const requirementsTextarea = document.getElementById('requirements');
            let packageInfo = '';

            if (packageType === 'basic') {
                packageInfo = '我对基础套餐感兴趣，包括机场接送、4小时商务用车、酒店推荐和基础翻译服务。';
            } else if (packageType === 'standard') {
                packageInfo = '我对标准套餐感兴趣，包括VIP机场接送、8小时商务用车、会议室预订、商务酒店预订、专业翻译和商务餐饮安排。';
            } else if (packageType === 'premium') {
                packageInfo = '我对高级套餐感兴趣，包括豪华车队接送、全天商务用车、高级会议场地、五星级酒店、专属翻译陪同、高端商务餐饮和24小时专属服务。';
            }

            requirementsTextarea.value = packageInfo;
        }

        // 设置活动日期最小值为今天
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('eventDate').min = today;
        });

        // 表单提交处理
        document.getElementById('business-contact-form').addEventListener('submit', function(e) {
            e.preventDefault();

            // 这里可以添加表单验证和提交逻辑
            alert('感谢您的咨询！我们的商务顾问将在2小时内与您联系。');

            // 重置表单
            this.reset();
        });
    </script>
</body>
</html>
