<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="联系 Sky Mirror World Tour - 专业的马来西亚旅游服务，24小时客服支持">
    <meta name="keywords" content="联系我们,马来西亚旅游,客服,预订咨询">
    
    <title>联系我们 - Sky Mirror World Tour</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/components.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <!-- 品牌Logo -->
            <a href="../index.html" class="nav-brand">
                <div class="nav-logo">S</div>
                <span class="nav-title">Sky Mirror World Tour</span>
            </a>

            <!-- 桌面导航菜单 -->
            <div class="nav-menu">
                <a href="../index.html" class="nav-link" data-page="home">首页</a>
                <a href="domestic-transport.html" class="nav-link" data-page="domestic-transport">境内用车</a>
                <a href="signature-tickets.html" class="nav-link" data-page="signature-tickets">特色船票</a>
                <a href="international-tours.html" class="nav-link" data-page="international-tours">国际旅游</a>
                <a href="business-reception.html" class="nav-link" data-page="business-reception">商务接待</a>
                <a href="island-hopping.html" class="nav-link" data-page="island-hopping">海岛旅游</a>
                <a href="sky-pier-cafe.html" class="nav-link" data-page="sky-pier-cafe">天空号船咖啡厅</a>
                <a href="about.html" class="nav-link" data-page="about">关于我们</a>
                <a href="contact.html" class="nav-cta active" data-page="contact">联系我们</a>
            </div>

            <!-- 移动端菜单按钮 -->
            <button type="button" class="mobile-menu-btn" id="mobileMenuBtn" aria-label="打开菜单">
                <svg id="menuIcon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="4" y1="6" x2="20" y2="6"/>
                    <line x1="4" y1="12" x2="20" y2="12"/>
                    <line x1="4" y1="18" x2="20" y2="18"/>
                </svg>
                <svg id="closeIcon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="hidden">
                    <path d="M18 6 6 18"/>
                    <path d="m6 6 12 12"/>
                </svg>
            </button>
        </div>

        <!-- 移动端菜单 -->
        <div class="mobile-menu" id="mobileMenu">
            <a href="../index.html" class="mobile-nav-link" data-page="home">首页</a>
            <a href="domestic-transport.html" class="mobile-nav-link" data-page="domestic-transport">境内用车</a>
            <a href="signature-tickets.html" class="mobile-nav-link" data-page="signature-tickets">特色船票</a>
            <a href="international-tours.html" class="mobile-nav-link" data-page="international-tours">国际旅游</a>
            <a href="business-reception.html" class="mobile-nav-link" data-page="business-reception">商务接待</a>
            <a href="island-hopping.html" class="mobile-nav-link" data-page="island-hopping">海岛旅游</a>
            <a href="sky-pier-cafe.html" class="mobile-nav-link" data-page="sky-pier-cafe">天空号船咖啡厅</a>
            <a href="about.html" class="mobile-nav-link" data-page="about">关于我们</a>
            <a href="contact.html" class="mobile-nav-link nav-cta-mobile active" data-page="contact">联系我们</a>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main>
        <!-- Hero 区域 -->
        <section class="hero-section">
            <div class="hero-background" style="background-image: url('https://images.pexels.com/photos/1271619/pexels-photo-1271619.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop')"></div>
            <div class="hero-overlay"></div>
            <div class="hero-content">
                <h1 class="text-4xl md:text-5xl lg:text-7xl font-bold mb-6 leading-tight">
                    联系我们
                </h1>
                <p class="text-lg md:text-xl mb-8 max-w-3xl mx-auto leading-relaxed opacity-90">
                    我们随时为您提供专业的旅游咨询服务，让我们一起规划您的完美旅程
                </p>
            </div>
        </section>

        <!-- 联系方式区域 -->
        <section class="responsive-py-20 bg-gray-50">
            <div class="container">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                        多种联系方式
                    </h2>
                    <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                        选择最适合您的联系方式，我们的专业团队将为您提供及时、贴心的服务
                    </p>
                </div>

                <div class="contact-grid">
                    <!-- 电话咨询 -->
                    <div class="contact-card">
                        <div class="contact-icon" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                            </svg>
                        </div>
                        <h3 class="contact-title">电话咨询</h3>
                        <div class="contact-details">
                            <div class="contact-detail">+60 12-345-6789</div>
                            <div class="contact-detail">+60 12-987-6543</div>
                        </div>
                        <p class="contact-description">
                            24小时客服热线，随时为您解答疑问
                        </p>
                    </div>

                    <!-- 微信咨询 -->
                    <div class="contact-card">
                        <div class="contact-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                            </svg>
                        </div>
                        <h3 class="contact-title">微信咨询</h3>
                        <div class="contact-details">
                            <div class="contact-detail">微信号：SkyMirrorTour</div>
                        </div>
                        <p class="contact-description">
                            扫码添加微信，获取实时旅游资讯
                        </p>
                    </div>

                    <!-- 邮件联系 -->
                    <div class="contact-card">
                        <div class="contact-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                                <polyline points="22,6 12,13 2,6"/>
                            </svg>
                        </div>
                        <h3 class="contact-title">邮件联系</h3>
                        <div class="contact-details">
                            <div class="contact-detail"><EMAIL></div>
                            <div class="contact-detail"><EMAIL></div>
                        </div>
                        <p class="contact-description">
                            发送邮件咨询，我们将在24小时内回复
                        </p>
                    </div>

                    <!-- 紧急联系 -->
                    <div class="contact-card">
                        <div class="contact-icon" style="background: linear-gradient(135deg, #ef4444, #dc2626);">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
                                <line x1="12" y1="9" x2="12" y2="13"/>
                                <line x1="12" y1="17" x2="12.01" y2="17"/>
                            </svg>
                        </div>
                        <h3 class="contact-title">紧急联系</h3>
                        <div class="contact-details">
                            <div class="contact-detail">+60 12-999-8888</div>
                        </div>
                        <p class="contact-description">
                            旅途中遇到紧急情况，请立即拨打
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 联系表单区域 -->
        <section class="responsive-py-20">
            <div class="container">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                    <!-- 左侧：表单 -->
                    <div>
                        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                            发送消息
                        </h2>
                        <p class="text-gray-600 mb-8 leading-relaxed">
                            填写下方表单，我们的专业团队将尽快与您联系，为您提供个性化的旅游方案。
                        </p>

                        <form id="contact-form" class="space-y-6">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="firstName" class="form-label">姓名 *</label>
                                    <input type="text" id="firstName" name="firstName" class="form-input" required>
                                </div>
                                <div class="form-group">
                                    <label for="email" class="form-label">邮箱 *</label>
                                    <input type="email" id="email" name="email" class="form-input" required>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="phone" class="form-label">电话</label>
                                    <input type="tel" id="phone" name="phone" class="form-input">
                                </div>
                                <div class="form-group">
                                    <label for="travelDate" class="form-label">计划出行日期</label>
                                    <input type="date" id="travelDate" name="travelDate" class="form-input">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="service" class="form-label">感兴趣的服务</label>
                                <select id="service" name="service" class="form-select">
                                    <option value="">请选择服务类型</option>
                                    <option value="signature-tickets">特色船票体验</option>
                                    <option value="domestic-transport">境内用车服务</option>
                                    <option value="international-tours">国际旅游</option>
                                    <option value="business-reception">商务接待</option>
                                    <option value="island-hopping">海岛旅游</option>
                                    <option value="sky-pier-cafe">天空号船咖啡厅</option>
                                    <option value="other">其他服务</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="message" class="form-label">详细需求 *</label>
                                <textarea id="message" name="message" rows="5" class="form-textarea" 
                                          placeholder="请详细描述您的旅游需求、人数、预算等信息..." required></textarea>
                            </div>

                            <button type="submit" class="btn btn-primary btn-lg w-full">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                                    <polyline points="22,6 12,13 2,6"/>
                                </svg>
                                发送消息
                            </button>
                        </form>
                    </div>

                    <!-- 右侧：公司信息 -->
                    <div>
                        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                            公司信息
                        </h2>

                        <!-- 联系方式 -->
                        <div class="card mb-6">
                            <div class="card-body">
                                <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2 text-blue-600">
                                        <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                                    </svg>
                                    联系方式
                                </h3>
                                <div class="space-y-4">
                                    <div class="flex items-center">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-3 text-green-600">
                                            <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                                        </svg>
                                        <div>
                                            <div class="font-medium text-gray-900">客服热线</div>
                                            <a href="tel:+60123456789" class="text-blue-600 hover:text-blue-800">+60 12-345-6789</a>
                                        </div>
                                    </div>
                                    <div class="flex items-center">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-3 text-blue-600">
                                            <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                                            <polyline points="22,6 12,13 2,6"/>
                                        </svg>
                                        <div>
                                            <div class="font-medium text-gray-900">邮箱</div>
                                            <a href="mailto:<EMAIL>" class="text-blue-600 hover:text-blue-800"><EMAIL></a>
                                        </div>
                                    </div>
                                    <div class="flex items-center">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" class="mr-3 text-green-500">
                                            <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.864 3.488"/>
                                        </svg>
                                        <div>
                                            <div class="font-medium text-gray-900">WhatsApp</div>
                                            <a href="https://wa.me/60123456789" class="text-blue-600 hover:text-blue-800" target="_blank" rel="noopener">+60 12-345-6789</a>
                                        </div>
                                    </div>
                                    <div class="flex items-center">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" class="mr-3 text-green-600">
                                            <path d="M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.17 4.203 3.002 5.55a.59.59 0 0 1 .213.665l-.39 1.48c-.019.07-.048.141-.048.213 0 .163.13.295.29.295a.326.326 0 0 0 .167-.054l1.903-1.114a.864.864 0 0 1 .717-.098 10.16 10.16 0 0 0 2.837.403c.276 0 .543-.027.811-.05-.857-2.578.157-4.972 1.932-6.446 1.703-1.415 3.882-1.98 5.853-1.838-.576-3.583-4.196-6.348-8.596-6.348zM5.785 5.991c.642 0 1.162.529 1.162 1.18 0 .659-.52 1.188-1.162 1.188-.642 0-1.162-.529-1.162-1.188 0-.651.52-1.18 1.162-1.18zm5.813 0c.642 0 1.162.529 1.162 1.18 0 .659-.52 1.188-1.162 1.188-.642 0-1.162-.529-1.162-1.188 0-.651.52-1.18 1.162-1.18zm5.34 2.867c-1.797-.052-3.746.512-5.28 1.786-1.72 1.428-2.687 3.72-1.78 6.22.942 2.453 3.666 4.229 6.884 4.229.826 0 1.622-.12 2.361-.336a.722.722 0 0 1 .598.082l1.584.926a.272.272 0 0 0 .14.045c.134 0 .24-.111.24-.248 0-.06-.023-.12-.038-.177l-.327-1.233a.582.582 0 0 1-.023-.156.49.49 0 0 1 .201-.398C23.024 18.48 24 16.82 24 14.98c0-3.21-2.931-5.837-6.656-6.088V8.858zm-3.288 1.333c.472 0 .857.383.857.857 0 .473-.385.857-.857.857a.857.857 0 0 1-.857-.857c0-.474.384-.857.857-.857zm4.288 0c.472 0 .857.383.857.857 0 .473-.385.857-.857.857a.857.857 0 0 1-.857-.857c0-.474.385-.857.857-.857z"/>
                                        </svg>
                                        <div>
                                            <div class="font-medium text-gray-900">微信</div>
                                            <span class="text-gray-600">SkyMirrorTour</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 地址信息 -->
                        <div class="card mb-6">
                            <div class="card-body">
                                <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2 text-blue-600">
                                        <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                                        <circle cx="12" cy="10" r="3"/>
                                    </svg>
                                    办公地址
                                </h3>
                                <p class="text-gray-600 leading-relaxed">
                                    马来西亚 雪兰莪州<br>
                                    瓜拉雪兰莪 天空之镜旅游中心<br>
                                    Jalan Pantai, Kuala Selangor<br>
                                    邮编：45000
                                </p>
                                <div class="mt-4">
                                    <a href="https://maps.google.com/?q=Kuala+Selangor+Sky+Mirror"
                                       target="_blank"
                                       rel="noopener"
                                       class="inline-flex items-center text-blue-600 hover:text-blue-800">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                            <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                                            <circle cx="12" cy="10" r="3"/>
                                        </svg>
                                        在地图中查看
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- 营业时间 -->
                        <div class="card mb-6">
                            <div class="card-body">
                                <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2 text-blue-600">
                                        <circle cx="12" cy="12" r="10"/>
                                        <polyline points="12,6 12,12 16,14"/>
                                    </svg>
                                    营业时间
                                </h3>
                                <div class="space-y-2 text-gray-600">
                                    <div class="flex justify-between">
                                        <span>周一至周五</span>
                                        <span>8:00 - 20:00</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>周六至周日</span>
                                        <span>8:00 - 22:00</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>节假日</span>
                                        <span>9:00 - 18:00</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 服务承诺 -->
                        <div class="card">
                            <div class="card-body">
                                <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2 text-blue-600">
                                        <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10"/>
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    服务承诺
                                </h3>
                                <ul class="space-y-2 text-gray-600">
                                    <li class="flex items-center">
                                        <div class="w-2 h-2 bg-blue-600 rounded-full mr-3"></div>
                                        24小时内回复您的咨询
                                    </li>
                                    <li class="flex items-center">
                                        <div class="w-2 h-2 bg-blue-600 rounded-full mr-3"></div>
                                        专业的旅游规划建议
                                    </li>
                                    <li class="flex items-center">
                                        <div class="w-2 h-2 bg-blue-600 rounded-full mr-3"></div>
                                        透明的价格，无隐藏费用
                                    </li>
                                    <li class="flex items-center">
                                        <div class="w-2 h-2 bg-blue-600 rounded-full mr-3"></div>
                                        全程贴心的客户服务
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- JavaScript -->
    <script src="../assets/js/navigation.js"></script>
    <script src="../assets/js/contact-form.js"></script>
    <script src="../assets/js/main.js"></script>
</body>
</html>
