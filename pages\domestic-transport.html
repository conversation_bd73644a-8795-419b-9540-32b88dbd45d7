<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Sky Mirror World Tour 境内用车服务 - 机场接送、半天包车、全天包车，专业司机，安全舒适">
    <meta name="keywords" content="境内用车,机场接送,包车服务,马来西亚交通,专业司机">
    
    <title>境内用车服务 - Sky Mirror World Tour</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/components.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <!-- 品牌Logo -->
            <a href="../index.html" class="nav-brand">
                <div class="nav-logo">S</div>
                <span class="nav-title">Sky Mirror World Tour</span>
            </a>

            <!-- 桌面导航菜单 -->
            <div class="nav-menu">
                <a href="../index.html" class="nav-link" data-page="home">首页</a>
                <a href="domestic-transport.html" class="nav-link active" data-page="domestic-transport">境内用车</a>
                <a href="signature-tickets.html" class="nav-link" data-page="signature-tickets">特色船票</a>
                <a href="international-tours.html" class="nav-link" data-page="international-tours">国际旅游</a>
                <a href="business-reception.html" class="nav-link" data-page="business-reception">商务接待</a>
                <a href="island-hopping.html" class="nav-link" data-page="island-hopping">海岛旅游</a>
                <a href="sky-pier-cafe.html" class="nav-link" data-page="sky-pier-cafe">天空号船咖啡厅</a>
                <a href="about.html" class="nav-link" data-page="about">关于我们</a>
                <a href="contact.html" class="nav-cta">联系我们</a>
            </div>

            <!-- 移动端菜单按钮 -->
            <button type="button" class="mobile-menu-btn" id="mobileMenuBtn" aria-label="打开菜单">
                <svg id="menuIcon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="4" y1="6" x2="20" y2="6"/>
                    <line x1="4" y1="12" x2="20" y2="12"/>
                    <line x1="4" y1="18" x2="20" y2="18"/>
                </svg>
                <svg id="closeIcon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="hidden">
                    <path d="M18 6 6 18"/>
                    <path d="m6 6 12 12"/>
                </svg>
            </button>
        </div>

        <!-- 移动端菜单 -->
        <div class="mobile-menu" id="mobileMenu">
            <a href="../index.html" class="mobile-nav-link" data-page="home">首页</a>
            <a href="domestic-transport.html" class="mobile-nav-link active" data-page="domestic-transport">境内用车</a>
            <a href="signature-tickets.html" class="mobile-nav-link" data-page="signature-tickets">特色船票</a>
            <a href="international-tours.html" class="mobile-nav-link" data-page="international-tours">国际旅游</a>
            <a href="business-reception.html" class="mobile-nav-link" data-page="business-reception">商务接待</a>
            <a href="island-hopping.html" class="mobile-nav-link" data-page="island-hopping">海岛旅游</a>
            <a href="sky-pier-cafe.html" class="mobile-nav-link" data-page="sky-pier-cafe">天空号船咖啡厅</a>
            <a href="about.html" class="mobile-nav-link" data-page="about">关于我们</a>
            <a href="contact.html" class="mobile-nav-link nav-cta-mobile">联系我们</a>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main>
        <!-- Hero 区域 -->
        <section class="hero-section">
            <div class="hero-background" style="background-image: url('https://images.pexels.com/photos/116675/pexels-photo-116675.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop')"></div>
            <div class="hero-overlay"></div>
            <div class="hero-content">
                <h1 class="text-4xl md:text-5xl lg:text-7xl font-bold mb-6 leading-tight">
                    境内用车服务
                </h1>
                <p class="text-lg md:text-xl mb-8 max-w-3xl mx-auto leading-relaxed opacity-90">
                    专业司机，安全舒适，为您提供机场接送、半天包车、全天包车等多样化交通服务
                </p>
                <div class="button-group">
                    <a href="contact.html" class="btn btn-primary btn-lg">
                        立即预订
                    </a>
                    <a href="#services" class="btn btn-outline btn-lg">
                        查看服务
                    </a>
                </div>
            </div>
        </section>

        <!-- 服务类型区域 -->
        <section class="responsive-py-20 bg-gray-50" id="services">
            <div class="container">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                        用车服务类型
                    </h2>
                    <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                        根据您的需求选择最适合的用车服务，我们提供专业的司机和舒适的车辆
                    </p>
                </div>

                <div class="services-grid">
                    <!-- 机场接送 -->
                    <div class="service-card">
                        <div class="p-6">
                            <div class="service-icon" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                    <path d="M17.8 19.2 16 11l3.5-3.5C21 6 21 4 19 4s-2 2-3.5 3.5L11 16l-8.2 1.8c-.5.1-.8.6-.8 1.1s.3 1 .8 1.1L11 21.8c.5.1 1-.3 1.1-.8L14 13l3.5 3.5c1.5 1.5 3.5 1.5 3.5-1.5s-2-2-3.5-3.5Z"/>
                                </svg>
                            </div>
                            <h3 class="service-title">机场接送</h3>
                            <p class="service-description">
                                准时可靠的机场接送服务，专业司机提前到达，让您的旅程从舒适开始。
                            </p>
                            <ul class="feature-list mt-4">
                                <li class="flex items-center space-x-2">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">24小时服务</span>
                                </li>
                                <li class="flex items-center space-x-2">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">航班动态跟踪</span>
                                </li>
                                <li class="flex items-center space-x-2">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">免费等候60分钟</span>
                                </li>
                            </ul>
                            <div class="mt-4 text-2xl font-bold text-blue-600">
                                RM 80 起
                            </div>
                        </div>
                    </div>

                    <!-- 半天包车 -->
                    <div class="service-card">
                        <div class="p-6">
                            <div class="service-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"/>
                                    <polyline points="12,6 12,12 16,14"/>
                                </svg>
                            </div>
                            <h3 class="service-title">半天包车</h3>
                            <p class="service-description">
                                4小时灵活用车服务，适合城市观光、购物或短途旅行，自由安排行程。
                            </p>
                            <ul class="feature-list mt-4">
                                <li class="flex items-center space-x-2">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">4小时服务</span>
                                </li>
                                <li class="flex items-center space-x-2">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">自由安排路线</span>
                                </li>
                                <li class="flex items-center space-x-2">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">专业导游建议</span>
                                </li>
                            </ul>
                            <div class="mt-4 text-2xl font-bold text-blue-600">
                                RM 200 起
                            </div>
                        </div>
                    </div>

                    <!-- 全天包车 -->
                    <div class="service-card">
                        <div class="p-6">
                            <div class="service-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"/>
                                    <path d="M12 6v6l4 2"/>
                                </svg>
                            </div>
                            <h3 class="service-title">全天包车</h3>
                            <p class="service-description">
                                8-10小时全天用车服务，适合深度游览、多景点游览或商务活动。
                            </p>
                            <ul class="feature-list mt-4">
                                <li class="flex items-center space-x-2">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">8-10小时服务</span>
                                </li>
                                <li class="flex items-center space-x-2">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">多景点游览</span>
                                </li>
                                <li class="flex items-center space-x-2">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">司机餐费包含</span>
                                </li>
                            </ul>
                            <div class="mt-4 text-2xl font-bold text-blue-600">
                                RM 350 起
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 车型选择区域 -->
        <section class="responsive-py-20">
            <div class="container">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                        车型选择
                    </h2>
                    <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                        多种车型满足不同需求，从经济型到豪华型，为您提供最适合的出行方案
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <!-- 经济型轿车 -->
                    <div class="card">
                        <div class="aspect-video bg-gray-200 rounded-t-xl overflow-hidden">
                            <img src="https://images.pexels.com/photos/116675/pexels-photo-116675.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop" 
                                 alt="经济型轿车" class="w-full h-full object-cover">
                        </div>
                        <div class="card-body">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">经济型轿车</h3>
                            <p class="text-gray-600 text-sm mb-3">适合1-3人，经济实惠</p>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>• 丰田 Vios / 本田 City</li>
                                <li>• 空调、音响系统</li>
                                <li>• 舒适座椅</li>
                            </ul>
                        </div>
                    </div>

                    <!-- 舒适型MPV -->
                    <div class="card">
                        <div class="aspect-video bg-gray-200 rounded-t-xl overflow-hidden">
                            <img src="https://images.pexels.com/photos/116675/pexels-photo-116675.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop" 
                                 alt="舒适型MPV" class="w-full h-full object-cover">
                        </div>
                        <div class="card-body">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">舒适型MPV</h3>
                            <p class="text-gray-600 text-sm mb-3">适合4-6人，空间宽敞</p>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>• 丰田 Innova / 本田 Odyssey</li>
                                <li>• 大空间行李箱</li>
                                <li>• 独立空调控制</li>
                            </ul>
                        </div>
                    </div>

                    <!-- 豪华型轿车 -->
                    <div class="card">
                        <div class="aspect-video bg-gray-200 rounded-t-xl overflow-hidden">
                            <img src="https://images.pexels.com/photos/116675/pexels-photo-116675.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop" 
                                 alt="豪华型轿车" class="w-full h-full object-cover">
                        </div>
                        <div class="card-body">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">豪华型轿车</h3>
                            <p class="text-gray-600 text-sm mb-3">适合商务出行，尊贵体验</p>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>• 奔驰 E-Class / 宝马 5系</li>
                                <li>• 真皮座椅</li>
                                <li>• 高端音响系统</li>
                            </ul>
                        </div>
                    </div>

                    <!-- 大型客车 -->
                    <div class="card">
                        <div class="aspect-video bg-gray-200 rounded-t-xl overflow-hidden">
                            <img src="https://images.pexels.com/photos/116675/pexels-photo-116675.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop" 
                                 alt="大型客车" class="w-full h-full object-cover">
                        </div>
                        <div class="card-body">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">大型客车</h3>
                            <p class="text-gray-600 text-sm mb-3">适合团体出行，10人以上</p>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>• 丰田 Hiace / 奔驰 Sprinter</li>
                                <li>• 超大行李空间</li>
                                <li>• 团体出行首选</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 服务特色区域 -->
        <section class="responsive-py-20 bg-gray-50">
            <div class="container">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                        服务特色
                    </h2>
                </div>

                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10"/>
                                <path d="M9 12l2 2 4-4"/>
                            </svg>
                        </div>
                        <h3 class="feature-title">安全保障</h3>
                        <p class="feature-description">
                            所有车辆定期保养，司机经验丰富，为您提供安全可靠的出行保障
                        </p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                <circle cx="12" cy="12" r="10"/>
                                <polyline points="12,6 12,12 16,14"/>
                            </svg>
                        </div>
                        <h3 class="feature-title">准时服务</h3>
                        <p class="feature-description">
                            严格按照约定时间提供服务，绝不让您等待，珍惜您的宝贵时间
                        </p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
                                <circle cx="9" cy="7" r="4"/>
                                <path d="M22 21v-2a4 4 0 0 0-3-3.87"/>
                                <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                            </svg>
                        </div>
                        <h3 class="feature-title">专业司机</h3>
                        <p class="feature-description">
                            持证上岗的专业司机，熟悉当地路况，提供贴心的服务体验
                        </p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                <line x1="12" y1="1" x2="12" y2="23"/>
                                <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/>
                            </svg>
                        </div>
                        <h3 class="feature-title">透明价格</h3>
                        <p class="feature-description">
                            明码标价，无隐藏费用，让您清楚了解每一项服务的费用
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- CTA 区域 -->
        <section class="responsive-py-20 bg-gradient-to-r from-blue-600 to-emerald-600">
            <div class="container">
                <div class="text-center text-white">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
                        立即预订您的专属用车服务
                    </h2>
                    <p class="text-lg md:text-xl mb-8 max-w-3xl mx-auto leading-relaxed opacity-90">
                        联系我们获取个性化的用车方案和优惠价格
                    </p>
                    <div class="button-group">
                        <a href="contact.html" class="btn btn-secondary btn-lg">
                            立即预订
                        </a>
                        <a href="tel:+60123456789" class="btn btn-outline btn-lg">
                            电话咨询
                        </a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- JavaScript -->
    <script src="../assets/js/navigation.js"></script>
    <script src="../assets/js/main.js"></script>
</body>
</html>
