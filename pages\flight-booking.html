<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Sky Mirror World Tour 机票代订服务 - 国内航线、国际航线机票预订，特价机票优惠">
    <meta name="keywords" content="机票代订,机票预订,国内航线,国际航线,特价机票,航空公司">
    
    <title>机票代订服务 - Sky Mirror World Tour</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/components.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <!-- 品牌Logo -->
            <a href="../index.html" class="nav-brand">
                <div class="nav-logo">S</div>
                <span class="nav-title">Sky Mirror World Tour</span>
            </a>

            <!-- 桌面导航菜单 -->
            <div class="nav-menu">
                <a href="../index.html" class="nav-link" data-page="home">首页</a>
                <a href="domestic-transport.html" class="nav-link" data-page="domestic-transport">境内用车</a>
                <a href="signature-tickets.html" class="nav-link" data-page="signature-tickets">特色船票</a>
                <a href="international-tours.html" class="nav-link" data-page="international-tours">国际旅游</a>
                <a href="business-reception.html" class="nav-link" data-page="business-reception">商务接待</a>
                <a href="island-hopping.html" class="nav-link" data-page="island-hopping">海岛旅游</a>
                <a href="hotel-booking.html" class="nav-link" data-page="hotel-booking">酒店代订</a>
                <a href="flight-booking.html" class="nav-link active" data-page="flight-booking">机票代订</a>
                <a href="student-groups.html" class="nav-link" data-page="student-groups">学生团</a>
                <a href="medical-tourism.html" class="nav-link" data-page="medical-tourism">医疗旅游</a>
                <a href="corporate-team-building.html" class="nav-link" data-page="corporate-team-building">公司团建</a>
                <a href="sky-pier-cafe.html" class="nav-link" data-page="sky-pier-cafe">天空号船咖啡厅</a>
                <a href="about.html" class="nav-link" data-page="about">关于我们</a>
                <a href="contact.html" class="nav-cta">联系我们</a>
            </div>

            <!-- 移动端菜单按钮 -->
            <button type="button" class="mobile-menu-btn" id="mobileMenuBtn" aria-label="打开菜单">
                <svg id="menuIcon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="4" y1="6" x2="20" y2="6"/>
                    <line x1="4" y1="12" x2="20" y2="12"/>
                    <line x1="4" y1="18" x2="20" y2="18"/>
                </svg>
                <svg id="closeIcon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="hidden">
                    <path d="M18 6 6 18"/>
                    <path d="m6 6 12 12"/>
                </svg>
            </button>
        </div>

        <!-- 移动端菜单 -->
        <div class="mobile-menu" id="mobileMenu">
            <a href="../index.html" class="mobile-nav-link" data-page="home">首页</a>
            <a href="domestic-transport.html" class="mobile-nav-link" data-page="domestic-transport">境内用车</a>
            <a href="signature-tickets.html" class="mobile-nav-link" data-page="signature-tickets">特色船票</a>
            <a href="international-tours.html" class="mobile-nav-link" data-page="international-tours">国际旅游</a>
            <a href="business-reception.html" class="mobile-nav-link" data-page="business-reception">商务接待</a>
            <a href="island-hopping.html" class="mobile-nav-link" data-page="island-hopping">海岛旅游</a>
            <a href="hotel-booking.html" class="mobile-nav-link" data-page="hotel-booking">酒店代订</a>
            <a href="flight-booking.html" class="mobile-nav-link active" data-page="flight-booking">机票代订</a>
            <a href="student-groups.html" class="mobile-nav-link" data-page="student-groups">学生团</a>
            <a href="medical-tourism.html" class="mobile-nav-link" data-page="medical-tourism">医疗旅游</a>
            <a href="corporate-team-building.html" class="mobile-nav-link" data-page="corporate-team-building">公司团建</a>
            <a href="sky-pier-cafe.html" class="mobile-nav-link" data-page="sky-pier-cafe">天空号船咖啡厅</a>
            <a href="about.html" class="mobile-nav-link" data-page="about">关于我们</a>
            <a href="contact.html" class="mobile-nav-link nav-cta-mobile">联系我们</a>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main>
        <!-- Hero 区域 -->
        <section class="hero-section">
            <div class="hero-background" style="background-image: url('https://images.pexels.com/photos/912050/pexels-photo-912050.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop')"></div>
            <div class="hero-overlay"></div>
            <div class="hero-content">
                <h1 class="text-4xl md:text-5xl lg:text-7xl font-bold mb-6 leading-tight">
                    机票代订服务
                </h1>
                <p class="text-lg md:text-xl mb-8 max-w-3xl mx-auto leading-relaxed opacity-90">
                    为您提供全球航线机票预订服务，从国内航线到国际航线，享受优惠价格和专业服务
                </p>
                <div class="button-group">
                    <a href="#search" class="btn btn-primary btn-lg">
                        搜索机票
                    </a>
                    <a href="#airlines" class="btn btn-outline btn-lg">
                        合作航空
                    </a>
                </div>
            </div>
        </section>

        <!-- 机票搜索区域 -->
        <section class="responsive-py-20 bg-gray-50" id="search">
            <div class="container">
                <div class="max-w-6xl mx-auto">
                    <div class="text-center mb-12">
                        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                            搜索最优机票
                        </h2>
                        <p class="text-lg text-gray-600 leading-relaxed">
                            输入您的行程信息，我们为您找到最优惠的机票选择
                        </p>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <!-- 行程类型选择 -->
                            <div class="mb-6">
                                <div class="flex flex-wrap gap-4">
                                    <label class="flex items-center">
                                        <input type="radio" name="tripType" value="roundtrip" class="mr-2" checked>
                                        <span class="text-gray-700">往返</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="tripType" value="oneway" class="mr-2">
                                        <span class="text-gray-700">单程</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="tripType" value="multicity" class="mr-2">
                                        <span class="text-gray-700">多程</span>
                                    </label>
                                </div>
                            </div>

                            <form id="flight-search-form" class="space-y-6">
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                                    <div>
                                        <label for="departure" class="form-label">出发地 *</label>
                                        <input type="text" id="departure" name="departure" class="form-input" 
                                               placeholder="城市或机场代码" required>
                                    </div>
                                    <div>
                                        <label for="destination" class="form-label">目的地 *</label>
                                        <input type="text" id="destination" name="destination" class="form-input" 
                                               placeholder="城市或机场代码" required>
                                    </div>
                                    <div>
                                        <label for="departureDate" class="form-label">出发日期 *</label>
                                        <input type="date" id="departureDate" name="departureDate" class="form-input" required>
                                    </div>
                                    <div id="returnDateField">
                                        <label for="returnDate" class="form-label">返程日期</label>
                                        <input type="date" id="returnDate" name="returnDate" class="form-input">
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    <div>
                                        <label for="passengers" class="form-label">乘客数量</label>
                                        <select id="passengers" name="passengers" class="form-select">
                                            <option value="1">1位乘客</option>
                                            <option value="2">2位乘客</option>
                                            <option value="3">3位乘客</option>
                                            <option value="4">4位乘客</option>
                                            <option value="5+">5位乘客以上</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label for="class" class="form-label">舱位等级</label>
                                        <select id="class" name="class" class="form-select">
                                            <option value="economy">经济舱</option>
                                            <option value="premium">高级经济舱</option>
                                            <option value="business">商务舱</option>
                                            <option value="first">头等舱</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label for="airline" class="form-label">偏好航空公司</label>
                                        <select id="airline" name="airline" class="form-select">
                                            <option value="">无偏好</option>
                                            <option value="mas">马来西亚航空</option>
                                            <option value="airasia">亚洲航空</option>
                                            <option value="singapore">新加坡航空</option>
                                            <option value="thai">泰国航空</option>
                                            <option value="cathay">国泰航空</option>
                                            <option value="emirates">阿联酋航空</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="text-center">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <circle cx="11" cy="11" r="8"/>
                                            <path d="M21 21l-4.35-4.35"/>
                                        </svg>
                                        搜索机票
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 航线分类 -->
        <section class="responsive-py-20" id="routes">
            <div class="container">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                        热门航线
                    </h2>
                    <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                        精选国内外热门航线，为您的出行提供便捷选择
                    </p>
                </div>

                <!-- 航线分类标签 -->
                <div class="flex justify-center mb-12">
                    <div class="inline-flex rounded-lg border border-gray-200 bg-gray-50 p-1">
                        <button type="button" class="route-tab active" data-tab="domestic" onclick="switchRouteTab('domestic')">
                            国内航线
                        </button>
                        <button type="button" class="route-tab" data-tab="international" onclick="switchRouteTab('international')">
                            国际航线
                        </button>
                    </div>
                </div>

                <!-- 国内航线 -->
                <div id="domestic-routes" class="route-content active">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        <!-- 吉隆坡-槟城 -->
                        <div class="card">
                            <div class="card-body">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center">
                                        <div class="text-lg font-bold text-gray-900">KUL</div>
                                        <div class="mx-4 flex-1">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-blue-600">
                                                <path d="M17.8 19.2 16 11l3.5-3.5C21 6 21 4 19 4s-2 2-3.5 3.5L11 16l-8.2 1.8c-.5.1-.8.6-.8 1.1s.3 1 .8 1.1L11 21.8c.5.1 1-.3 1.1-.8L14 13l3.5 3.5c1.5 1.5 3.5 1.5 3.5-1.5s-2-2-3.5-3.5Z"/>
                                            </svg>
                                        </div>
                                        <div class="text-lg font-bold text-gray-900">PEN</div>
                                    </div>
                                    <div class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                                        热门
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <h3 class="font-semibold text-gray-900">吉隆坡 → 槟城</h3>
                                    <p class="text-sm text-gray-600">飞行时间：1小时20分钟</p>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="text-xl font-bold text-blue-600">
                                        RM 89 起
                                    </div>
                                    <button type="button" class="btn btn-primary btn-sm" onclick="searchRoute('KUL', 'PEN')">
                                        查看航班
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 吉隆坡-哥打京那巴鲁 -->
                        <div class="card">
                            <div class="card-body">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center">
                                        <div class="text-lg font-bold text-gray-900">KUL</div>
                                        <div class="mx-4 flex-1">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-blue-600">
                                                <path d="M17.8 19.2 16 11l3.5-3.5C21 6 21 4 19 4s-2 2-3.5 3.5L11 16l-8.2 1.8c-.5.1-.8.6-.8 1.1s.3 1 .8 1.1L11 21.8c.5.1 1-.3 1.1-.8L14 13l3.5 3.5c1.5 1.5 3.5 1.5 3.5-1.5s-2-2-3.5-3.5Z"/>
                                            </svg>
                                        </div>
                                        <div class="text-lg font-bold text-gray-900">BKI</div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <h3 class="font-semibold text-gray-900">吉隆坡 → 哥打京那巴鲁</h3>
                                    <p class="text-sm text-gray-600">飞行时间：2小时30分钟</p>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="text-xl font-bold text-blue-600">
                                        RM 159 起
                                    </div>
                                    <button type="button" class="btn btn-primary btn-sm" onclick="searchRoute('KUL', 'BKI')">
                                        查看航班
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 吉隆坡-古晋 -->
                        <div class="card">
                            <div class="card-body">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center">
                                        <div class="text-lg font-bold text-gray-900">KUL</div>
                                        <div class="mx-4 flex-1">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-blue-600">
                                                <path d="M17.8 19.2 16 11l3.5-3.5C21 6 21 4 19 4s-2 2-3.5 3.5L11 16l-8.2 1.8c-.5.1-.8.6-.8 1.1s.3 1 .8 1.1L11 21.8c.5.1 1-.3 1.1-.8L14 13l3.5 3.5c1.5 1.5 3.5 1.5 3.5-1.5s-2-2-3.5-3.5Z"/>
                                            </svg>
                                        </div>
                                        <div class="text-lg font-bold text-gray-900">KCH</div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <h3 class="font-semibold text-gray-900">吉隆坡 → 古晋</h3>
                                    <p class="text-sm text-gray-600">飞行时间：1小时45分钟</p>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="text-xl font-bold text-blue-600">
                                        RM 129 起
                                    </div>
                                    <button type="button" class="btn btn-primary btn-sm" onclick="searchRoute('KUL', 'KCH')">
                                        查看航班
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 国际航线 -->
                <div id="international-routes" class="route-content">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        <!-- 吉隆坡-新加坡 -->
                        <div class="card">
                            <div class="card-body">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center">
                                        <div class="text-lg font-bold text-gray-900">KUL</div>
                                        <div class="mx-4 flex-1">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-blue-600">
                                                <path d="M17.8 19.2 16 11l3.5-3.5C21 6 21 4 19 4s-2 2-3.5 3.5L11 16l-8.2 1.8c-.5.1-.8.6-.8 1.1s.3 1 .8 1.1L11 21.8c.5.1 1-.3 1.1-.8L14 13l3.5 3.5c1.5 1.5 3.5 1.5 3.5-1.5s-2-2-3.5-3.5Z"/>
                                            </svg>
                                        </div>
                                        <div class="text-lg font-bold text-gray-900">SIN</div>
                                    </div>
                                    <div class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                                        热门
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <h3 class="font-semibold text-gray-900">吉隆坡 → 新加坡</h3>
                                    <p class="text-sm text-gray-600">飞行时间：1小时30分钟</p>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="text-xl font-bold text-blue-600">
                                        RM 199 起
                                    </div>
                                    <button type="button" class="btn btn-primary btn-sm" onclick="searchRoute('KUL', 'SIN')">
                                        查看航班
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 吉隆坡-曼谷 -->
                        <div class="card">
                            <div class="card-body">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center">
                                        <div class="text-lg font-bold text-gray-900">KUL</div>
                                        <div class="mx-4 flex-1">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-blue-600">
                                                <path d="M17.8 19.2 16 11l3.5-3.5C21 6 21 4 19 4s-2 2-3.5 3.5L11 16l-8.2 1.8c-.5.1-.8.6-.8 1.1s.3 1 .8 1.1L11 21.8c.5.1 1-.3 1.1-.8L14 13l3.5 3.5c1.5 1.5 3.5 1.5 3.5-1.5s-2-2-3.5-3.5Z"/>
                                            </svg>
                                        </div>
                                        <div class="text-lg font-bold text-gray-900">BKK</div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <h3 class="font-semibold text-gray-900">吉隆坡 → 曼谷</h3>
                                    <p class="text-sm text-gray-600">飞行时间：2小时15分钟</p>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="text-xl font-bold text-blue-600">
                                        RM 299 起
                                    </div>
                                    <button type="button" class="btn btn-primary btn-sm" onclick="searchRoute('KUL', 'BKK')">
                                        查看航班
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 吉隆坡-东京 -->
                        <div class="card">
                            <div class="card-body">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center">
                                        <div class="text-lg font-bold text-gray-900">KUL</div>
                                        <div class="mx-4 flex-1">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-blue-600">
                                                <path d="M17.8 19.2 16 11l3.5-3.5C21 6 21 4 19 4s-2 2-3.5 3.5L11 16l-8.2 1.8c-.5.1-.8.6-.8 1.1s.3 1 .8 1.1L11 21.8c.5.1 1-.3 1.1-.8L14 13l3.5 3.5c1.5 1.5 3.5 1.5 3.5-1.5s-2-2-3.5-3.5Z"/>
                                            </svg>
                                        </div>
                                        <div class="text-lg font-bold text-gray-900">NRT</div>
                                    </div>
                                    <div class="bg-orange-100 text-orange-800 px-2 py-1 rounded-full text-xs font-medium">
                                        特价
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <h3 class="font-semibold text-gray-900">吉隆坡 → 东京</h3>
                                    <p class="text-sm text-gray-600">飞行时间：7小时</p>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="text-xl font-bold text-blue-600">
                                        RM 899 起
                                    </div>
                                    <button type="button" class="btn btn-primary btn-sm" onclick="searchRoute('KUL', 'NRT')">
                                        查看航班
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 合作航空公司 -->
        <section class="responsive-py-20 bg-gray-50" id="airlines">
            <div class="container">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                        合作航空公司
                    </h2>
                    <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                        与全球知名航空公司合作，为您提供优质的飞行体验和优惠价格
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- 马来西亚航空 -->
                    <div class="card">
                        <div class="card-body text-center">
                            <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-red-600">
                                    <path d="M17.8 19.2 16 11l3.5-3.5C21 6 21 4 19 4s-2 2-3.5 3.5L11 16l-8.2 1.8c-.5.1-.8.6-.8 1.1s.3 1 .8 1.1L11 21.8c.5.1 1-.3 1.1-.8L14 13l3.5 3.5c1.5 1.5 3.5 1.5 3.5-1.5s-2-2-3.5-3.5Z"/>
                                </svg>
                            </div>
                            <h3 class="text-lg font-bold text-gray-900 mb-2">马来西亚航空</h3>
                            <p class="text-gray-600 text-sm mb-4">
                                马来西亚国家航空公司，提供优质的国内外航线服务
                            </p>
                            <div class="text-sm text-gray-500">
                                覆盖全球60多个目的地
                            </div>
                        </div>
                    </div>

                    <!-- 亚洲航空 -->
                    <div class="card">
                        <div class="card-body text-center">
                            <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-red-600">
                                    <path d="M17.8 19.2 16 11l3.5-3.5C21 6 21 4 19 4s-2 2-3.5 3.5L11 16l-8.2 1.8c-.5.1-.8.6-.8 1.1s.3 1 .8 1.1L11 21.8c.5.1 1-.3 1.1-.8L14 13l3.5 3.5c1.5 1.5 3.5 1.5 3.5-1.5s-2-2-3.5-3.5Z"/>
                                </svg>
                            </div>
                            <h3 class="text-lg font-bold text-gray-900 mb-2">亚洲航空</h3>
                            <p class="text-gray-600 text-sm mb-4">
                                亚洲领先的低成本航空公司，提供经济实惠的航班选择
                            </p>
                            <div class="text-sm text-gray-500">
                                东南亚最大的低成本航空网络
                            </div>
                        </div>
                    </div>

                    <!-- 新加坡航空 -->
                    <div class="card">
                        <div class="card-body text-center">
                            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-blue-600">
                                    <path d="M17.8 19.2 16 11l3.5-3.5C21 6 21 4 19 4s-2 2-3.5 3.5L11 16l-8.2 1.8c-.5.1-.8.6-.8 1.1s.3 1 .8 1.1L11 21.8c.5.1 1-.3 1.1-.8L14 13l3.5 3.5c1.5 1.5 3.5 1.5 3.5-1.5s-2-2-3.5-3.5Z"/>
                                </svg>
                            </div>
                            <h3 class="text-lg font-bold text-gray-900 mb-2">新加坡航空</h3>
                            <p class="text-gray-600 text-sm mb-4">
                                世界知名的五星级航空公司，提供卓越的服务品质
                            </p>
                            <div class="text-sm text-gray-500">
                                连接全球130多个城市
                            </div>
                        </div>
                    </div>

                    <!-- 泰国航空 -->
                    <div class="card">
                        <div class="card-body text-center">
                            <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-purple-600">
                                    <path d="M17.8 19.2 16 11l3.5-3.5C21 6 21 4 19 4s-2 2-3.5 3.5L11 16l-8.2 1.8c-.5.1-.8.6-.8 1.1s.3 1 .8 1.1L11 21.8c.5.1 1-.3 1.1-.8L14 13l3.5 3.5c1.5 1.5 3.5 1.5 3.5-1.5s-2-2-3.5-3.5Z"/>
                                </svg>
                            </div>
                            <h3 class="text-lg font-bold text-gray-900 mb-2">泰国航空</h3>
                            <p class="text-gray-600 text-sm mb-4">
                                泰国国家航空公司，以优质服务和泰式待客之道闻名
                            </p>
                            <div class="text-sm text-gray-500">
                                星空联盟创始成员
                            </div>
                        </div>
                    </div>

                    <!-- 国泰航空 -->
                    <div class="card">
                        <div class="card-body text-center">
                            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-green-600">
                                    <path d="M17.8 19.2 16 11l3.5-3.5C21 6 21 4 19 4s-2 2-3.5 3.5L11 16l-8.2 1.8c-.5.1-.8.6-.8 1.1s.3 1 .8 1.1L11 21.8c.5.1 1-.3 1.1-.8L14 13l3.5 3.5c1.5 1.5 3.5 1.5 3.5-1.5s-2-2-3.5-3.5Z"/>
                                </svg>
                            </div>
                            <h3 class="text-lg font-bold text-gray-900 mb-2">国泰航空</h3>
                            <p class="text-gray-600 text-sm mb-4">
                                香港的国际航空公司，提供优质的亚洲及国际航线服务
                            </p>
                            <div class="text-sm text-gray-500">
                                寰宇一家联盟成员
                            </div>
                        </div>
                    </div>

                    <!-- 阿联酋航空 -->
                    <div class="card">
                        <div class="card-body text-center">
                            <div class="w-16 h-16 bg-gold-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-gold-600">
                                    <path d="M17.8 19.2 16 11l3.5-3.5C21 6 21 4 19 4s-2 2-3.5 3.5L11 16l-8.2 1.8c-.5.1-.8.6-.8 1.1s.3 1 .8 1.1L11 21.8c.5.1 1-.3 1.1-.8L14 13l3.5 3.5c1.5 1.5 3.5 1.5 3.5-1.5s-2-2-3.5-3.5Z"/>
                                </svg>
                            </div>
                            <h3 class="text-lg font-bold text-gray-900 mb-2">阿联酋航空</h3>
                            <p class="text-gray-600 text-sm mb-4">
                                世界领先的国际航空公司，以豪华服务和先进设施著称
                            </p>
                            <div class="text-sm text-gray-500">
                                连接全球150多个目的地
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 特价机票 -->
        <section class="responsive-py-20">
            <div class="container">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                        特价机票优惠
                    </h2>
                    <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                        限时特价机票优惠，为您的旅行节省更多费用
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <!-- 早鸟优惠 -->
                    <div class="card border-2 border-blue-500">
                        <div class="card-body">
                            <div class="flex items-center mb-4">
                                <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-blue-600">
                                        <circle cx="12" cy="12" r="10"/>
                                        <polyline points="12,6 12,12 16,14"/>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-xl font-bold text-gray-900">早鸟特价</h3>
                                    <p class="text-blue-600 font-semibold">最高可省40%</p>
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4 leading-relaxed">
                                提前60天预订国际航班，享受早鸟特价优惠。适用于精选航线，数量有限。
                            </p>
                            <ul class="space-y-2 mb-4">
                                <li class="flex items-center text-sm text-gray-600">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">提前60天预订</span>
                                </li>
                                <li class="flex items-center text-sm text-gray-600">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">免费改期一次</span>
                                </li>
                                <li class="flex items-center text-sm text-gray-600">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">包含餐食</span>
                                </li>
                            </ul>
                            <button type="button" class="btn btn-primary w-full" onclick="showEarlyBirdFlights()">
                                查看早鸟特价
                            </button>
                        </div>
                    </div>

                    <!-- 团体优惠 -->
                    <div class="card border-2 border-green-500">
                        <div class="card-body">
                            <div class="flex items-center mb-4">
                                <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-green-600">
                                        <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
                                        <circle cx="9" cy="7" r="4"/>
                                        <path d="M22 21v-2a4 4 0 0 0-3-3.87"/>
                                        <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-xl font-bold text-gray-900">团体优惠</h3>
                                    <p class="text-green-600 font-semibold">10人以上享优惠</p>
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4 leading-relaxed">
                                10人以上团体预订享受特别优惠价格，适合家庭旅行、公司团建等。
                            </p>
                            <ul class="space-y-2 mb-4">
                                <li class="flex items-center text-sm text-gray-600">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">10人以上团体价</span>
                                </li>
                                <li class="flex items-center text-sm text-gray-600">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">专属客服服务</span>
                                </li>
                                <li class="flex items-center text-sm text-gray-600">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">灵活支付方式</span>
                                </li>
                            </ul>
                            <button type="button" class="btn btn-primary w-full" onclick="showGroupBooking()">
                                团体预订咨询
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 机票预订表单 -->
        <section class="responsive-py-20 bg-gray-50" id="booking">
            <div class="container">
                <div class="max-w-4xl mx-auto">
                    <div class="text-center mb-12">
                        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                            机票预订申请
                        </h2>
                        <p class="text-lg text-gray-600 leading-relaxed">
                            填写下方表单，我们的专业顾问将为您找到最优惠的机票并协助预订
                        </p>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <form id="flight-booking-form" class="space-y-6">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="firstName" class="form-label">姓名 *</label>
                                        <input type="text" id="firstName" name="firstName" class="form-input" required>
                                    </div>
                                    <div>
                                        <label for="email" class="form-label">邮箱 *</label>
                                        <input type="email" id="email" name="email" class="form-input" required>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="phone" class="form-label">电话号码</label>
                                        <input type="tel" id="phone" name="phone" class="form-input">
                                    </div>
                                    <div>
                                        <label for="preferredAirline" class="form-label">偏好航空公司</label>
                                        <select id="preferredAirline" name="preferredAirline" class="form-select">
                                            <option value="">无偏好</option>
                                            <option value="mas">马来西亚航空</option>
                                            <option value="airasia">亚洲航空</option>
                                            <option value="singapore">新加坡航空</option>
                                            <option value="thai">泰国航空</option>
                                            <option value="cathay">国泰航空</option>
                                            <option value="emirates">阿联酋航空</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="budget" class="form-label">预算范围</label>
                                        <select id="budget" name="budget" class="form-select">
                                            <option value="">请选择预算</option>
                                            <option value="under-500">RM 500以下</option>
                                            <option value="500-1000">RM 500-1000</option>
                                            <option value="1000-2000">RM 1000-2000</option>
                                            <option value="2000-3000">RM 2000-3000</option>
                                            <option value="over-3000">RM 3000以上</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label for="flexibility" class="form-label">时间灵活性</label>
                                        <select id="flexibility" name="flexibility" class="form-select">
                                            <option value="fixed">固定日期</option>
                                            <option value="flexible-3">前后3天内</option>
                                            <option value="flexible-7">前后一周内</option>
                                            <option value="flexible-month">整月灵活</option>
                                        </select>
                                    </div>
                                </div>

                                <div>
                                    <label for="specialRequests" class="form-label">特殊需求</label>
                                    <textarea id="specialRequests" name="specialRequests" rows="4" class="form-textarea"
                                              placeholder="请告诉我们您的特殊需求，如座位偏好、餐食要求、特殊协助等..."></textarea>
                                </div>

                                <div class="text-center">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                                            <polyline points="22,6 12,13 2,6"/>
                                        </svg>
                                        提交预订申请
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- CTA 区域 -->
        <section class="responsive-py-20 bg-gradient-to-r from-sky-600 to-blue-600">
            <div class="container">
                <div class="text-center text-white">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
                        让我们为您找到最优惠的机票
                    </h2>
                    <p class="text-lg md:text-xl mb-8 max-w-3xl mx-auto leading-relaxed opacity-90">
                        专业的机票预订服务，为您的每一次飞行提供最佳选择和优惠价格
                    </p>
                    <div class="button-group">
                        <a href="contact.html" class="btn btn-secondary btn-lg">
                            联系我们
                        </a>
                        <a href="tel:+***********" class="btn btn-outline btn-lg">
                            电话咨询
                        </a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- JavaScript -->
    <script src="../assets/js/navigation.js"></script>
    <script src="../assets/js/main.js"></script>
    <script>
        // 机票代订页面特定功能
        function switchRouteTab(tabName) {
            // 移除所有活动状态
            document.querySelectorAll('.route-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.route-content').forEach(content => {
                content.classList.remove('active');
            });

            // 添加活动状态
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
            document.getElementById(`${tabName}-routes`).classList.add('active');
        }

        function searchRoute(departure, destination) {
            // 滚动到搜索区域并预填信息
            document.getElementById('search').scrollIntoView({
                behavior: 'smooth'
            });

            document.getElementById('departure').value = departure;
            document.getElementById('destination').value = destination;
        }

        function showEarlyBirdFlights() {
            alert('早鸟特价详情：\n\n• 提前60天预订享受最高40%折扣\n• 免费改期一次\n• 包含餐食\n• 适用于精选国际航线\n\n请联系我们的顾问了解更多详情！');
        }

        function showGroupBooking() {
            alert('团体预订详情：\n\n• 10人以上享受团体优惠价格\n• 专属客服服务\n• 灵活支付方式\n• 适合家庭旅行、公司团建\n\n请联系我们的顾问了解更多详情！');
        }

        // 行程类型切换
        document.addEventListener('DOMContentLoaded', function() {
            const tripTypeRadios = document.querySelectorAll('input[name="tripType"]');
            const returnDateField = document.getElementById('returnDateField');

            tripTypeRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    if (this.value === 'oneway' || this.value === 'multicity') {
                        returnDateField.style.display = 'none';
                        document.getElementById('returnDate').required = false;
                    } else {
                        returnDateField.style.display = 'block';
                        document.getElementById('returnDate').required = true;
                    }
                });
            });

            // 设置日期最小值为今天
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('departureDate').min = today;
            document.getElementById('returnDate').min = today;

            // 出发日期变化时更新返程日期最小值
            document.getElementById('departureDate').addEventListener('change', function() {
                const departureDate = new Date(this.value);
                departureDate.setDate(departureDate.getDate() + 1);
                document.getElementById('returnDate').min = departureDate.toISOString().split('T')[0];
            });
        });

        // 机票搜索表单提交
        document.getElementById('flight-search-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const departure = document.getElementById('departure').value;
            const destination = document.getElementById('destination').value;
            const departureDate = document.getElementById('departureDate').value;

            if (!departure || !destination || !departureDate) {
                alert('请填写完整的搜索信息！');
                return;
            }

            alert(`正在搜索航班...\n出发地：${departure}\n目的地：${destination}\n出发日期：${departureDate}\n\n我们将为您找到最优惠的机票选择！`);
        });

        // 机票预订表单提交
        document.getElementById('flight-booking-form').addEventListener('submit', function(e) {
            e.preventDefault();

            alert('感谢您的预订申请！我们的专业顾问将在24小时内与您联系，为您推荐最优惠的机票选择。');

            // 重置表单
            this.reset();
        });
    </script>
</body>
</html>
