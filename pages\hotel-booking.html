<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Sky Mirror World Tour 酒店代订服务 - 经济型、商务型、豪华型酒店预订，特价优惠">
    <meta name="keywords" content="酒店代订,酒店预订,经济型酒店,商务酒店,豪华酒店,特价酒店">
    
    <title>酒店代订服务 - Sky Mirror World Tour</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/components.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <!-- 品牌Logo -->
            <a href="../index.html" class="nav-brand">
                <div class="nav-logo">S</div>
                <span class="nav-title">Sky Mirror World Tour</span>
            </a>

            <!-- 桌面导航菜单 -->
            <div class="nav-menu">
                <a href="../index.html" class="nav-link" data-page="home">首页</a>
                <a href="domestic-transport.html" class="nav-link" data-page="domestic-transport">境内用车</a>
                <a href="signature-tickets.html" class="nav-link" data-page="signature-tickets">特色船票</a>
                <a href="international-tours.html" class="nav-link" data-page="international-tours">国际旅游</a>
                <a href="business-reception.html" class="nav-link" data-page="business-reception">商务接待</a>
                <a href="island-hopping.html" class="nav-link" data-page="island-hopping">海岛旅游</a>
                <a href="hotel-booking.html" class="nav-link active" data-page="hotel-booking">酒店代订</a>
                <a href="flight-booking.html" class="nav-link" data-page="flight-booking">机票代订</a>
                <a href="student-groups.html" class="nav-link" data-page="student-groups">学生团</a>
                <a href="medical-tourism.html" class="nav-link" data-page="medical-tourism">医疗旅游</a>
                <a href="corporate-team-building.html" class="nav-link" data-page="corporate-team-building">公司团建</a>
                <a href="sky-pier-cafe.html" class="nav-link" data-page="sky-pier-cafe">天空号船咖啡厅</a>
                <a href="about.html" class="nav-link" data-page="about">关于我们</a>
                <a href="contact.html" class="nav-cta">联系我们</a>
            </div>

            <!-- 移动端菜单按钮 -->
            <button type="button" class="mobile-menu-btn" id="mobileMenuBtn" aria-label="打开菜单">
                <svg id="menuIcon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="4" y1="6" x2="20" y2="6"/>
                    <line x1="4" y1="12" x2="20" y2="12"/>
                    <line x1="4" y1="18" x2="20" y2="18"/>
                </svg>
                <svg id="closeIcon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="hidden">
                    <path d="M18 6 6 18"/>
                    <path d="m6 6 12 12"/>
                </svg>
            </button>
        </div>

        <!-- 移动端菜单 -->
        <div class="mobile-menu" id="mobileMenu">
            <a href="../index.html" class="mobile-nav-link" data-page="home">首页</a>
            <a href="domestic-transport.html" class="mobile-nav-link" data-page="domestic-transport">境内用车</a>
            <a href="signature-tickets.html" class="mobile-nav-link" data-page="signature-tickets">特色船票</a>
            <a href="international-tours.html" class="mobile-nav-link" data-page="international-tours">国际旅游</a>
            <a href="business-reception.html" class="mobile-nav-link" data-page="business-reception">商务接待</a>
            <a href="island-hopping.html" class="mobile-nav-link" data-page="island-hopping">海岛旅游</a>
            <a href="hotel-booking.html" class="mobile-nav-link active" data-page="hotel-booking">酒店代订</a>
            <a href="flight-booking.html" class="mobile-nav-link" data-page="flight-booking">机票代订</a>
            <a href="student-groups.html" class="mobile-nav-link" data-page="student-groups">学生团</a>
            <a href="medical-tourism.html" class="mobile-nav-link" data-page="medical-tourism">医疗旅游</a>
            <a href="corporate-team-building.html" class="mobile-nav-link" data-page="corporate-team-building">公司团建</a>
            <a href="sky-pier-cafe.html" class="mobile-nav-link" data-page="sky-pier-cafe">天空号船咖啡厅</a>
            <a href="about.html" class="mobile-nav-link" data-page="about">关于我们</a>
            <a href="contact.html" class="mobile-nav-link nav-cta-mobile">联系我们</a>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main>
        <!-- Hero 区域 -->
        <section class="hero-section">
            <div class="hero-background" style="background-image: url('https://images.pexels.com/photos/271624/pexels-photo-271624.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop')"></div>
            <div class="hero-overlay"></div>
            <div class="hero-content">
                <h1 class="text-4xl md:text-5xl lg:text-7xl font-bold mb-6 leading-tight">
                    酒店代订服务
                </h1>
                <p class="text-lg md:text-xl mb-8 max-w-3xl mx-auto leading-relaxed opacity-90">
                    为您精选全球优质酒店，从经济实惠到奢华享受，满足您不同的住宿需求
                </p>
                <div class="button-group">
                    <a href="#search" class="btn btn-primary btn-lg">
                        搜索酒店
                    </a>
                    <a href="#hotels" class="btn btn-outline btn-lg">
                        热门推荐
                    </a>
                </div>
            </div>
        </section>

        <!-- 酒店搜索区域 -->
        <section class="responsive-py-20 bg-gray-50" id="search">
            <div class="container">
                <div class="max-w-6xl mx-auto">
                    <div class="text-center mb-12">
                        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                            搜索理想酒店
                        </h2>
                        <p class="text-lg text-gray-600 leading-relaxed">
                            输入您的旅行信息，我们为您找到最适合的住宿选择
                        </p>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <form id="hotel-search-form" class="space-y-6">
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                                    <div>
                                        <label for="destination" class="form-label">目的地 *</label>
                                        <input type="text" id="destination" name="destination" class="form-input" 
                                               placeholder="城市或酒店名称" required>
                                    </div>
                                    <div>
                                        <label for="checkin" class="form-label">入住日期 *</label>
                                        <input type="date" id="checkin" name="checkin" class="form-input" required>
                                    </div>
                                    <div>
                                        <label for="checkout" class="form-label">退房日期 *</label>
                                        <input type="date" id="checkout" name="checkout" class="form-input" required>
                                    </div>
                                    <div>
                                        <label for="rooms" class="form-label">房间数量</label>
                                        <select id="rooms" name="rooms" class="form-select">
                                            <option value="1">1间房</option>
                                            <option value="2">2间房</option>
                                            <option value="3">3间房</option>
                                            <option value="4">4间房</option>
                                            <option value="5+">5间房以上</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    <div>
                                        <label for="adults" class="form-label">成人数量</label>
                                        <select id="adults" name="adults" class="form-select">
                                            <option value="1">1位成人</option>
                                            <option value="2">2位成人</option>
                                            <option value="3">3位成人</option>
                                            <option value="4">4位成人</option>
                                            <option value="5+">5位成人以上</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label for="children" class="form-label">儿童数量</label>
                                        <select id="children" name="children" class="form-select">
                                            <option value="0">无儿童</option>
                                            <option value="1">1位儿童</option>
                                            <option value="2">2位儿童</option>
                                            <option value="3">3位儿童</option>
                                            <option value="4+">4位儿童以上</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label for="hotelType" class="form-label">酒店类型</label>
                                        <select id="hotelType" name="hotelType" class="form-select">
                                            <option value="">所有类型</option>
                                            <option value="economy">经济型</option>
                                            <option value="business">商务型</option>
                                            <option value="luxury">豪华型</option>
                                            <option value="resort">度假村</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="text-center">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <circle cx="11" cy="11" r="8"/>
                                            <path d="M21 21l-4.35-4.35"/>
                                        </svg>
                                        搜索酒店
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 酒店分类展示 -->
        <section class="responsive-py-20" id="hotels">
            <div class="container">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                        酒店分类选择
                    </h2>
                    <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                        根据您的预算和需求，选择最适合的酒店类型
                    </p>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- 经济型酒店 -->
                    <div class="card">
                        <div class="aspect-video bg-gray-200 rounded-t-xl overflow-hidden">
                            <img src="https://images.pexels.com/photos/271624/pexels-photo-271624.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop" 
                                 alt="经济型酒店" class="w-full h-full object-cover">
                        </div>
                        <div class="card-body">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-2xl font-bold text-gray-900">经济型酒店</h3>
                                <div class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                                    实惠
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4 leading-relaxed">
                                干净舒适的住宿环境，基础设施齐全，性价比高，适合预算有限的旅行者。
                            </p>
                            <div class="space-y-2 mb-4">
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    免费WiFi
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    24小时前台
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    空调设施
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="text-2xl font-bold text-blue-600">
                                    RM 80 - 150
                                    <span class="text-sm font-normal text-gray-500">/晚</span>
                                </div>
                                <button class="btn btn-primary btn-sm" onclick="filterHotels('economy')">
                                    查看酒店
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 商务型酒店 -->
                    <div class="card border-2 border-blue-500">
                        <div class="aspect-video bg-gray-200 rounded-t-xl overflow-hidden">
                            <img src="https://images.pexels.com/photos/164595/pexels-photo-164595.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop" 
                                 alt="商务型酒店" class="w-full h-full object-cover">
                        </div>
                        <div class="card-body">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-2xl font-bold text-gray-900">商务型酒店</h3>
                                <div class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                                    推荐
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4 leading-relaxed">
                                专为商务人士设计，提供完善的商务设施和服务，地理位置优越。
                            </p>
                            <div class="space-y-2 mb-4">
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    商务中心
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    会议室
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    健身房
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    机场接送
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="text-2xl font-bold text-blue-600">
                                    RM 200 - 400
                                    <span class="text-sm font-normal text-gray-500">/晚</span>
                                </div>
                                <button class="btn btn-primary btn-sm" onclick="filterHotels('business')">
                                    查看酒店
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 豪华型酒店 -->
                    <div class="card">
                        <div class="aspect-video bg-gray-200 rounded-t-xl overflow-hidden">
                            <img src="https://images.pexels.com/photos/338504/pexels-photo-338504.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop" 
                                 alt="豪华型酒店" class="w-full h-full object-cover">
                        </div>
                        <div class="card-body">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-2xl font-bold text-gray-900">豪华型酒店</h3>
                                <div class="bg-gold-100 text-gold-800 px-3 py-1 rounded-full text-sm font-medium">
                                    奢华
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4 leading-relaxed">
                                五星级奢华体验，顶级设施和个性化服务，为您带来难忘的住宿体验。
                            </p>
                            <div class="space-y-2 mb-4">
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    SPA中心
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    高级餐厅
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    私人管家
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    豪华套房
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="text-2xl font-bold text-blue-600">
                                    RM 500+
                                    <span class="text-sm font-normal text-gray-500">/晚</span>
                                </div>
                                <button class="btn btn-primary btn-sm" onclick="filterHotels('luxury')">
                                    查看酒店
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 特价优惠 -->
        <section class="responsive-py-20">
            <div class="container">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                        特价优惠
                    </h2>
                    <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                        限时特价酒店优惠，为您的旅行节省更多预算
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <!-- 早鸟优惠 -->
                    <div class="card border-2 border-orange-500">
                        <div class="card-body">
                            <div class="flex items-center mb-4">
                                <div class="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mr-4">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-orange-600">
                                        <circle cx="12" cy="12" r="10"/>
                                        <polyline points="12,6 12,12 16,14"/>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-xl font-bold text-gray-900">早鸟优惠</h3>
                                    <p class="text-orange-600 font-semibold">最高可省30%</p>
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4 leading-relaxed">
                                提前30天预订酒店，享受早鸟特价优惠。适用于全球精选酒店，数量有限，先到先得。
                            </p>
                            <ul class="space-y-2 mb-4">
                                <li class="flex items-center text-sm text-gray-600">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">提前30天预订</span>
                                </li>
                                <li class="flex items-center text-sm text-gray-600">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">免费取消政策</span>
                                </li>
                                <li class="flex items-center text-sm text-gray-600">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">包含早餐</span>
                                </li>
                            </ul>
                            <button class="btn btn-primary w-full" onclick="showEarlyBirdDeals()">
                                查看早鸟优惠
                            </button>
                        </div>
                    </div>

                    <!-- 最后一分钟优惠 -->
                    <div class="card border-2 border-red-500">
                        <div class="card-body">
                            <div class="flex items-center mb-4">
                                <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="text-red-600">
                                        <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"/>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-xl font-bold text-gray-900">最后一分钟优惠</h3>
                                    <p class="text-red-600 font-semibold">最高可省50%</p>
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4 leading-relaxed">
                                当天或次日入住的酒店特价优惠，价格超值，适合灵活安排行程的旅客。
                            </p>
                            <ul class="space-y-2 mb-4">
                                <li class="flex items-center text-sm text-gray-600">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">当天/次日入住</span>
                                </li>
                                <li class="flex items-center text-sm text-gray-600">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">即时确认</span>
                                </li>
                                <li class="flex items-center text-sm text-gray-600">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">超值价格</span>
                                </li>
                            </ul>
                            <button class="btn btn-primary w-full" onclick="showLastMinuteDeals()">
                                查看限时优惠
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 预订表单 -->
        <section class="responsive-py-20 bg-gray-50" id="booking">
            <div class="container">
                <div class="max-w-4xl mx-auto">
                    <div class="text-center mb-12">
                        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                            酒店预订申请
                        </h2>
                        <p class="text-lg text-gray-600 leading-relaxed">
                            填写下方表单，我们的专业顾问将为您找到最适合的酒店并协助预订
                        </p>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <form id="hotel-booking-form" class="space-y-6">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="firstName" class="form-label">姓名 *</label>
                                        <input type="text" id="firstName" name="firstName" class="form-input" required>
                                    </div>
                                    <div>
                                        <label for="email" class="form-label">邮箱 *</label>
                                        <input type="email" id="email" name="email" class="form-input" required>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="phone" class="form-label">电话号码</label>
                                        <input type="tel" id="phone" name="phone" class="form-input">
                                    </div>
                                    <div>
                                        <label for="preferredHotel" class="form-label">偏好酒店类型</label>
                                        <select id="preferredHotel" name="preferredHotel" class="form-select">
                                            <option value="">请选择类型</option>
                                            <option value="economy">经济型</option>
                                            <option value="business">商务型</option>
                                            <option value="luxury">豪华型</option>
                                            <option value="resort">度假村</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="budget" class="form-label">预算范围（每晚）</label>
                                        <select id="budget" name="budget" class="form-select">
                                            <option value="">请选择预算</option>
                                            <option value="under-100">RM 100以下</option>
                                            <option value="100-200">RM 100-200</option>
                                            <option value="200-400">RM 200-400</option>
                                            <option value="400-600">RM 400-600</option>
                                            <option value="over-600">RM 600以上</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label for="nights" class="form-label">住宿天数</label>
                                        <select id="nights" name="nights" class="form-select">
                                            <option value="">请选择天数</option>
                                            <option value="1">1晚</option>
                                            <option value="2">2晚</option>
                                            <option value="3">3晚</option>
                                            <option value="4-6">4-6晚</option>
                                            <option value="7+">7晚以上</option>
                                        </select>
                                    </div>
                                </div>

                                <div>
                                    <label for="specialRequests" class="form-label">特殊需求</label>
                                    <textarea id="specialRequests" name="specialRequests" rows="4" class="form-textarea"
                                              placeholder="请告诉我们您的特殊需求，如房间偏好、设施要求、饮食限制等..."></textarea>
                                </div>

                                <div class="text-center">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                                            <polyline points="22,6 12,13 2,6"/>
                                        </svg>
                                        提交预订申请
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- CTA 区域 -->
        <section class="responsive-py-20 bg-gradient-to-r from-blue-600 to-purple-600">
            <div class="container">
                <div class="text-center text-white">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
                        让我们为您找到完美的住宿
                    </h2>
                    <p class="text-lg md:text-xl mb-8 max-w-3xl mx-auto leading-relaxed opacity-90">
                        专业的酒店预订服务，为您的每一次旅行提供舒适的住宿体验
                    </p>
                    <div class="button-group">
                        <a href="contact.html" class="btn btn-secondary btn-lg">
                            联系我们
                        </a>
                        <a href="tel:+60123456789" class="btn btn-outline btn-lg">
                            电话咨询
                        </a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- JavaScript -->
    <script src="../assets/js/navigation.js"></script>
    <script src="../assets/js/main.js"></script>
    <script>
        // 酒店代订页面特定功能
        function filterHotels(type) {
            // 滚动到搜索区域并预填酒店类型
            document.getElementById('search').scrollIntoView({
                behavior: 'smooth'
            });

            const hotelTypeSelect = document.getElementById('hotelType');
            hotelTypeSelect.value = type;
        }

        function bookHotel(hotelId) {
            // 滚动到预订表单
            document.getElementById('booking').scrollIntoView({
                behavior: 'smooth'
            });

            // 根据酒店ID预填相关信息
            const destinationInput = document.getElementById('destination');

            switch(hotelId) {
                case 'kl-center':
                    destinationInput.value = '吉隆坡市中心';
                    break;
                case 'singapore-marina':
                    destinationInput.value = '新加坡滨海湾';
                    break;
                case 'bangkok-sukhumvit':
                    destinationInput.value = '曼谷素坤逸';
                    break;
                case 'tokyo-shinjuku':
                    destinationInput.value = '东京新宿';
                    break;
                case 'seoul-myeongdong':
                    destinationInput.value = '首尔明洞';
                    break;
                case 'bali-beach':
                    destinationInput.value = '巴厘岛海滨';
                    break;
            }
        }

        function showEarlyBirdDeals() {
            alert('早鸟优惠详情：\n\n• 提前30天预订享受最高30%折扣\n• 免费取消政策\n• 包含早餐\n• 适用于全球精选酒店\n\n请联系我们的顾问了解更多详情！');
        }

        function showLastMinuteDeals() {
            alert('最后一分钟优惠详情：\n\n• 当天或次日入住享受最高50%折扣\n• 即时确认\n• 超值价格\n• 数量有限，先到先得\n\n请联系我们的顾问了解更多详情！');
        }

        // 设置日期最小值为今天
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('checkin').min = today;
            document.getElementById('checkout').min = today;

            // 入住日期变化时更新退房日期最小值
            document.getElementById('checkin').addEventListener('change', function() {
                const checkinDate = new Date(this.value);
                checkinDate.setDate(checkinDate.getDate() + 1);
                document.getElementById('checkout').min = checkinDate.toISOString().split('T')[0];
            });
        });

        // 酒店搜索表单提交
        document.getElementById('hotel-search-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const destination = document.getElementById('destination').value;
            const checkin = document.getElementById('checkin').value;
            const checkout = document.getElementById('checkout').value;

            if (!destination || !checkin || !checkout) {
                alert('请填写完整的搜索信息！');
                return;
            }

            alert(`正在搜索 ${destination} 的酒店...\n入住：${checkin}\n退房：${checkout}\n\n我们将为您找到最适合的酒店选择！`);
        });

        // 酒店预订表单提交
        document.getElementById('hotel-booking-form').addEventListener('submit', function(e) {
            e.preventDefault();

            alert('感谢您的预订申请！我们的专业顾问将在24小时内与您联系，为您推荐最适合的酒店选择。');

            // 重置表单
            this.reset();
        });
    </script>
</body>
</html>
