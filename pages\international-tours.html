<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Sky Mirror World Tour 国际旅游服务 - 新加坡、泰国、印尼等东南亚国家旅游套餐">
    <meta name="keywords" content="国际旅游,东南亚旅游,新加坡旅游,泰国旅游,印尼旅游,旅游套餐">
    
    <title>国际旅游服务 - Sky Mirror World Tour</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/components.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <!-- 品牌Logo -->
            <a href="../index.html" class="nav-brand">
                <div class="nav-logo">S</div>
                <span class="nav-title">Sky Mirror World Tour</span>
            </a>

            <!-- 桌面导航菜单 -->
            <div class="nav-menu">
                <a href="../index.html" class="nav-link" data-page="home">首页</a>
                <a href="domestic-transport.html" class="nav-link" data-page="domestic-transport">境内用车</a>
                <a href="signature-tickets.html" class="nav-link" data-page="signature-tickets">特色船票</a>
                <a href="international-tours.html" class="nav-link active" data-page="international-tours">国际旅游</a>
                <a href="business-reception.html" class="nav-link" data-page="business-reception">商务接待</a>
                <a href="island-hopping.html" class="nav-link" data-page="island-hopping">海岛旅游</a>
                <a href="sky-pier-cafe.html" class="nav-link" data-page="sky-pier-cafe">天空号船咖啡厅</a>
                <a href="about.html" class="nav-link" data-page="about">关于我们</a>
                <a href="contact.html" class="nav-cta">联系我们</a>
            </div>

            <!-- 移动端菜单按钮 -->
            <button type="button" class="mobile-menu-btn" id="mobileMenuBtn" aria-label="打开菜单">
                <svg id="menuIcon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="4" y1="6" x2="20" y2="6"/>
                    <line x1="4" y1="12" x2="20" y2="12"/>
                    <line x1="4" y1="18" x2="20" y2="18"/>
                </svg>
                <svg id="closeIcon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="hidden">
                    <path d="M18 6 6 18"/>
                    <path d="m6 6 12 12"/>
                </svg>
            </button>
        </div>

        <!-- 移动端菜单 -->
        <div class="mobile-menu" id="mobileMenu">
            <a href="../index.html" class="mobile-nav-link" data-page="home">首页</a>
            <a href="domestic-transport.html" class="mobile-nav-link" data-page="domestic-transport">境内用车</a>
            <a href="signature-tickets.html" class="mobile-nav-link" data-page="signature-tickets">特色船票</a>
            <a href="international-tours.html" class="mobile-nav-link active" data-page="international-tours">国际旅游</a>
            <a href="business-reception.html" class="mobile-nav-link" data-page="business-reception">商务接待</a>
            <a href="island-hopping.html" class="mobile-nav-link" data-page="island-hopping">海岛旅游</a>
            <a href="sky-pier-cafe.html" class="mobile-nav-link" data-page="sky-pier-cafe">天空号船咖啡厅</a>
            <a href="about.html" class="mobile-nav-link" data-page="about">关于我们</a>
            <a href="contact.html" class="mobile-nav-link nav-cta-mobile">联系我们</a>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main>
        <!-- Hero 区域 -->
        <section class="hero-section">
            <div class="hero-background" style="background-image: url('https://images.pexels.com/photos/1271619/pexels-photo-1271619.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop')"></div>
            <div class="hero-overlay"></div>
            <div class="hero-content">
                <h1 class="text-4xl md:text-5xl lg:text-7xl font-bold mb-6 leading-tight">
                    国际旅游服务
                </h1>
                <p class="text-lg md:text-xl mb-8 max-w-3xl mx-auto leading-relaxed opacity-90">
                    探索东南亚的魅力，体验不同国家的文化与风情，我们为您精心安排每一段国际旅程
                </p>
                <div class="button-group">
                    <a href="#destinations" class="btn btn-primary btn-lg">
                        热门目的地
                    </a>
                    <a href="#packages" class="btn btn-outline btn-lg">
                        查看套餐
                    </a>
                </div>
            </div>
        </section>

        <!-- 热门目的地区域 -->
        <section class="responsive-py-20" id="destinations">
            <div class="container">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                        热门目的地
                    </h2>
                    <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                        精选东南亚最受欢迎的旅游目的地，每个地方都有独特的魅力等待您的探索
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- 新加坡 -->
                    <div class="card" id="singapore-tours">
                        <div class="aspect-video bg-gray-200 rounded-t-xl overflow-hidden">
                            <img src="https://images.pexels.com/photos/1366919/pexels-photo-1366919.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop" 
                                 alt="新加坡" class="w-full h-full object-cover">
                        </div>
                        <div class="card-body">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-xl font-bold text-gray-900">新加坡</h3>
                                <div class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                                    热门
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4 text-sm leading-relaxed">
                                花园城市新加坡，现代化与传统文化完美融合。滨海湾、圣淘沙、牛车水等经典景点一网打尽。
                            </p>
                            <div class="space-y-2 mb-4">
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <circle cx="12" cy="12" r="10"/>
                                        <polyline points="12,6 12,12 16,14"/>
                                    </svg>
                                    3天2夜 / 4天3夜
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                                        <circle cx="12" cy="10" r="3"/>
                                    </svg>
                                    滨海湾、圣淘沙、环球影城
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="text-2xl font-bold text-blue-600">
                                    RM 899 起
                                </div>
                                <button class="btn btn-primary btn-sm" onclick="openBookingModal('singapore')">
                                    立即预订
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 泰国 -->
                    <div class="card" id="thailand-tours">
                        <div class="aspect-video bg-gray-200 rounded-t-xl overflow-hidden">
                            <img src="https://images.pexels.com/photos/302899/pexels-photo-302899.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop" 
                                 alt="泰国" class="w-full h-full object-cover">
                        </div>
                        <div class="card-body">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-xl font-bold text-gray-900">泰国</h3>
                                <div class="bg-orange-100 text-orange-800 px-2 py-1 rounded-full text-xs font-medium">
                                    推荐
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4 text-sm leading-relaxed">
                                微笑之国泰国，丰富的文化遗产和美丽的海滩。曼谷、清迈、普吉岛，感受不同的泰式风情。
                            </p>
                            <div class="space-y-2 mb-4">
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <circle cx="12" cy="12" r="10"/>
                                        <polyline points="12,6 12,12 16,14"/>
                                    </svg>
                                    5天4夜 / 7天6夜
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                                        <circle cx="12" cy="10" r="3"/>
                                    </svg>
                                    曼谷、清迈、普吉岛
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="text-2xl font-bold text-blue-600">
                                    RM 1,299 起
                                </div>
                                <button class="btn btn-primary btn-sm" onclick="openBookingModal('thailand')">
                                    立即预订
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 印尼 -->
                    <div class="card" id="indonesia-tours">
                        <div class="aspect-video bg-gray-200 rounded-t-xl overflow-hidden">
                            <img src="https://images.pexels.com/photos/1271619/pexels-photo-1271619.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop" 
                                 alt="印尼巴厘岛" class="w-full h-full object-cover">
                        </div>
                        <div class="card-body">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-xl font-bold text-gray-900">印尼巴厘岛</h3>
                                <div class="bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-xs font-medium">
                                    度假
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4 text-sm leading-relaxed">
                                神明之岛巴厘岛，热带天堂的完美诠释。乌布梯田、库塔海滩、圣泉寺，体验印尼独特文化。
                            </p>
                            <div class="space-y-2 mb-4">
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <circle cx="12" cy="12" r="10"/>
                                        <polyline points="12,6 12,12 16,14"/>
                                    </svg>
                                    4天3夜 / 6天5夜
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                                        <circle cx="12" cy="10" r="3"/>
                                    </svg>
                                    乌布、库塔、圣泉寺
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="text-2xl font-bold text-blue-600">
                                    RM 1,599 起
                                </div>
                                <button class="btn btn-primary btn-sm" onclick="openBookingModal('indonesia')">
                                    立即预订
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 日本 -->
                    <div class="card" id="japan-tours">
                        <div class="aspect-video bg-gray-200 rounded-t-xl overflow-hidden">
                            <img src="https://images.pexels.com/photos/2506923/pexels-photo-2506923.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop"
                                 alt="日本樱花" class="w-full h-full object-cover">
                        </div>
                        <div class="card-body">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-xl font-bold text-gray-900">日本</h3>
                                <div class="bg-pink-100 text-pink-800 px-2 py-1 rounded-full text-xs font-medium">
                                    樱花季
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4 text-sm leading-relaxed">
                                樱花之国日本，传统与现代的完美融合。东京、大阪、京都，体验独特的日式文化和美食。
                            </p>
                            <div class="space-y-2 mb-4">
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <circle cx="12" cy="12" r="10"/>
                                        <polyline points="12,6 12,12 16,14"/>
                                    </svg>
                                    5天4夜 / 7天6夜
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                                        <circle cx="12" cy="10" r="3"/>
                                    </svg>
                                    东京、大阪、富士山、京都
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="text-2xl font-bold text-blue-600">
                                    RM 2,899 起
                                </div>
                                <button class="btn btn-primary btn-sm" onclick="openBookingModal('japan')">
                                    立即预订
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 韩国 -->
                    <div class="card" id="korea-tours">
                        <div class="aspect-video bg-gray-200 rounded-t-xl overflow-hidden">
                            <img src="https://images.pexels.com/photos/2070033/pexels-photo-2070033.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop"
                                 alt="韩国首尔" class="w-full h-full object-cover">
                        </div>
                        <div class="card-body">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-xl font-bold text-gray-900">韩国</h3>
                                <div class="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-medium">
                                    韩流
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4 text-sm leading-relaxed">
                                韩流文化发源地，首尔、济州岛，体验K-pop文化、韩式美食和购物天堂。
                            </p>
                            <div class="space-y-2 mb-4">
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <circle cx="12" cy="12" r="10"/>
                                        <polyline points="12,6 12,12 16,14"/>
                                    </svg>
                                    4天3夜 / 5天4夜
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                                        <circle cx="12" cy="10" r="3"/>
                                    </svg>
                                    首尔、明洞、济州岛、N首尔塔
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="text-2xl font-bold text-blue-600">
                                    RM 1,899 起
                                </div>
                                <button class="btn btn-primary btn-sm" onclick="openBookingModal('korea')">
                                    立即预订
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 越南 -->
                    <div class="card" id="vietnam-tours">
                        <div class="aspect-video bg-gray-200 rounded-t-xl overflow-hidden">
                            <img src="https://images.pexels.com/photos/1371360/pexels-photo-1371360.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop"
                                 alt="越南胡志明市" class="w-full h-full object-cover">
                        </div>
                        <div class="card-body">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-xl font-bold text-gray-900">越南</h3>
                                <div class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium">
                                    美食
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4 text-sm leading-relaxed">
                                东南亚明珠越南，胡志明市、河内、下龙湾，体验法式殖民风情和越南美食文化。
                            </p>
                            <div class="space-y-2 mb-4">
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <circle cx="12" cy="12" r="10"/>
                                        <polyline points="12,6 12,12 16,14"/>
                                    </svg>
                                    6天5夜 / 8天7夜
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                                        <circle cx="12" cy="10" r="3"/>
                                    </svg>
                                    胡志明市、河内、下龙湾、美奈
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="text-2xl font-bold text-blue-600">
                                    RM 2,599 起
                                </div>
                                <button class="btn btn-primary btn-sm" onclick="openBookingModal('vietnam')">
                                    立即预订
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 旅游套餐区域 -->
        <section class="responsive-py-20 bg-gray-50" id="packages">
            <div class="container">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                        精选旅游套餐
                    </h2>
                    <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                        我们为您精心设计的旅游套餐，包含机票、酒店、交通、导游等全方位服务
                    </p>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- 经济套餐 -->
                    <div class="card">
                        <div class="card-header">
                            <div class="flex items-center justify-between">
                                <h3 class="text-2xl font-bold text-gray-900">经济套餐</h3>
                                <div class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                                    超值
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="text-3xl font-bold text-blue-600 mb-4">
                                RM 899 - 1,299
                                <span class="text-base font-normal text-gray-500">/人</span>
                            </div>
                            <ul class="space-y-3 mb-6">
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">往返机票（经济舱）</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">3-4星级酒店住宿</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">机场接送服务</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">中文导游服务</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">主要景点门票</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">旅游保险</span>
                                </li>
                            </ul>
                            <button class="btn btn-primary w-full" onclick="openBookingModal('economy')">
                                选择经济套餐
                            </button>
                        </div>
                    </div>

                    <!-- 豪华套餐 -->
                    <div class="card border-2 border-blue-500">
                        <div class="card-header">
                            <div class="flex items-center justify-between">
                                <h3 class="text-2xl font-bold text-gray-900">豪华套餐</h3>
                                <div class="bg-gold-100 text-gold-800 px-3 py-1 rounded-full text-sm font-medium">
                                    推荐
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="text-3xl font-bold text-blue-600 mb-4">
                                RM 1,899 - 2,999
                                <span class="text-base font-normal text-gray-500">/人</span>
                            </div>
                            <ul class="space-y-3 mb-6">
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">往返机票（商务舱可选）</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">5星级豪华酒店住宿</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">专车接送服务</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">专业中文导游</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">所有景点门票</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">特色餐饮体验</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">SPA 体验</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">全程旅游保险</span>
                                </li>
                            </ul>
                            <button class="btn btn-primary w-full" onclick="openBookingModal('luxury')">
                                选择豪华套餐
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 服务特色区域 -->
        <section class="responsive-py-20">
            <div class="container">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                        为什么选择我们
                    </h2>
                    <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                        专业的国际旅游服务，让您的每一次出行都成为美好的回忆
                    </p>
                </div>

                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10"/>
                                <path d="M9 12l2 2 4-4"/>
                            </svg>
                        </div>
                        <h3 class="feature-title">签证协助</h3>
                        <p class="feature-description">
                            专业的签证申请服务，协助您办理各国签证，让出行更加便捷
                        </p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
                                <circle cx="9" cy="7" r="4"/>
                                <path d="M22 21v-2a4 4 0 0 0-3-3.87"/>
                                <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                            </svg>
                        </div>
                        <h3 class="feature-title">专业导游</h3>
                        <p class="feature-description">
                            当地中文导游服务，深度了解当地文化，为您提供最地道的旅游体验
                        </p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                <circle cx="12" cy="12" r="10"/>
                                <polyline points="12,6 12,12 16,14"/>
                            </svg>
                        </div>
                        <h3 class="feature-title">24小时支持</h3>
                        <p class="feature-description">
                            全程24小时客服支持，无论您在哪里遇到问题，我们都会及时协助
                        </p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                <line x1="12" y1="1" x2="12" y2="23"/>
                                <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/>
                            </svg>
                        </div>
                        <h3 class="feature-title">价格透明</h3>
                        <p class="feature-description">
                            明码标价，无隐藏费用，让您清楚了解每一项服务的费用
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 预订表单区域 -->
        <section class="responsive-py-20 bg-gray-50" id="booking">
            <div class="container">
                <div class="max-w-4xl mx-auto">
                    <div class="text-center mb-12">
                        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                            立即预订您的国际之旅
                        </h2>
                        <p class="text-lg text-gray-600 leading-relaxed">
                            填写下方表单，我们的专业顾问将在24小时内与您联系，为您定制专属的旅游方案
                        </p>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <form id="international-booking-form" class="space-y-6">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="firstName" class="form-label">姓名 *</label>
                                        <input type="text" id="firstName" name="firstName" class="form-input" required>
                                    </div>
                                    <div>
                                        <label for="email" class="form-label">邮箱 *</label>
                                        <input type="email" id="email" name="email" class="form-input" required>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="phone" class="form-label">电话号码</label>
                                        <input type="tel" id="phone" name="phone" class="form-input">
                                    </div>
                                    <div>
                                        <label for="travelers" class="form-label">出行人数 *</label>
                                        <select id="travelers" name="travelers" class="form-select" required>
                                            <option value="">请选择人数</option>
                                            <option value="1">1人</option>
                                            <option value="2">2人</option>
                                            <option value="3">3人</option>
                                            <option value="4">4人</option>
                                            <option value="5">5人</option>
                                            <option value="6+">6人以上</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="destination" class="form-label">目的地 *</label>
                                        <select id="destination" name="destination" class="form-select" required>
                                            <option value="">请选择目的地</option>
                                            <option value="singapore">新加坡</option>
                                            <option value="thailand">泰国</option>
                                            <option value="indonesia">印尼巴厘岛</option>
                                            <option value="japan">日本</option>
                                            <option value="korea">韩国</option>
                                            <option value="vietnam">越南</option>
                                            <option value="cambodia">柬埔寨</option>
                                            <option value="other">其他（请在备注中说明）</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label for="packageType" class="form-label">套餐类型 *</label>
                                        <select id="packageType" name="packageType" class="form-select" required>
                                            <option value="">请选择套餐</option>
                                            <option value="economy">经济套餐</option>
                                            <option value="luxury">豪华套餐</option>
                                            <option value="custom">定制套餐</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="departureDate" class="form-label">出发日期 *</label>
                                        <input type="date" id="departureDate" name="departureDate" class="form-input" required>
                                    </div>
                                    <div>
                                        <label for="duration" class="form-label">旅行天数</label>
                                        <select id="duration" name="duration" class="form-select">
                                            <option value="">请选择天数</option>
                                            <option value="3">3天2夜</option>
                                            <option value="4">4天3夜</option>
                                            <option value="5">5天4夜</option>
                                            <option value="6">6天5夜</option>
                                            <option value="7">7天6夜</option>
                                            <option value="custom">其他（请在备注中说明）</option>
                                        </select>
                                    </div>
                                </div>

                                <div>
                                    <label for="specialRequests" class="form-label">特殊需求或备注</label>
                                    <textarea id="specialRequests" name="specialRequests" rows="4" class="form-textarea"
                                              placeholder="请告诉我们您的特殊需求，如饮食偏好、住宿要求、特殊活动等..."></textarea>
                                </div>

                                <div class="text-center">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                                            <polyline points="22,6 12,13 2,6"/>
                                        </svg>
                                        提交预订申请
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- CTA 区域 -->
        <section class="responsive-py-20 bg-gradient-to-r from-purple-600 to-blue-600">
            <div class="container">
                <div class="text-center text-white">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
                        开启您的国际旅程
                    </h2>
                    <p class="text-lg md:text-xl mb-8 max-w-3xl mx-auto leading-relaxed opacity-90">
                        专业的服务团队，丰富的旅游经验，为您打造难忘的国际旅游体验
                    </p>
                    <div class="button-group">
                        <a href="contact.html" class="btn btn-secondary btn-lg">
                            联系顾问
                        </a>
                        <a href="tel:+60123456789" class="btn btn-outline btn-lg">
                            电话咨询
                        </a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- JavaScript -->
    <script src="../assets/js/navigation.js"></script>
    <script src="../assets/js/main.js"></script>
    <script>
        // 国际旅游页面特定功能
        function openBookingModal(type) {
            // 滚动到预订表单
            document.getElementById('booking').scrollIntoView({
                behavior: 'smooth'
            });

            // 根据类型预填表单
            const packageSelect = document.getElementById('packageType');
            const destinationSelect = document.getElementById('destination');

            if (type === 'singapore') {
                destinationSelect.value = 'singapore';
            } else if (type === 'thailand') {
                destinationSelect.value = 'thailand';
            } else if (type === 'indonesia') {
                destinationSelect.value = 'indonesia';
            } else if (type === 'economy') {
                packageSelect.value = 'economy';
            } else if (type === 'luxury') {
                packageSelect.value = 'luxury';
            }
        }

        // 设置出发日期最小值为今天
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('departureDate').min = today;
        });

        // 表单提交处理
        document.getElementById('international-booking-form').addEventListener('submit', function(e) {
            e.preventDefault();

            // 这里可以添加表单验证和提交逻辑
            alert('感谢您的预订申请！我们的专业顾问将在24小时内与您联系。');

            // 重置表单
            this.reset();
        });
    </script>
</body>
</html>
