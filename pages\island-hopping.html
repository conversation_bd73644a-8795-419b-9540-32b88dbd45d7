<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Sky Mirror World Tour 海岛旅游服务 - 兰卡威、刁曼岛、热浪岛等马来西亚热门海岛旅游套餐">
    <meta name="keywords" content="海岛旅游,兰卡威,刁曼岛,热浪岛,海岛跳跃,马来西亚海岛">
    
    <title>海岛旅游服务 - Sky Mirror World Tour</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/components.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <!-- 品牌Logo -->
            <a href="../index.html" class="nav-brand">
                <div class="nav-logo">S</div>
                <span class="nav-title">Sky Mirror World Tour</span>
            </a>

            <!-- 桌面导航菜单 -->
            <div class="nav-menu">
                <a href="../index.html" class="nav-link" data-page="home">首页</a>
                <a href="domestic-transport.html" class="nav-link" data-page="domestic-transport">境内用车</a>
                <a href="signature-tickets.html" class="nav-link" data-page="signature-tickets">特色船票</a>
                <a href="international-tours.html" class="nav-link" data-page="international-tours">国际旅游</a>
                <a href="business-reception.html" class="nav-link" data-page="business-reception">商务接待</a>
                <a href="island-hopping.html" class="nav-link active" data-page="island-hopping">海岛旅游</a>
                <a href="sky-pier-cafe.html" class="nav-link" data-page="sky-pier-cafe">天空号船咖啡厅</a>
                <a href="about.html" class="nav-link" data-page="about">关于我们</a>
                <a href="contact.html" class="nav-cta">联系我们</a>
            </div>

            <!-- 移动端菜单按钮 -->
            <button type="button" class="mobile-menu-btn" id="mobileMenuBtn" aria-label="打开菜单">
                <svg id="menuIcon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="4" y1="6" x2="20" y2="6"/>
                    <line x1="4" y1="12" x2="20" y2="12"/>
                    <line x1="4" y1="18" x2="20" y2="18"/>
                </svg>
                <svg id="closeIcon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="hidden">
                    <path d="M18 6 6 18"/>
                    <path d="m6 6 12 12"/>
                </svg>
            </button>
        </div>

        <!-- 移动端菜单 -->
        <div class="mobile-menu" id="mobileMenu">
            <a href="../index.html" class="mobile-nav-link" data-page="home">首页</a>
            <a href="domestic-transport.html" class="mobile-nav-link" data-page="domestic-transport">境内用车</a>
            <a href="signature-tickets.html" class="mobile-nav-link" data-page="signature-tickets">特色船票</a>
            <a href="international-tours.html" class="mobile-nav-link" data-page="international-tours">国际旅游</a>
            <a href="business-reception.html" class="mobile-nav-link" data-page="business-reception">商务接待</a>
            <a href="island-hopping.html" class="mobile-nav-link active" data-page="island-hopping">海岛旅游</a>
            <a href="sky-pier-cafe.html" class="mobile-nav-link" data-page="sky-pier-cafe">天空号船咖啡厅</a>
            <a href="about.html" class="mobile-nav-link" data-page="about">关于我们</a>
            <a href="contact.html" class="mobile-nav-link nav-cta-mobile">联系我们</a>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main>
        <!-- Hero 区域 -->
        <section class="hero-section">
            <div class="hero-background" style="background-image: url('https://images.pexels.com/photos/1366919/pexels-photo-1366919.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop')"></div>
            <div class="hero-overlay"></div>
            <div class="hero-content">
                <h1 class="text-4xl md:text-5xl lg:text-7xl font-bold mb-6 leading-tight">
                    马来西亚海岛旅游
                </h1>
                <p class="text-lg md:text-xl mb-8 max-w-3xl mx-auto leading-relaxed opacity-90">
                    探索马来西亚最美丽的海岛，享受阳光、沙滩、海浪，体验热带天堂的无限魅力
                </p>
                <div class="button-group">
                    <a href="#islands" class="btn btn-primary btn-lg">
                        热门海岛
                    </a>
                    <a href="#packages" class="btn btn-outline btn-lg">
                        查看套餐
                    </a>
                </div>
            </div>
        </section>

        <!-- 热门海岛区域 -->
        <section class="responsive-py-20" id="islands">
            <div class="container">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                        热门海岛目的地
                    </h2>
                    <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                        精选马来西亚最受欢迎的海岛，每个岛屿都有独特的魅力和美景
                    </p>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
                    <!-- 兰卡威 -->
                    <div class="card">
                        <div class="aspect-video bg-gray-200 rounded-t-xl overflow-hidden">
                            <img src="https://images.pexels.com/photos/1366919/pexels-photo-1366919.jpeg?auto=compress&cs=tinysrgb&w=800&h=450&fit=crop" 
                                 alt="兰卡威" class="w-full h-full object-cover">
                        </div>
                        <div class="card-body">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-2xl font-bold text-gray-900">兰卡威</h3>
                                <div class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                                    传奇之岛
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4 leading-relaxed">
                                马来西亚最著名的度假岛屿，拥有美丽的海滩、神秘的传说和丰富的自然景观。
                                天空之桥、珍南海滩、黑沙海滩等景点让您流连忘返。
                            </p>
                            <div class="space-y-2 mb-4">
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                                        <circle cx="12" cy="10" r="3"/>
                                    </svg>
                                    天空之桥、珍南海滩、黑沙海滩
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <circle cx="12" cy="12" r="10"/>
                                        <polyline points="12,6 12,12 16,14"/>
                                    </svg>
                                    3天2夜 / 4天3夜 / 5天4夜
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="text-2xl font-bold text-blue-600">
                                    RM 599 起
                                </div>
                                <button class="btn btn-primary btn-sm" onclick="selectIsland('langkawi')">
                                    选择兰卡威
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 刁曼岛 -->
                    <div class="card">
                        <div class="aspect-video bg-gray-200 rounded-t-xl overflow-hidden">
                            <img src="https://images.pexels.com/photos/302899/pexels-photo-302899.jpeg?auto=compress&cs=tinysrgb&w=800&h=450&fit=crop" 
                                 alt="刁曼岛" class="w-full h-full object-cover">
                        </div>
                        <div class="card-body">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-2xl font-bold text-gray-900">刁曼岛</h3>
                                <div class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                                    潜水天堂
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4 leading-relaxed">
                                被誉为世界十大最美海岛之一，拥有清澈的海水和丰富的海洋生物。
                                是潜水和浮潜爱好者的天堂，也是度假放松的理想之地。
                            </p>
                            <div class="space-y-2 mb-4">
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                                        <circle cx="12" cy="10" r="3"/>
                                    </svg>
                                    ABC海滩、珊瑚岛、猴子湾
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <circle cx="12" cy="12" r="10"/>
                                        <polyline points="12,6 12,12 16,14"/>
                                    </svg>
                                    2天1夜 / 3天2夜 / 4天3夜
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="text-2xl font-bold text-blue-600">
                                    RM 399 起
                                </div>
                                <button class="btn btn-primary btn-sm" onclick="selectIsland('tioman')">
                                    选择刁曼岛
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- 热浪岛 -->
                    <div class="card">
                        <div class="aspect-video bg-gray-200 rounded-t-xl overflow-hidden">
                            <img src="https://images.pexels.com/photos/1271619/pexels-photo-1271619.jpeg?auto=compress&cs=tinysrgb&w=800&h=450&fit=crop" 
                                 alt="热浪岛" class="w-full h-full object-cover">
                        </div>
                        <div class="card-body">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-2xl font-bold text-gray-900">热浪岛</h3>
                                <div class="bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm font-medium">
                                    海龟之家
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4 leading-relaxed">
                                以海龟保护区而闻名，拥有原始的自然环境和清澈的海水。
                                是观赏海龟、浮潜和享受宁静海岛生活的绝佳选择。
                            </p>
                            <div class="space-y-2 mb-4">
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                                        <circle cx="12" cy="10" r="3"/>
                                    </svg>
                                    海龟保护区、长滩、珊瑚花园
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <circle cx="12" cy="12" r="10"/>
                                        <polyline points="12,6 12,12 16,14"/>
                                    </svg>
                                    2天1夜 / 3天2夜
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="text-2xl font-bold text-blue-600">
                                    RM 499 起
                                </div>
                                <button class="btn btn-primary btn-sm" onclick="selectIsland('redang')">
                                    选择热浪岛
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 邦咯岛 -->
                    <div class="card">
                        <div class="aspect-video bg-gray-200 rounded-t-xl overflow-hidden">
                            <img src="https://images.pexels.com/photos/1366919/pexels-photo-1366919.jpeg?auto=compress&cs=tinysrgb&w=800&h=450&fit=crop" 
                                 alt="邦咯岛" class="w-full h-full object-cover">
                        </div>
                        <div class="card-body">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-2xl font-bold text-gray-900">邦咯岛</h3>
                                <div class="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium">
                                    历史之岛
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4 leading-relaxed">
                                马来西亚第一个海滩度假村的所在地，拥有丰富的历史文化和美丽的海滩。
                                适合家庭度假和文化探索。
                            </p>
                            <div class="space-y-2 mb-4">
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                                        <circle cx="12" cy="10" r="3"/>
                                    </svg>
                                    荷兰堡、珊瑚湾、渔村
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <circle cx="12" cy="12" r="10"/>
                                        <polyline points="12,6 12,12 16,14"/>
                                    </svg>
                                    2天1夜 / 3天2夜
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="text-2xl font-bold text-blue-600">
                                    RM 349 起
                                </div>
                                <button class="btn btn-primary btn-sm" onclick="selectIsland('pangkor')">
                                    选择邦咯岛
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 海岛活动区域 -->
        <section class="responsive-py-20 bg-gray-50">
            <div class="container">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                        海岛活动体验
                    </h2>
                    <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                        丰富多彩的海岛活动，让您的海岛之旅更加精彩难忘
                    </p>
                </div>

                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                <path d="M12 2L2 7l10 5 10-5-10-5z"/>
                                <path d="M2 17l10 5 10-5"/>
                                <path d="M2 12l10 5 10-5"/>
                            </svg>
                        </div>
                        <h3 class="feature-title">浮潜探索</h3>
                        <p class="feature-description">
                            探索海底世界，观赏五彩斑斓的珊瑚礁和热带鱼类
                        </p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                <circle cx="12" cy="12" r="10"/>
                                <path d="M8 14s1.5 2 4 2 4-2 4-2"/>
                                <line x1="9" y1="9" x2="9.01" y2="9"/>
                                <line x1="15" y1="9" x2="15.01" y2="9"/>
                            </svg>
                        </div>
                        <h3 class="feature-title">海滩休闲</h3>
                        <p class="feature-description">
                            在洁白的沙滩上享受阳光浴，感受海风轻拂的惬意时光
                        </p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                <path d="M8 6v6h8V6"/>
                                <path d="M4 6h16l2 6v6a2 2 0 0 1-2 2h-2a2 2 0 0 1-2-2v-2H8v2a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-6l2-6Z"/>
                                <circle cx="8" cy="16" r="2"/>
                                <circle cx="16" cy="16" r="2"/>
                            </svg>
                        </div>
                        <h3 class="feature-title">岛屿跳跃</h3>
                        <p class="feature-description">
                            乘船游览多个岛屿，体验不同岛屿的独特魅力
                        </p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"/>
                                <circle cx="12" cy="13" r="4"/>
                            </svg>
                        </div>
                        <h3 class="feature-title">日落摄影</h3>
                        <p class="feature-description">
                            捕捉海岛最美的日落时刻，留下珍贵的回忆
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 海岛套餐区域 -->
        <section class="responsive-py-20" id="packages">
            <div class="container">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                        海岛旅游套餐
                    </h2>
                    <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                        精心设计的海岛旅游套餐，包含交通、住宿、活动等全方位服务
                    </p>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- 经济套餐 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="text-2xl font-bold text-gray-900">经济套餐</h3>
                            <div class="text-3xl font-bold text-blue-600 mt-2">
                                RM 349 - 599
                                <span class="text-base font-normal text-gray-500">/人</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <ul class="space-y-3 mb-6">
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">往返船票</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">标准酒店住宿</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">码头接送</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">基础浮潜装备</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">旅游保险</span>
                                </li>
                            </ul>
                            <button class="btn btn-primary w-full" onclick="selectPackage('economy')">
                                选择经济套餐
                            </button>
                        </div>
                    </div>

                    <!-- 标准套餐 -->
                    <div class="card border-2 border-blue-500">
                        <div class="card-header">
                            <div class="flex items-center justify-between">
                                <h3 class="text-2xl font-bold text-gray-900">标准套餐</h3>
                                <div class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                                    推荐
                                </div>
                            </div>
                            <div class="text-3xl font-bold text-blue-600 mt-2">
                                RM 699 - 999
                                <span class="text-base font-normal text-gray-500">/人</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <ul class="space-y-3 mb-6">
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">往返船票</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">海景酒店住宿</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">专车接送</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">浮潜一日游</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">岛屿跳跃游</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">海鲜大餐</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">全程旅游保险</span>
                                </li>
                            </ul>
                            <button class="btn btn-primary w-full" onclick="selectPackage('standard')">
                                选择标准套餐
                            </button>
                        </div>
                    </div>

                    <!-- 豪华套餐 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="text-2xl font-bold text-gray-900">豪华套餐</h3>
                            <div class="text-3xl font-bold text-blue-600 mt-2">
                                RM 1,299 - 1,899
                                <span class="text-base font-normal text-gray-500">/人</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <ul class="space-y-3 mb-6">
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">快艇往返</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">豪华度假村</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">私人接送</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">专业潜水指导</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">私人岛屿游</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">SPA 体验</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">高端餐饮</span>
                                </li>
                                <li class="flex items-center">
                                    <div class="list-dot emerald"></div>
                                    <span class="list-text">全程管家服务</span>
                                </li>
                            </ul>
                            <button class="btn btn-primary w-full" onclick="selectPackage('luxury')">
                                选择豪华套餐
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 海岛图片画廊 -->
        <section class="responsive-py-20 bg-gray-50">
            <div class="container">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                        海岛美景画廊
                    </h2>
                    <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                        感受马来西亚海岛的绝美风光，每一张照片都是大自然的杰作
                    </p>
                </div>

                <div class="photo-gallery">
                    <div class="gallery-item">
                        <img src="https://images.pexels.com/photos/1366919/pexels-photo-1366919.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop"
                             alt="兰卡威天空之桥" class="gallery-image">
                        <div class="gallery-overlay">
                            <h3 class="text-white font-semibold mb-2">兰卡威天空之桥</h3>
                            <p class="text-white text-sm opacity-90">壮观的悬空桥梁</p>
                        </div>
                    </div>
                    <div class="gallery-item">
                        <img src="https://images.pexels.com/photos/302899/pexels-photo-302899.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop"
                             alt="刁曼岛珊瑚礁" class="gallery-image">
                        <div class="gallery-overlay">
                            <h3 class="text-white font-semibold mb-2">刁曼岛珊瑚礁</h3>
                            <p class="text-white text-sm opacity-90">五彩斑斓的海底世界</p>
                        </div>
                    </div>
                    <div class="gallery-item">
                        <img src="https://images.pexels.com/photos/1271619/pexels-photo-1271619.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop"
                             alt="热浪岛海龟" class="gallery-image">
                        <div class="gallery-overlay">
                            <h3 class="text-white font-semibold mb-2">热浪岛海龟</h3>
                            <p class="text-white text-sm opacity-90">与海龟的亲密接触</p>
                        </div>
                    </div>
                    <div class="gallery-item">
                        <img src="https://images.pexels.com/photos/1366919/pexels-photo-1366919.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop"
                             alt="邦咯岛日落" class="gallery-image">
                        <div class="gallery-overlay">
                            <h3 class="text-white font-semibold mb-2">邦咯岛日落</h3>
                            <p class="text-white text-sm opacity-90">醉人的黄昏美景</p>
                        </div>
                    </div>
                    <div class="gallery-item">
                        <img src="https://images.pexels.com/photos/302899/pexels-photo-302899.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop"
                             alt="海岛度假村" class="gallery-image">
                        <div class="gallery-overlay">
                            <h3 class="text-white font-semibold mb-2">海岛度假村</h3>
                            <p class="text-white text-sm opacity-90">奢华的住宿体验</p>
                        </div>
                    </div>
                    <div class="gallery-item">
                        <img src="https://images.pexels.com/photos/1271619/pexels-photo-1271619.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop"
                             alt="水上活动" class="gallery-image">
                        <div class="gallery-overlay">
                            <h3 class="text-white font-semibold mb-2">水上活动</h3>
                            <p class="text-white text-sm opacity-90">刺激的海上运动</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 客户评价 -->
        <section class="responsive-py-20">
            <div class="container">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                        客户真实评价
                    </h2>
                    <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                        听听我们的客户分享他们的海岛之旅体验
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- 评价 1 -->
                    <div class="card">
                        <div class="card-body">
                            <div class="flex items-center mb-4">
                                <div class="flex text-yellow-400">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                    </svg>
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                    </svg>
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                    </svg>
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                    </svg>
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                    </svg>
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4 leading-relaxed">
                                "兰卡威的海岛之旅太棒了！天空之桥的景色令人震撼，海水清澈见底，
                                浮潜时看到了很多美丽的热带鱼。服务团队非常专业，强烈推荐！"
                            </p>
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-blue-600 font-semibold">陈</span>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900">陈先生</h4>
                                    <p class="text-sm text-gray-500">兰卡威3天2夜 · 2024年11月</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 评价 2 -->
                    <div class="card">
                        <div class="card-body">
                            <div class="flex items-center mb-4">
                                <div class="flex text-yellow-400">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                    </svg>
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                    </svg>
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                    </svg>
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                    </svg>
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                    </svg>
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4 leading-relaxed">
                                "刁曼岛的潜水体验让我终生难忘！珊瑚礁的颜色太美了，
                                还看到了海龟和各种热带鱼。度假村的服务也很棒，下次还会再来！"
                            </p>
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-green-600 font-semibold">林</span>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900">林女士</h4>
                                    <p class="text-sm text-gray-500">刁曼岛4天3夜 · 2024年10月</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 评价 3 -->
                    <div class="card">
                        <div class="card-body">
                            <div class="flex items-center mb-4">
                                <div class="flex text-yellow-400">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                    </svg>
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                    </svg>
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                    </svg>
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                    </svg>
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                    </svg>
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4 leading-relaxed">
                                "热浪岛的海龟保护区太有意义了！能够近距离观察海龟真的很感动。
                                岛上的环境保护得很好，是一次很有教育意义的旅行。"
                            </p>
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-orange-600 font-semibold">黄</span>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900">黄小姐</h4>
                                    <p class="text-sm text-gray-500">热浪岛3天2夜 · 2024年12月</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 预订表单区域 -->
        <section class="responsive-py-20 bg-gray-50" id="booking">
            <div class="container">
                <div class="max-w-4xl mx-auto">
                    <div class="text-center mb-12">
                        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                            预订您的海岛之旅
                        </h2>
                        <p class="text-lg text-gray-600 leading-relaxed">
                            填写下方表单，我们将为您安排完美的海岛旅游体验
                        </p>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <form id="island-booking-form" class="space-y-6">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="firstName" class="form-label">姓名 *</label>
                                        <input type="text" id="firstName" name="firstName" class="form-input" required>
                                    </div>
                                    <div>
                                        <label for="email" class="form-label">邮箱 *</label>
                                        <input type="email" id="email" name="email" class="form-input" required>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="phone" class="form-label">电话号码</label>
                                        <input type="tel" id="phone" name="phone" class="form-input">
                                    </div>
                                    <div>
                                        <label for="travelers" class="form-label">出行人数 *</label>
                                        <select id="travelers" name="travelers" class="form-select" required>
                                            <option value="">请选择人数</option>
                                            <option value="1">1人</option>
                                            <option value="2">2人</option>
                                            <option value="3">3人</option>
                                            <option value="4">4人</option>
                                            <option value="5">5人</option>
                                            <option value="6+">6人以上</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="island" class="form-label">目的地海岛 *</label>
                                        <select id="island" name="island" class="form-select" required>
                                            <option value="">请选择海岛</option>
                                            <option value="langkawi">兰卡威</option>
                                            <option value="tioman">刁曼岛</option>
                                            <option value="redang">热浪岛</option>
                                            <option value="pangkor">邦咯岛</option>
                                            <option value="multiple">多岛游</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label for="packageType" class="form-label">套餐类型 *</label>
                                        <select id="packageType" name="packageType" class="form-select" required>
                                            <option value="">请选择套餐</option>
                                            <option value="economy">经济套餐</option>
                                            <option value="standard">标准套餐</option>
                                            <option value="luxury">豪华套餐</option>
                                            <option value="custom">定制套餐</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="departureDate" class="form-label">出发日期 *</label>
                                        <input type="date" id="departureDate" name="departureDate" class="form-input" required>
                                    </div>
                                    <div>
                                        <label for="duration" class="form-label">旅行天数</label>
                                        <select id="duration" name="duration" class="form-select">
                                            <option value="">请选择天数</option>
                                            <option value="2">2天1夜</option>
                                            <option value="3">3天2夜</option>
                                            <option value="4">4天3夜</option>
                                            <option value="5">5天4夜</option>
                                            <option value="custom">其他（请在备注中说明）</option>
                                        </select>
                                    </div>
                                </div>

                                <div>
                                    <label for="specialRequests" class="form-label">特殊需求或备注</label>
                                    <textarea id="specialRequests" name="specialRequests" rows="4" class="form-textarea"
                                              placeholder="请告诉我们您的特殊需求，如饮食偏好、住宿要求、特殊活动等..."></textarea>
                                </div>

                                <div class="text-center">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                                            <polyline points="22,6 12,13 2,6"/>
                                        </svg>
                                        提交预订申请
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- CTA 区域 -->
        <section class="responsive-py-20 bg-gradient-to-r from-blue-600 to-teal-600">
            <div class="container">
                <div class="text-center text-white">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
                        开启您的海岛探索之旅
                    </h2>
                    <p class="text-lg md:text-xl mb-8 max-w-3xl mx-auto leading-relaxed opacity-90">
                        马来西亚的美丽海岛等待着您的到来，让我们一起创造难忘的海岛回忆
                    </p>
                    <div class="button-group">
                        <a href="contact.html" class="btn btn-secondary btn-lg">
                            联系我们
                        </a>
                        <a href="tel:+60123456789" class="btn btn-outline btn-lg">
                            电话咨询
                        </a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- JavaScript -->
    <script src="../assets/js/navigation.js"></script>
    <script src="../assets/js/main.js"></script>
    <script>
        // 海岛旅游页面特定功能
        function selectIsland(island) {
            // 滚动到预订表单
            document.getElementById('booking').scrollIntoView({
                behavior: 'smooth'
            });

            // 根据海岛预填表单
            const islandSelect = document.getElementById('island');
            islandSelect.value = island;
        }

        function selectPackage(packageType) {
            // 滚动到预订表单
            document.getElementById('booking').scrollIntoView({
                behavior: 'smooth'
            });

            // 根据套餐类型预填表单
            const packageSelect = document.getElementById('packageType');
            packageSelect.value = packageType;
        }

        // 设置出发日期最小值为今天
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('departureDate').min = today;
        });

        // 表单提交处理
        document.getElementById('island-booking-form').addEventListener('submit', function(e) {
            e.preventDefault();

            // 这里可以添加表单验证和提交逻辑
            alert('感谢您的预订申请！我们将在24小时内与您联系确认行程安排。');

            // 重置表单
            this.reset();
        });
    </script>
</body>
</html>
