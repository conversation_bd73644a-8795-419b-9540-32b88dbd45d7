<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Sky Mirror World Tour 医疗旅游服务 - 健康检查、医疗美容、康复疗养，专业医疗旅游服务">
    <meta name="keywords" content="医疗旅游,健康检查,医疗美容,康复疗养,海外医疗,医疗服务">
    
    <title>医疗旅游服务 - Sky Mirror World Tour</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/components.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <!-- 品牌Logo -->
            <a href="../index.html" class="nav-brand">
                <div class="nav-logo">S</div>
                <span class="nav-title">Sky Mirror World Tour</span>
            </a>

            <!-- 桌面导航菜单 -->
            <div class="nav-menu">
                <a href="../index.html" class="nav-link" data-page="home">首页</a>
                <a href="domestic-transport.html" class="nav-link" data-page="domestic-transport">境内用车</a>
                <a href="signature-tickets.html" class="nav-link" data-page="signature-tickets">特色船票</a>
                <a href="international-tours.html" class="nav-link" data-page="international-tours">国际旅游</a>
                <a href="business-reception.html" class="nav-link" data-page="business-reception">商务接待</a>
                <a href="island-hopping.html" class="nav-link" data-page="island-hopping">海岛旅游</a>
                <a href="hotel-booking.html" class="nav-link" data-page="hotel-booking">酒店代订</a>
                <a href="flight-booking.html" class="nav-link" data-page="flight-booking">机票代订</a>
                <a href="student-groups.html" class="nav-link" data-page="student-groups">学生团</a>
                <a href="medical-tourism.html" class="nav-link active" data-page="medical-tourism">医疗旅游</a>
                <a href="corporate-team-building.html" class="nav-link" data-page="corporate-team-building">公司团建</a>
                <a href="sky-pier-cafe.html" class="nav-link" data-page="sky-pier-cafe">天空号船咖啡厅</a>
                <a href="about.html" class="nav-link" data-page="about">关于我们</a>
                <a href="contact.html" class="nav-cta">联系我们</a>
            </div>

            <!-- 移动端菜单按钮 -->
            <button type="button" class="mobile-menu-btn" id="mobileMenuBtn" aria-label="打开菜单">
                <svg id="menuIcon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="4" y1="6" x2="20" y2="6"/>
                    <line x1="4" y1="12" x2="20" y2="12"/>
                    <line x1="4" y1="18" x2="20" y2="18"/>
                </svg>
                <svg id="closeIcon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="hidden">
                    <path d="M18 6 6 18"/>
                    <path d="m6 6 12 12"/>
                </svg>
            </button>
        </div>

        <!-- 移动端菜单 -->
        <div class="mobile-menu" id="mobileMenu">
            <a href="../index.html" class="mobile-nav-link" data-page="home">首页</a>
            <a href="domestic-transport.html" class="mobile-nav-link" data-page="domestic-transport">境内用车</a>
            <a href="signature-tickets.html" class="mobile-nav-link" data-page="signature-tickets">特色船票</a>
            <a href="international-tours.html" class="mobile-nav-link" data-page="international-tours">国际旅游</a>
            <a href="business-reception.html" class="mobile-nav-link" data-page="business-reception">商务接待</a>
            <a href="island-hopping.html" class="mobile-nav-link" data-page="island-hopping">海岛旅游</a>
            <a href="hotel-booking.html" class="mobile-nav-link" data-page="hotel-booking">酒店代订</a>
            <a href="flight-booking.html" class="mobile-nav-link" data-page="flight-booking">机票代订</a>
            <a href="student-groups.html" class="mobile-nav-link" data-page="student-groups">学生团</a>
            <a href="medical-tourism.html" class="mobile-nav-link active" data-page="medical-tourism">医疗旅游</a>
            <a href="corporate-team-building.html" class="mobile-nav-link" data-page="corporate-team-building">公司团建</a>
            <a href="sky-pier-cafe.html" class="mobile-nav-link" data-page="sky-pier-cafe">天空号船咖啡厅</a>
            <a href="about.html" class="mobile-nav-link" data-page="about">关于我们</a>
            <a href="contact.html" class="mobile-nav-link nav-cta-mobile">联系我们</a>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main>
        <!-- Hero 区域 -->
        <section class="hero-section">
            <div class="hero-background" style="background-image: url('https://images.pexels.com/photos/263402/pexels-photo-263402.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop')"></div>
            <div class="hero-overlay"></div>
            <div class="hero-content">
                <h1 class="text-4xl md:text-5xl lg:text-7xl font-bold mb-6 leading-tight">
                    医疗旅游服务
                </h1>
                <p class="text-lg md:text-xl mb-8 max-w-3xl mx-auto leading-relaxed opacity-90">
                    结合旅游与医疗的专业服务，为您提供健康检查、医疗美容、康复疗养等全方位医疗旅游体验
                </p>
                <div class="button-group">
                    <a href="#services" class="btn btn-primary btn-lg">
                        查看服务
                    </a>
                    <a href="#hospitals" class="btn btn-outline btn-lg">
                        合作医院
                    </a>
                </div>
            </div>
        </section>

        <!-- 医疗旅游服务分类 -->
        <section class="responsive-py-20" id="services">
            <div class="container">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                        医疗旅游服务
                    </h2>
                    <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                        专业的医疗旅游服务，让您在享受旅行的同时获得优质的医疗护理
                    </p>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- 健康检查 -->
                    <div class="card">
                        <div class="aspect-video bg-gray-200 rounded-t-xl overflow-hidden">
                            <img src="https://images.pexels.com/photos/263402/pexels-photo-263402.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop" 
                                 alt="健康检查" class="w-full h-full object-cover">
                        </div>
                        <div class="card-body">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-2xl font-bold text-gray-900">健康检查</h3>
                                <div class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                                    预防保健
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4 leading-relaxed">
                                全面的健康体检服务，包括基础体检、高端体检、专项检查等，早期发现健康问题。
                            </p>
                            <div class="space-y-2 mb-4">
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    全身健康体检
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    癌症筛查
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    心血管检查
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    基因检测
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="text-2xl font-bold text-blue-600">
                                    RM 1,999 起
                                    <span class="text-sm font-normal text-gray-500">/套餐</span>
                                </div>
                                <button class="btn btn-primary btn-sm" onclick="selectService('health-checkup')">
                                    了解详情
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 医疗美容 -->
                    <div class="card border-2 border-pink-500">
                        <div class="aspect-video bg-gray-200 rounded-t-xl overflow-hidden">
                            <img src="https://images.pexels.com/photos/3985163/pexels-photo-3985163.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop" 
                                 alt="医疗美容" class="w-full h-full object-cover">
                        </div>
                        <div class="card-body">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-2xl font-bold text-gray-900">医疗美容</h3>
                                <div class="bg-pink-100 text-pink-800 px-3 py-1 rounded-full text-sm font-medium">
                                    热门
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4 leading-relaxed">
                                专业的医疗美容服务，包括整形手术、微整形、皮肤护理等，让您重获自信美丽。
                            </p>
                            <div class="space-y-2 mb-4">
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    整形手术
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    微整形注射
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    激光美容
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    皮肤护理
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="text-2xl font-bold text-blue-600">
                                    RM 3,999 起
                                    <span class="text-sm font-normal text-gray-500">/项目</span>
                                </div>
                                <button class="btn btn-primary btn-sm" onclick="selectService('cosmetic-surgery')">
                                    了解详情
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 康复疗养 -->
                    <div class="card">
                        <div class="aspect-video bg-gray-200 rounded-t-xl overflow-hidden">
                            <img src="https://images.pexels.com/photos/3985163/pexels-photo-3985163.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop" 
                                 alt="康复疗养" class="w-full h-full object-cover">
                        </div>
                        <div class="card-body">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-2xl font-bold text-gray-900">康复疗养</h3>
                                <div class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                                    康复
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4 leading-relaxed">
                                专业的康复疗养服务，包括术后康复、慢性病管理、养生保健等，恢复身心健康。
                            </p>
                            <div class="space-y-2 mb-4">
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    术后康复
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    物理治疗
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    中医理疗
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    养生保健
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="text-2xl font-bold text-blue-600">
                                    RM 2,499 起
                                    <span class="text-sm font-normal text-gray-500">/疗程</span>
                                </div>
                                <button class="btn btn-primary btn-sm" onclick="selectService('rehabilitation')">
                                    了解详情
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 医疗旅游预订表单 -->
        <section class="responsive-py-20 bg-gray-50" id="booking">
            <div class="container">
                <div class="max-w-4xl mx-auto">
                    <div class="text-center mb-12">
                        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                            医疗旅游咨询
                        </h2>
                        <p class="text-lg text-gray-600 leading-relaxed">
                            填写下方表单，我们的专业医疗顾问将为您提供个性化的医疗旅游方案
                        </p>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <form id="medical-tourism-form" class="space-y-6">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="fullName" class="form-label">姓名 *</label>
                                        <input type="text" id="fullName" name="fullName" class="form-input" required>
                                    </div>
                                    <div>
                                        <label for="age" class="form-label">年龄</label>
                                        <input type="number" id="age" name="age" class="form-input" min="1" max="120">
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="email" class="form-label">邮箱 *</label>
                                        <input type="email" id="email" name="email" class="form-input" required>
                                    </div>
                                    <div>
                                        <label for="phone" class="form-label">电话号码 *</label>
                                        <input type="tel" id="phone" name="phone" class="form-input" required>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="serviceType" class="form-label">服务类型 *</label>
                                        <select id="serviceType" name="serviceType" class="form-select" required>
                                            <option value="">请选择服务类型</option>
                                            <option value="health-checkup">健康检查</option>
                                            <option value="cosmetic-surgery">医疗美容</option>
                                            <option value="rehabilitation">康复疗养</option>
                                            <option value="specialized-treatment">专科治疗</option>
                                            <option value="consultation">咨询评估</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label for="preferredCountry" class="form-label">偏好国家</label>
                                        <select id="preferredCountry" name="preferredCountry" class="form-select">
                                            <option value="">无偏好</option>
                                            <option value="singapore">新加坡</option>
                                            <option value="thailand">泰国</option>
                                            <option value="malaysia">马来西亚</option>
                                            <option value="korea">韩国</option>
                                            <option value="japan">日本</option>
                                            <option value="india">印度</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="budget" class="form-label">预算范围</label>
                                        <select id="budget" name="budget" class="form-select">
                                            <option value="">请选择预算</option>
                                            <option value="under-5000">RM 5,000以下</option>
                                            <option value="5000-10000">RM 5,000-10,000</option>
                                            <option value="10000-20000">RM 10,000-20,000</option>
                                            <option value="20000-50000">RM 20,000-50,000</option>
                                            <option value="over-50000">RM 50,000以上</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label for="preferredDate" class="form-label">期望时间</label>
                                        <input type="date" id="preferredDate" name="preferredDate" class="form-input">
                                    </div>
                                </div>

                                <div>
                                    <label for="medicalHistory" class="form-label">病史和具体需求</label>
                                    <textarea id="medicalHistory" name="medicalHistory" rows="4" class="form-textarea"
                                              placeholder="请详细描述您的病史、当前健康状况、具体医疗需求等..."></textarea>
                                </div>

                                <div>
                                    <label for="specialRequests" class="form-label">特殊要求</label>
                                    <textarea id="specialRequests" name="specialRequests" rows="3" class="form-textarea"
                                              placeholder="如语言偏好、住宿要求、陪同人员、饮食限制等..."></textarea>
                                </div>

                                <div class="text-center">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                                            <polyline points="22,6 12,13 2,6"/>
                                        </svg>
                                        提交咨询申请
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- CTA 区域 -->
        <section class="responsive-py-20 bg-gradient-to-r from-teal-600 to-blue-600">
            <div class="container">
                <div class="text-center text-white">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
                        开启您的健康之旅
                    </h2>
                    <p class="text-lg md:text-xl mb-8 max-w-3xl mx-auto leading-relaxed opacity-90">
                        专业的医疗旅游服务，让您在享受旅行的同时获得最优质的医疗护理
                    </p>
                    <div class="button-group">
                        <a href="contact.html" class="btn btn-secondary btn-lg">
                            联系我们
                        </a>
                        <a href="tel:+60123456789" class="btn btn-outline btn-lg">
                            电话咨询
                        </a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- JavaScript -->
    <script src="../assets/js/navigation.js"></script>
    <script src="../assets/js/main.js"></script>
    <script>
        // 医疗旅游页面特定功能
        function selectService(serviceType) {
            // 滚动到预订表单
            document.getElementById('booking').scrollIntoView({
                behavior: 'smooth'
            });

            // 根据服务类型预填表单
            const serviceSelect = document.getElementById('serviceType');
            serviceSelect.value = serviceType;
        }

        // 设置日期最小值为今天
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('preferredDate').min = today;
        });

        // 表单提交处理
        document.getElementById('medical-tourism-form').addEventListener('submit', function(e) {
            e.preventDefault();

            // 这里可以添加表单验证和提交逻辑
            alert('感谢您的咨询申请！我们的专业医疗顾问将在24小时内与您联系，为您提供个性化的医疗旅游方案。');

            // 重置表单
            this.reset();
        });
    </script>
</body>
</html>
