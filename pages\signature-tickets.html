<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Sky Mirror World Tour 特色船票 - 天空之镜、喂食老鹰、萤火虫观赏、红树林探索">
    <meta name="keywords" content="天空之镜,喂食老鹰,萤火虫,红树林,瓜拉雪兰莪,船票预订">
    
    <title>特色船票体验 - Sky Mirror World Tour</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/components.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    
    <!-- Preload critical resources -->
    <link rel="preconnect" href="https://images.pexels.com">
    <link rel="dns-prefetch" href="//images.pexels.com">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <!-- 品牌Logo -->
            <a href="../index.html" class="nav-brand">
                <div class="nav-logo">S</div>
                <span class="nav-title">Sky Mirror World Tour</span>
            </a>

            <!-- 桌面导航菜单 -->
            <div class="nav-menu">
                <a href="../index.html" class="nav-link" data-page="home">
                    <span>首页</span>
                </a>
                <a href="domestic-transport.html" class="nav-link" data-page="domestic-transport">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9L18 10V6c0-2-2-4-4-4H4c-2 0-4 2-4 4v10c0 .6.4 1 1 1h2"/>
                        <circle cx="7" cy="17" r="2"/>
                        <path d="M9 17h6"/>
                        <circle cx="17" cy="17" r="2"/>
                    </svg>
                    <span>境内用车</span>
                </a>
                <a href="signature-tickets.html" class="nav-link active" data-page="signature-tickets">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M2 9a3 3 0 0 1 0 6v2a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-2a3 3 0 0 1 0-6V7a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v2Z"/>
                        <path d="M13 5v2"/>
                        <path d="M13 17v2"/>
                        <path d="M13 11v2"/>
                    </svg>
                    <span>特色船票</span>
                </a>
                <a href="international-tours.html" class="nav-link" data-page="international-tours">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="10"/>
                        <path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20"/>
                        <path d="M2 12h20"/>
                    </svg>
                    <span>国际旅游</span>
                </a>
                <a href="business-reception.html" class="nav-link" data-page="business-reception">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z"/>
                        <path d="M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2"/>
                        <path d="M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2"/>
                        <path d="M10 6h4"/>
                        <path d="M10 10h4"/>
                        <path d="M10 14h4"/>
                        <path d="M10 18h4"/>
                    </svg>
                    <span>商务接待</span>
                </a>
                <a href="island-hopping.html" class="nav-link" data-page="island-hopping">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M2 6s1.5-2 5-2 5 2 5 2v14s-1.5-2-5-2-5 2-5 2V6Z"/>
                        <path d="M12 6s1.5-2 5-2 5 2 5 2v14s-1.5-2-5-2-5 2-5 2V6Z"/>
                    </svg>
                    <span>海岛旅游</span>
                </a>
                <a href="sky-pier-cafe.html" class="nav-link" data-page="sky-pier-cafe">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M18 8h1a4 4 0 0 1 0 8h-1"/>
                        <path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8Z"/>
                        <line x1="6" y1="1" x2="6" y2="4"/>
                        <line x1="10" y1="1" x2="10" y2="4"/>
                        <line x1="14" y1="1" x2="14" y2="4"/>
                    </svg>
                    <span>天空号船咖啡厅</span>
                </a>
                <a href="about.html" class="nav-link" data-page="about">关于我们</a>
                <a href="contact.html" class="nav-cta">联系我们</a>
            </div>

            <!-- 移动端菜单按钮 -->
            <button type="button" class="mobile-menu-btn" id="mobileMenuBtn" aria-label="打开菜单">
                <svg id="menuIcon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="4" y1="6" x2="20" y2="6"/>
                    <line x1="4" y1="12" x2="20" y2="12"/>
                    <line x1="4" y1="18" x2="20" y2="18"/>
                </svg>
                <svg id="closeIcon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="hidden">
                    <path d="M18 6 6 18"/>
                    <path d="m6 6 12 12"/>
                </svg>
            </button>
        </div>

        <!-- 移动端菜单 -->
        <div class="mobile-menu" id="mobileMenu">
            <a href="../index.html" class="mobile-nav-link" data-page="home">
                <span>首页</span>
            </a>
            <a href="domestic-transport.html" class="mobile-nav-link" data-page="domestic-transport">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9L18 10V6c0-2-2-4-4-4H4c-2 0-4 2-4 4v10c0 .6.4 1 1 1h2"/>
                    <circle cx="7" cy="17" r="2"/>
                    <path d="M9 17h6"/>
                    <circle cx="17" cy="17" r="2"/>
                </svg>
                <span>境内用车</span>
            </a>
            <a href="signature-tickets.html" class="mobile-nav-link active" data-page="signature-tickets">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M2 9a3 3 0 0 1 0 6v2a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-2a3 3 0 0 1 0-6V7a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v2Z"/>
                    <path d="M13 5v2"/>
                    <path d="M13 17v2"/>
                    <path d="M13 11v2"/>
                </svg>
                <span>特色船票</span>
            </a>
            <a href="international-tours.html" class="mobile-nav-link" data-page="international-tours">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="12" cy="12" r="10"/>
                    <path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20"/>
                    <path d="M2 12h20"/>
                </svg>
                <span>国际旅游</span>
            </a>
            <a href="business-reception.html" class="mobile-nav-link" data-page="business-reception">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z"/>
                    <path d="M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2"/>
                    <path d="M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2"/>
                    <path d="M10 6h4"/>
                    <path d="M10 10h4"/>
                    <path d="M10 14h4"/>
                    <path d="M10 18h4"/>
                </svg>
                <span>商务接待</span>
            </a>
            <a href="island-hopping.html" class="mobile-nav-link" data-page="island-hopping">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M2 6s1.5-2 5-2 5 2 5 2v14s-1.5-2-5-2-5 2-5 2V6Z"/>
                    <path d="M12 6s1.5-2 5-2 5 2 5 2v14s-1.5-2-5-2-5 2-5 2V6Z"/>
                </svg>
                <span>海岛旅游</span>
            </a>
            <a href="sky-pier-cafe.html" class="mobile-nav-link" data-page="sky-pier-cafe">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M18 8h1a4 4 0 0 1 0 8h-1"/>
                    <path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8Z"/>
                    <line x1="6" y1="1" x2="6" y2="4"/>
                    <line x1="10" y1="1" x2="10" y2="4"/>
                    <line x1="14" y1="1" x2="14" y2="4"/>
                </svg>
                <span>天空号船咖啡厅</span>
            </a>
            <a href="about.html" class="mobile-nav-link" data-page="about">
                关于我们
            </a>
            <a href="contact.html" class="mobile-nav-link nav-cta-mobile">
                联系我们
            </a>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main>
        <!-- Hero 区域 -->
        <section class="hero-section" id="hero-section">
            <div class="hero-background" id="hero-background"></div>
            <div class="hero-overlay"></div>
            <div class="hero-content">
                <h1 class="text-4xl md:text-5xl lg:text-7xl font-bold mb-6 leading-tight">
                    特色船票体验
                </h1>
                <p class="text-lg md:text-xl mb-8 max-w-3xl mx-auto leading-relaxed opacity-90">
                    探索马来西亚最神奇的自然奇观，体验天空之镜的魅力，观赏老鹰觅食，探索萤火虫的奇妙世界
                </p>
            </div>
        </section>

        <!-- 体验选择区域 -->
        <section class="responsive-py-20 bg-gray-50">
            <div class="container">
                <!-- 选项卡导航 -->
                <div class="tab-nav">
                    <button type="button" class="tab-button active" data-tab="sky-mirror">
                        <span class="tab-icon">🌊</span>
                        <span>天空之镜</span>
                    </button>
                    <button type="button" class="tab-button" data-tab="eagle-feeding">
                        <span class="tab-icon">🦅</span>
                        <span>喂食老鹰</span>
                    </button>
                    <button type="button" class="tab-button" data-tab="firefly">
                        <span class="tab-icon">✨</span>
                        <span>萤火虫观赏</span>
                    </button>
                    <button type="button" class="tab-button" data-tab="mangrove">
                        <span class="tab-icon">🌿</span>
                        <span>红树林探索</span>
                    </button>
                </div>

                <!-- 体验内容 -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- 左侧：体验详情 -->
                    <div class="lg:col-span-2">
                        <div class="card">
                            <div class="card-body">
                                <h2 class="text-2xl md:text-3xl font-bold text-gray-900 mb-4" id="experience-title">
                                    天空之镜
                                </h2>
                                <div class="flex items-center gap-4 mb-4">
                                    <div class="text-3xl font-bold text-blue-600" id="experience-price">
                                        RM 120
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        /人起
                                    </div>
                                    <div class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                                        热门推荐
                                    </div>
                                </div>
                                <p class="text-gray-600 mb-6 leading-relaxed" id="experience-description">
                                    在这里，天空与海水完美融合，创造出令人叹为观止的镜面效果。体验大自然的神奇魅力，在最佳时间段欣赏完美的镜面反射，留下一生难忘的美好回忆。我们的专业导游将为您详细介绍天空之镜的形成原理和最佳拍摄技巧。
                                </p>

                                <!-- 关键信息 -->
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                                    <div class="info-card">
                                        <div class="info-icon">
                                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <circle cx="12" cy="12" r="10"/>
                                                <polyline points="12,6 12,12 16,14"/>
                                            </svg>
                                        </div>
                                        <div class="info-content">
                                            <div class="info-title">体验时长</div>
                                            <div class="info-text" id="experience-duration">4-5小时</div>
                                        </div>
                                    </div>
                                    <div class="info-card">
                                        <div class="info-icon">
                                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <circle cx="12" cy="12" r="10"/>
                                                <path d="M12 6v6l4 2"/>
                                            </svg>
                                        </div>
                                        <div class="info-content">
                                            <div class="info-title">最佳时间</div>
                                            <div class="info-text" id="experience-best-time">早上6:00-10:00</div>
                                        </div>
                                    </div>
                                    <div class="info-card">
                                        <div class="info-icon">
                                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                                                <circle cx="12" cy="10" r="3"/>
                                            </svg>
                                        </div>
                                        <div class="info-content">
                                            <div class="info-title">出发地点</div>
                                            <div class="info-text" id="experience-location">瓜拉雪兰莪</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 费用包含/不含 -->
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                    <div>
                                        <h3 class="font-semibold text-gray-900 mb-3 flex items-center">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2 text-emerald-500">
                                                <polyline points="20,6 9,17 4,12"/>
                                            </svg>
                                            费用包含
                                        </h3>
                                        <ul class="feature-list" id="includes-list">
                                            <li class="flex items-center space-x-2">
                                                <div class="list-dot emerald"></div>
                                                <span class="list-text">往返船票</span>
                                            </li>
                                            <li class="flex items-center space-x-2">
                                                <div class="list-dot emerald"></div>
                                                <span class="list-text">专业向导</span>
                                            </li>
                                            <li class="flex items-center space-x-2">
                                                <div class="list-dot emerald"></div>
                                                <span class="list-text">救生衣</span>
                                            </li>
                                            <li class="flex items-center space-x-2">
                                                <div class="list-dot emerald"></div>
                                                <span class="list-text">拍照服务</span>
                                            </li>
                                        </ul>
                                    </div>
                                    <div>
                                        <h3 class="font-semibold text-gray-900 mb-3 flex items-center">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2 text-red-500">
                                                <line x1="18" y1="6" x2="6" y2="18"/>
                                                <line x1="6" y1="6" x2="18" y2="18"/>
                                            </svg>
                                            费用不含
                                        </h3>
                                        <ul class="feature-list" id="excludes-list">
                                            <li class="flex items-center space-x-2">
                                                <div class="list-dot red"></div>
                                                <span class="list-text">餐食</span>
                                            </li>
                                            <li class="flex items-center space-x-2">
                                                <div class="list-dot red"></div>
                                                <span class="list-text">个人消费</span>
                                            </li>
                                            <li class="flex items-center space-x-2">
                                                <div class="list-dot red"></div>
                                                <span class="list-text">小费</span>
                                            </li>
                                        </ul>
                                    </div>
                                </div>

                                <!-- 温馨提示 -->
                                <div class="alert yellow">
                                    <h3 class="alert-title flex items-center">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                            <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
                                            <line x1="12" y1="9" x2="12" y2="13"/>
                                            <line x1="12" y1="17" x2="12.01" y2="17"/>
                                        </svg>
                                        温馨提示
                                    </h3>
                                    <ul class="feature-list" id="tips-list">
                                        <li class="flex items-start space-x-2">
                                            <div class="list-dot yellow"></div>
                                            <span class="list-text">建议穿着鲜艳色彩的衣服</span>
                                        </li>
                                        <li class="flex items-start space-x-2">
                                            <div class="list-dot yellow"></div>
                                            <span class="list-text">请准备防晒用品</span>
                                        </li>
                                        <li class="flex items-start space-x-2">
                                            <div class="list-dot yellow"></div>
                                            <span class="list-text">建议携带帽子和太阳镜</span>
                                        </li>
                                    </ul>
                                </div>

                                <!-- 图片画廊 -->
                                <div class="mt-6">
                                    <h3 class="font-semibold text-gray-900 mb-4">精彩瞬间</h3>
                                    <div class="photo-gallery" id="photo-gallery">
                                        <div class="gallery-item">
                                            <img src="https://images.pexels.com/photos/1680247/pexels-photo-1680247.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop" 
                                                 alt="天空之镜 1" class="gallery-image">
                                        </div>
                                        <div class="gallery-item">
                                            <img src="https://images.pexels.com/photos/1680247/pexels-photo-1680247.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop" 
                                                 alt="天空之镜 2" class="gallery-image">
                                        </div>
                                        <div class="gallery-item">
                                            <img src="https://images.pexels.com/photos/1680247/pexels-photo-1680247.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop" 
                                                 alt="天空之镜 3" class="gallery-image">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧：预订表单 -->
                    <div class="lg:col-span-1">
                        <div class="card sticky-top">
                            <div class="card-header">
                                <h3 class="text-xl font-bold text-gray-900">立即预订</h3>
                            </div>
                            <div class="card-body">
                                <form id="booking-form" class="space-y-4">
                                    <!-- 日期选择 -->
                                    <div class="form-group">
                                        <label for="booking-date" class="form-label">选择日期</label>
                                        <input type="date" id="booking-date" class="form-input" required>
                                    </div>

                                    <!-- 时间选择 -->
                                    <div class="form-group">
                                        <label class="form-label">选择时间</label>
                                        <div class="grid grid-cols-2 gap-2" id="time-slots">
                                            <button type="button" class="time-slot-btn" data-time="06:00">06:00</button>
                                            <button type="button" class="time-slot-btn" data-time="07:00">07:00</button>
                                            <button type="button" class="time-slot-btn" data-time="08:00">08:00</button>
                                            <button type="button" class="time-slot-btn" data-time="09:00">09:00</button>
                                        </div>
                                    </div>

                                    <!-- 人数选择 -->
                                    <div class="form-group">
                                        <label for="passenger-count" class="form-label">参与人数</label>
                                        <select id="passenger-count" class="form-select" required>
                                            <option value="1">1人</option>
                                            <option value="2">2人</option>
                                            <option value="3">3人</option>
                                            <option value="4">4人</option>
                                            <option value="5">5人</option>
                                            <option value="6">6人以上</option>
                                        </select>
                                    </div>

                                    <!-- 联系信息 -->
                                    <div class="form-group">
                                        <label for="contact-name" class="form-label">联系人姓名</label>
                                        <input type="text" id="contact-name" class="form-input" required>
                                    </div>

                                    <div class="form-group">
                                        <label for="contact-phone" class="form-label">联系电话</label>
                                        <input type="tel" id="contact-phone" class="form-input" required>
                                    </div>

                                    <!-- 特殊需求 -->
                                    <div class="form-group">
                                        <label for="special-requirements" class="form-label">特殊需求（可选）</label>
                                        <textarea id="special-requirements" class="form-textarea" rows="3" placeholder="请告诉我们您的特殊需求..."></textarea>
                                    </div>

                                    <!-- 提交按钮 -->
                                    <button type="submit" class="btn btn-primary w-full">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M2 9a3 3 0 0 1 0 6v2a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-2a3 3 0 0 1 0-6V7a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v2Z"/>
                                            <path d="M13 5v2"/>
                                            <path d="M13 17v2"/>
                                            <path d="M13 11v2"/>
                                        </svg>
                                        立即预订
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- JavaScript -->
    <script src="../assets/js/data-loader.js"></script>
    <script src="../assets/js/navigation.js"></script>
    <script src="../assets/js/signature-tickets.js"></script>
    <script src="../assets/js/main.js"></script>
</body>
</html>
