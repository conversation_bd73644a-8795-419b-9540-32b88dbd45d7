<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Sky Mirror World Tour 学生团服务 - 游学、研学、考察、团康活动，专业教育旅游服务">
    <meta name="keywords" content="学生团,游学,研学,考察,团康活动,教育旅游,学校旅行">
    
    <title>学生团服务 - Sky Mirror World Tour</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/components.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <!-- 品牌Logo -->
            <a href="../index.html" class="nav-brand">
                <div class="nav-logo">S</div>
                <span class="nav-title">Sky Mirror World Tour</span>
            </a>

            <!-- 桌面导航菜单 -->
            <div class="nav-menu">
                <a href="../index.html" class="nav-link" data-page="home">首页</a>
                <a href="domestic-transport.html" class="nav-link" data-page="domestic-transport">境内用车</a>
                <a href="signature-tickets.html" class="nav-link" data-page="signature-tickets">特色船票</a>
                <a href="international-tours.html" class="nav-link" data-page="international-tours">国际旅游</a>
                <a href="business-reception.html" class="nav-link" data-page="business-reception">商务接待</a>
                <a href="island-hopping.html" class="nav-link" data-page="island-hopping">海岛旅游</a>
                <a href="hotel-booking.html" class="nav-link" data-page="hotel-booking">酒店代订</a>
                <a href="flight-booking.html" class="nav-link" data-page="flight-booking">机票代订</a>
                <a href="student-groups.html" class="nav-link active" data-page="student-groups">学生团</a>
                <a href="medical-tourism.html" class="nav-link" data-page="medical-tourism">医疗旅游</a>
                <a href="corporate-team-building.html" class="nav-link" data-page="corporate-team-building">公司团建</a>
                <a href="sky-pier-cafe.html" class="nav-link" data-page="sky-pier-cafe">天空号船咖啡厅</a>
                <a href="about.html" class="nav-link" data-page="about">关于我们</a>
                <a href="contact.html" class="nav-cta">联系我们</a>
            </div>

            <!-- 移动端菜单按钮 -->
            <button type="button" class="mobile-menu-btn" id="mobileMenuBtn" aria-label="打开菜单">
                <svg id="menuIcon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="4" y1="6" x2="20" y2="6"/>
                    <line x1="4" y1="12" x2="20" y2="12"/>
                    <line x1="4" y1="18" x2="20" y2="18"/>
                </svg>
                <svg id="closeIcon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="hidden">
                    <path d="M18 6 6 18"/>
                    <path d="m6 6 12 12"/>
                </svg>
            </button>
        </div>

        <!-- 移动端菜单 -->
        <div class="mobile-menu" id="mobileMenu">
            <a href="../index.html" class="mobile-nav-link" data-page="home">首页</a>
            <a href="domestic-transport.html" class="mobile-nav-link" data-page="domestic-transport">境内用车</a>
            <a href="signature-tickets.html" class="mobile-nav-link" data-page="signature-tickets">特色船票</a>
            <a href="international-tours.html" class="mobile-nav-link" data-page="international-tours">国际旅游</a>
            <a href="business-reception.html" class="mobile-nav-link" data-page="business-reception">商务接待</a>
            <a href="island-hopping.html" class="mobile-nav-link" data-page="island-hopping">海岛旅游</a>
            <a href="hotel-booking.html" class="mobile-nav-link" data-page="hotel-booking">酒店代订</a>
            <a href="flight-booking.html" class="mobile-nav-link" data-page="flight-booking">机票代订</a>
            <a href="student-groups.html" class="mobile-nav-link active" data-page="student-groups">学生团</a>
            <a href="medical-tourism.html" class="mobile-nav-link" data-page="medical-tourism">医疗旅游</a>
            <a href="corporate-team-building.html" class="mobile-nav-link" data-page="corporate-team-building">公司团建</a>
            <a href="sky-pier-cafe.html" class="mobile-nav-link" data-page="sky-pier-cafe">天空号船咖啡厅</a>
            <a href="about.html" class="mobile-nav-link" data-page="about">关于我们</a>
            <a href="contact.html" class="mobile-nav-link nav-cta-mobile">联系我们</a>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main>
        <!-- Hero 区域 -->
        <section class="hero-section">
            <div class="hero-background" style="background-image: url('https://images.pexels.com/photos/1205651/pexels-photo-1205651.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop')"></div>
            <div class="hero-overlay"></div>
            <div class="hero-content">
                <h1 class="text-4xl md:text-5xl lg:text-7xl font-bold mb-6 leading-tight">
                    学生团专业服务
                </h1>
                <p class="text-lg md:text-xl mb-8 max-w-3xl mx-auto leading-relaxed opacity-90">
                    专为学生群体设计的教育旅游服务，游学、研学、考察、团康活动，寓教于乐，安全第一
                </p>
                <div class="button-group">
                    <a href="#programs" class="btn btn-primary btn-lg">
                        查看项目
                    </a>
                    <a href="#safety" class="btn btn-outline btn-lg">
                        安全保障
                    </a>
                </div>
            </div>
        </section>

        <!-- 学生团项目分类 -->
        <section class="responsive-py-20" id="programs">
            <div class="container">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                        学生团项目
                    </h2>
                    <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                        四大类别的学生团项目，满足不同年龄段和教育需求
                    </p>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- 游学项目 -->
                    <div class="card">
                        <div class="aspect-video bg-gray-200 rounded-t-xl overflow-hidden">
                            <img src="https://images.pexels.com/photos/1205651/pexels-photo-1205651.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop" 
                                 alt="游学项目" class="w-full h-full object-cover">
                        </div>
                        <div class="card-body">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-2xl font-bold text-gray-900">游学项目</h3>
                                <div class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                                    语言学习
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4 leading-relaxed">
                                结合语言学习和文化体验的国际游学项目，提升学生的语言能力和国际视野。
                            </p>
                            <div class="space-y-2 mb-4">
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    英语/中文语言课程
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    文化交流活动
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    寄宿家庭体验
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    学习证书颁发
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="text-2xl font-bold text-blue-600">
                                    RM 2,999 起
                                    <span class="text-sm font-normal text-gray-500">/人</span>
                                </div>
                                <button class="btn btn-primary btn-sm" onclick="selectProgram('study-abroad')">
                                    了解详情
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 研学项目 -->
                    <div class="card">
                        <div class="aspect-video bg-gray-200 rounded-t-xl overflow-hidden">
                            <img src="https://images.pexels.com/photos/159711/books-bookstore-book-reading-159711.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop" 
                                 alt="研学项目" class="w-full h-full object-cover">
                        </div>
                        <div class="card-body">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-2xl font-bold text-gray-900">研学项目</h3>
                                <div class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                                    学术研究
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4 leading-relaxed">
                                以学术研究为主导的深度学习项目，培养学生的研究能力和学术思维。
                            </p>
                            <div class="space-y-2 mb-4">
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    专题研究课程
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    实地调研活动
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    学术报告撰写
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    专家指导交流
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="text-2xl font-bold text-blue-600">
                                    RM 3,499 起
                                    <span class="text-sm font-normal text-gray-500">/人</span>
                                </div>
                                <button class="btn btn-primary btn-sm" onclick="selectProgram('research-study')">
                                    了解详情
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 考察项目 -->
                    <div class="card">
                        <div class="aspect-video bg-gray-200 rounded-t-xl overflow-hidden">
                            <img src="https://images.pexels.com/photos/1181396/pexels-photo-1181396.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop" 
                                 alt="考察项目" class="w-full h-full object-cover">
                        </div>
                        <div class="card-body">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-2xl font-bold text-gray-900">考察项目</h3>
                                <div class="bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm font-medium">
                                    实地体验
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4 leading-relaxed">
                                实地考察和体验式学习，让学生通过亲身体验了解不同行业和文化。
                            </p>
                            <div class="space-y-2 mb-4">
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    企业参观访问
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    历史文化考察
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    科技创新体验
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    职业体验活动
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="text-2xl font-bold text-blue-600">
                                    RM 2,199 起
                                    <span class="text-sm font-normal text-gray-500">/人</span>
                                </div>
                                <button class="btn btn-primary btn-sm" onclick="selectProgram('field-study')">
                                    了解详情
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 团康活动 -->
                    <div class="card">
                        <div class="aspect-video bg-gray-200 rounded-t-xl overflow-hidden">
                            <img src="https://images.pexels.com/photos/1181396/pexels-photo-1181396.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop" 
                                 alt="团康活动" class="w-full h-full object-cover">
                        </div>
                        <div class="card-body">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-2xl font-bold text-gray-900">团康活动</h3>
                                <div class="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium">
                                    团队建设
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4 leading-relaxed">
                                以团队建设和身心健康为目标的活动项目，增强学生的团队合作精神。
                            </p>
                            <div class="space-y-2 mb-4">
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    户外拓展训练
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    团队合作游戏
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    文艺表演活动
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="mr-2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    心理健康辅导
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="text-2xl font-bold text-blue-600">
                                    RM 1,599 起
                                    <span class="text-sm font-normal text-gray-500">/人</span>
                                </div>
                                <button class="btn btn-primary btn-sm" onclick="selectProgram('team-building')">
                                    了解详情
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 安全保障措施 -->
        <section class="responsive-py-20 bg-gray-50" id="safety">
            <div class="container">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                        安全保障措施
                    </h2>
                    <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                        学生安全是我们的首要考虑，完善的安全保障体系确保每位学生的安全
                    </p>
                </div>

                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10"/>
                                <path d="M9 12l2 2 4-4"/>
                            </svg>
                        </div>
                        <h3 class="feature-title">全程保险</h3>
                        <p class="feature-description">
                            为每位学生购买全程旅游保险，包含意外伤害、医疗费用等保障
                        </p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
                                <circle cx="9" cy="7" r="4"/>
                                <path d="M22 21v-2a4 4 0 0 0-3-3.87"/>
                                <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                            </svg>
                        </div>
                        <h3 class="feature-title">专业领队</h3>
                        <p class="feature-description">
                            经验丰富的专业领队全程陪同，师生比例1:8，确保每位学生得到照顾
                        </p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                <path d="M22 12h-4l-3 9L9 3l-3 9H2"/>
                            </svg>
                        </div>
                        <h3 class="feature-title">医疗支持</h3>
                        <p class="feature-description">
                            配备专业医护人员，建立紧急医疗联络网，确保及时医疗救助
                        </p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                                <path d="M9 12l2 2 4-4"/>
                                <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"/>
                                <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"/>
                                <path d="M12 3c0 1-1 3-3 3s-3-2-3-3 1-3 3-3 3 2 3 3"/>
                                <path d="M12 21c0-1 1-3 3-3s3 2 3 3-1 3-3 3-3-2-3-3"/>
                            </svg>
                        </div>
                        <h3 class="feature-title">实时定位</h3>
                        <p class="feature-description">
                            采用GPS定位系统，实时掌握学生位置，家长可通过APP查看动态
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 学生团预订表单 -->
        <section class="responsive-py-20" id="booking">
            <div class="container">
                <div class="max-w-4xl mx-auto">
                    <div class="text-center mb-12">
                        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                            学生团预订申请
                        </h2>
                        <p class="text-lg text-gray-600 leading-relaxed">
                            填写下方表单，我们的专业顾问将为您定制最适合的学生团项目
                        </p>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <form id="student-group-form" class="space-y-6">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="schoolName" class="form-label">学校名称 *</label>
                                        <input type="text" id="schoolName" name="schoolName" class="form-input" required>
                                    </div>
                                    <div>
                                        <label for="contactPerson" class="form-label">联系人 *</label>
                                        <input type="text" id="contactPerson" name="contactPerson" class="form-input" required>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="position" class="form-label">职位</label>
                                        <input type="text" id="position" name="position" class="form-input" placeholder="如：年级主任、班主任等">
                                    </div>
                                    <div>
                                        <label for="email" class="form-label">邮箱 *</label>
                                        <input type="email" id="email" name="email" class="form-input" required>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="phone" class="form-label">电话号码 *</label>
                                        <input type="tel" id="phone" name="phone" class="form-input" required>
                                    </div>
                                    <div>
                                        <label for="programType" class="form-label">项目类型 *</label>
                                        <select id="programType" name="programType" class="form-select" required>
                                            <option value="">请选择项目类型</option>
                                            <option value="study-abroad">游学项目</option>
                                            <option value="research-study">研学项目</option>
                                            <option value="field-study">考察项目</option>
                                            <option value="team-building">团康活动</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    <div>
                                        <label for="studentCount" class="form-label">学生人数 *</label>
                                        <select id="studentCount" name="studentCount" class="form-select" required>
                                            <option value="">请选择人数</option>
                                            <option value="10-20">10-20人</option>
                                            <option value="21-30">21-30人</option>
                                            <option value="31-40">31-40人</option>
                                            <option value="41-50">41-50人</option>
                                            <option value="50+">50人以上</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label for="ageGroup" class="form-label">年龄段 *</label>
                                        <select id="ageGroup" name="ageGroup" class="form-select" required>
                                            <option value="">请选择年龄段</option>
                                            <option value="primary">小学生（6-12岁）</option>
                                            <option value="middle">中学生（13-15岁）</option>
                                            <option value="high">高中生（16-18岁）</option>
                                            <option value="university">大学生（18岁以上）</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label for="duration" class="form-label">活动天数</label>
                                        <select id="duration" name="duration" class="form-select">
                                            <option value="">请选择天数</option>
                                            <option value="3">3天2夜</option>
                                            <option value="5">5天4夜</option>
                                            <option value="7">7天6夜</option>
                                            <option value="10">10天9夜</option>
                                            <option value="custom">其他（请在备注中说明）</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="preferredDate" class="form-label">期望出行日期</label>
                                        <input type="date" id="preferredDate" name="preferredDate" class="form-input">
                                    </div>
                                    <div>
                                        <label for="budget" class="form-label">预算范围（每人）</label>
                                        <select id="budget" name="budget" class="form-select">
                                            <option value="">请选择预算</option>
                                            <option value="under-1500">RM 1500以下</option>
                                            <option value="1500-2500">RM 1500-2500</option>
                                            <option value="2500-3500">RM 2500-3500</option>
                                            <option value="over-3500">RM 3500以上</option>
                                        </select>
                                    </div>
                                </div>

                                <div>
                                    <label for="requirements" class="form-label">具体需求和期望</label>
                                    <textarea id="requirements" name="requirements" rows="4" class="form-textarea"
                                              placeholder="请详细描述您的具体需求，如学习目标、特殊要求、安全考虑等..."></textarea>
                                </div>

                                <div class="text-center">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                                            <polyline points="22,6 12,13 2,6"/>
                                        </svg>
                                        提交申请
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- CTA 区域 -->
        <section class="responsive-py-20 bg-gradient-to-r from-green-600 to-blue-600">
            <div class="container">
                <div class="text-center text-white">
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
                        为学生打造难忘的学习之旅
                    </h2>
                    <p class="text-lg md:text-xl mb-8 max-w-3xl mx-auto leading-relaxed opacity-90">
                        专业的学生团服务，安全保障，寓教于乐，让每一次出行都成为宝贵的学习体验
                    </p>
                    <div class="button-group">
                        <a href="contact.html" class="btn btn-secondary btn-lg">
                            联系我们
                        </a>
                        <a href="tel:+60123456789" class="btn btn-outline btn-lg">
                            电话咨询
                        </a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- JavaScript -->
    <script src="../assets/js/navigation.js"></script>
    <script src="../assets/js/main.js"></script>
    <script>
        // 学生团页面特定功能
        function selectProgram(programType) {
            // 滚动到预订表单
            document.getElementById('booking').scrollIntoView({
                behavior: 'smooth'
            });

            // 根据项目类型预填表单
            const programSelect = document.getElementById('programType');
            programSelect.value = programType;
        }

        // 设置日期最小值为今天
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('preferredDate').min = today;
        });

        // 表单提交处理
        document.getElementById('student-group-form').addEventListener('submit', function(e) {
            e.preventDefault();

            // 这里可以添加表单验证和提交逻辑
            alert('感谢您的申请！我们的专业顾问将在24小时内与您联系，为您定制最适合的学生团项目。');

            // 重置表单
            this.reset();
        });
    </script>
</body>
</html>
