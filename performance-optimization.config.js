/**
 * Sky Mirror World Tour - 性能优化配置
 * 用于构建工具和部署的性能优化设置
 */

const performanceConfig = {
    // CSS 优化配置
    css: {
        // 关键 CSS 内联
        critical: {
            enabled: true,
            inline: true,
            minify: true,
            // 关键 CSS 选择器
            selectors: [
                '.navbar',
                '.hero-section',
                '.btn',
                '.container',
                '.grid',
                '.card'
            ]
        },
        
        // CSS 压缩
        minification: {
            enabled: true,
            removeComments: true,
            removeUnusedCSS: true,
            mergeDuplicateRules: true
        },
        
        // CSS 拆分
        splitting: {
            enabled: true,
            // 按页面拆分 CSS
            byPage: true,
            // 按组件拆分 CSS
            byComponent: false
        }
    },

    // JavaScript 优化配置
    javascript: {
        // JS 压缩
        minification: {
            enabled: true,
            removeComments: true,
            removeConsole: true, // 生产环境移除 console
            mangleNames: true
        },
        
        // 代码拆分
        splitting: {
            enabled: true,
            // 按页面拆分
            byPage: true,
            // 按功能拆分
            byFeature: true,
            // 第三方库单独打包
            vendor: true
        },
        
        // 懒加载配置
        lazyLoading: {
            enabled: true,
            // 懒加载模块
            modules: [
                'signature-tickets.js',
                'contact-form.js',
                'performance-monitor.js'
            ]
        }
    },

    // 图片优化配置
    images: {
        // 图片压缩
        compression: {
            enabled: true,
            quality: 85,
            progressive: true
        },
        
        // 响应式图片
        responsive: {
            enabled: true,
            breakpoints: [320, 640, 768, 1024, 1280, 1920],
            formats: ['webp', 'jpg', 'png']
        },
        
        // 懒加载
        lazyLoading: {
            enabled: true,
            placeholder: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2VlZSIvPjx0ZXh0IHg9IjUwIiB5PSI1MCIgZm9udC1zaXplPSIxOCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iIGZpbGw9IiNhYWEiPkxvYWRpbmc8L3RleHQ+PC9zdmc+',
            rootMargin: '50px'
        }
    },

    // 字体优化配置
    fonts: {
        // 字体预加载
        preload: {
            enabled: true,
            fonts: [
                {
                    family: 'Inter',
                    weights: [400, 500, 600, 700],
                    display: 'swap'
                }
            ]
        },
        
        // 字体子集化
        subsetting: {
            enabled: true,
            // 中文字符集
            chinese: true,
            // 英文字符集
            latin: true
        }
    },

    // 缓存配置
    caching: {
        // 静态资源缓存
        staticAssets: {
            enabled: true,
            maxAge: 31536000, // 1年
            immutable: true
        },
        
        // HTML 缓存
        html: {
            enabled: true,
            maxAge: 3600, // 1小时
            staleWhileRevalidate: 86400 // 24小时
        },
        
        // API 缓存
        api: {
            enabled: true,
            maxAge: 300, // 5分钟
            staleWhileRevalidate: 3600 // 1小时
        }
    },

    // 预加载配置
    preloading: {
        // 关键资源预加载
        critical: {
            enabled: true,
            resources: [
                { href: 'assets/css/main.css', as: 'style' },
                { href: 'assets/js/main.js', as: 'script' },
                { href: 'assets/js/navigation.js', as: 'script' }
            ]
        },
        
        // DNS 预解析
        dnsPrefetch: {
            enabled: true,
            domains: [
                'images.pexels.com',
                'fonts.googleapis.com',
                'www.google-analytics.com'
            ]
        },
        
        // 预连接
        preconnect: {
            enabled: true,
            origins: [
                'https://images.pexels.com',
                'https://fonts.gstatic.com'
            ]
        }
    },

    // 压缩配置
    compression: {
        // Gzip 压缩
        gzip: {
            enabled: true,
            level: 6,
            threshold: 1024 // 1KB 以上的文件才压缩
        },
        
        // Brotli 压缩
        brotli: {
            enabled: true,
            quality: 6,
            threshold: 1024
        }
    },

    // 监控配置
    monitoring: {
        // 性能监控
        performance: {
            enabled: true,
            // Core Web Vitals
            coreWebVitals: true,
            // 自定义指标
            customMetrics: true,
            // 错误监控
            errorTracking: true
        },
        
        // 分析工具
        analytics: {
            enabled: true,
            // Google Analytics
            googleAnalytics: {
                enabled: false,
                trackingId: 'GA_TRACKING_ID'
            },
            // 百度统计
            baiduAnalytics: {
                enabled: false,
                siteId: 'BAIDU_SITE_ID'
            }
        }
    },

    // 构建优化
    build: {
        // 代码分割
        codeSplitting: {
            enabled: true,
            strategy: 'page', // 'page' | 'component' | 'feature'
            chunkSize: 244 * 1024 // 244KB
        },
        
        // Tree Shaking
        treeShaking: {
            enabled: true,
            sideEffects: false
        },
        
        // 模块联邦
        moduleFederation: {
            enabled: false
        }
    },

    // 部署优化
    deployment: {
        // CDN 配置
        cdn: {
            enabled: true,
            baseUrl: 'https://cdn.skymirror.com',
            // 静态资源 CDN
            staticAssets: true,
            // 图片 CDN
            images: true
        },
        
        // 服务端渲染
        ssr: {
            enabled: false // 静态网站不需要 SSR
        },
        
        // 静态生成
        staticGeneration: {
            enabled: true,
            // 预生成页面
            prerender: [
                '/',
                '/pages/signature-tickets.html',
                '/pages/contact.html',
                '/pages/about.html'
            ]
        }
    }
};

// 环境特定配置
const environmentConfig = {
    development: {
        css: {
            minification: { enabled: false }
        },
        javascript: {
            minification: { enabled: false, removeConsole: false }
        },
        monitoring: {
            performance: { enabled: true }
        }
    },
    
    production: {
        css: {
            minification: { enabled: true }
        },
        javascript: {
            minification: { enabled: true, removeConsole: true }
        },
        monitoring: {
            performance: { enabled: true },
            analytics: { enabled: true }
        }
    }
};

// 获取当前环境配置
function getConfig(environment = 'production') {
    const baseConfig = performanceConfig;
    const envConfig = environmentConfig[environment] || {};
    
    // 深度合并配置
    return mergeDeep(baseConfig, envConfig);
}

// 深度合并对象
function mergeDeep(target, source) {
    const output = Object.assign({}, target);
    
    if (isObject(target) && isObject(source)) {
        Object.keys(source).forEach(key => {
            if (isObject(source[key])) {
                if (!(key in target)) {
                    Object.assign(output, { [key]: source[key] });
                } else {
                    output[key] = mergeDeep(target[key], source[key]);
                }
            } else {
                Object.assign(output, { [key]: source[key] });
            }
        });
    }
    
    return output;
}

function isObject(item) {
    return item && typeof item === 'object' && !Array.isArray(item);
}

// 导出配置
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        performanceConfig,
        environmentConfig,
        getConfig
    };
} else {
    window.PerformanceConfig = {
        performanceConfig,
        environmentConfig,
        getConfig
    };
}
