import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Navbar from './components/Navbar';
import Footer from './components/Footer';
import Home from './pages/Home';
import DomesticTransport from './pages/DomesticTransport';
import AirportTransfer from './pages/AirportTransfer';
import CharteredCar from './pages/CharteredCar';
import SignatureTickets from './pages/SignatureTickets';
import InternationalTours from './pages/InternationalTours';
import BusinessReception from './pages/BusinessReception';
import IslandHopping from './pages/IslandHopping';
import SkyPierCafe from './pages/SkyPierCafe';
import AboutUs from './pages/AboutUs';
import ContactUs from './pages/ContactUs';

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-white">
        <Navbar />
        <main>
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/domestic-transport" element={<DomesticTransport />} />
            <Route path="/airport-transfer" element={<AirportTransfer />} />
            <Route path="/chartered-car" element={<CharteredCar />} />
            <Route path="/signature-tickets" element={<SignatureTickets />} />
            <Route path="/international-tours" element={<InternationalTours />} />
            <Route path="/business-reception" element={<BusinessReception />} />
            <Route path="/island-hopping" element={<IslandHopping />} />
            <Route path="/sky-pier-cafe" element={<SkyPierCafe />} />
            <Route path="/about" element={<AboutUs />} />
            <Route path="/contact" element={<ContactUs />} />
          </Routes>
        </main>
        <Footer />
      </div>
    </Router>
  );
}

export default App;