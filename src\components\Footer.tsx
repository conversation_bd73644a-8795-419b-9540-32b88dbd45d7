import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Facebook, Instagram, Twitter, Phone, Mail, MapPin } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-emerald-500 rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-lg">S</span>
              </div>
              <span className="text-xl font-bold">Sky Mirror World Tour</span>
            </div>
            <p className="text-gray-300 mb-4 max-w-md">
              您的专属旅行伙伴，为您打造难忘的马来西亚之旅。从天空之镜到海岛度假，我们用心服务每一位旅客。
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-400 hover:text-blue-400 transition-colors">
                <Facebook size={20} />
              </a>
              <a href="#" className="text-gray-400 hover:text-blue-400 transition-colors">
                <Instagram size={20} />
              </a>
              <a href="#" className="text-gray-400 hover:text-blue-400 transition-colors">
                <Twitter size={20} />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">快速链接</h3>
            <ul className="space-y-2">
              <li><Link to="/domestic-transport" className="text-gray-300 hover:text-blue-400 transition-colors">境内用车</Link></li>
              <li><Link to="/signature-tickets" className="text-gray-300 hover:text-blue-400 transition-colors">特色船票</Link></li>
              <li><Link to="/international-tours" className="text-gray-300 hover:text-blue-400 transition-colors">国际旅游</Link></li>
              <li><Link to="/business-reception" className="text-gray-300 hover:text-blue-400 transition-colors">商务接待</Link></li>
              <li><Link to="/island-hopping" className="text-gray-300 hover:text-blue-400 transition-colors">海岛旅游</Link></li>
              <li><Link to="/sky-pier-cafe" className="text-gray-300 hover:text-blue-400 transition-colors">天空号船咖啡厅</Link></li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-semibold mb-4">联系我们</h3>
            <ul className="space-y-2">
              <li className="flex items-center space-x-2">
                <Phone size={16} className="text-blue-400" />
                <span className="text-gray-300">+60 ************</span>
              </li>
              <li className="flex items-center space-x-2">
                <Mail size={16} className="text-blue-400" />
                <span className="text-gray-300"><EMAIL></span>
              </li>
              <li className="flex items-center space-x-2">
                <MapPin size={16} className="text-blue-400" />
                <span className="text-gray-300">瓜拉雪兰莪天空之镜码头</span>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-8 pt-8 text-center">
          <p className="text-gray-400">&copy; 2024 Sky Mirror World Tour. 版权所有.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;