import React from 'react';
import { Award, Users, Heart, Star, Calendar, MapPin } from 'lucide-react';

const AboutUs = () => {
  const timeline = [
    { year: '2018', title: '公司成立', description: '在瓜拉雪兰莪成立，专注于天空之镜旅游服务' },
    { year: '2019', title: '服务扩展', description: '增加机场接送和包车服务，服务范围覆盖全马来西亚' },
    { year: '2020', title: '咖啡厅开业', description: '天空码头咖啡厅正式开业，为旅客提供休憩场所' },
    { year: '2021', title: '团队壮大', description: '团队扩展至20人，获得多项旅游业认证' },
    { year: '2022', title: '服务升级', description: '推出商务接待服务，成为当地知名旅游品牌' },
    { year: '2023', title: '海岛拓展', description: '开展海岛旅游业务，与多家度假村建立合作关系' },
    { year: '2024', title: '国际化发展', description: '筹备国际旅游线路，致力于成为世界级旅游服务商' }
  ];

  const team = [
    {
      name: '张伟明',
      position: '创始人 & CEO',
      description: '拥有15年旅游行业经验，致力于为每位客户提供最优质的服务体验',
      image: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop'
    },
    {
      name: '李美玲',
      position: '运营总监',
      description: '专业的旅游管理背景，负责公司日常运营和客户服务质量管控',
      image: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop'
    },
    {
      name: '王大华',
      position: '首席导游',
      description: '当地资深导游，精通中英马三语，熟悉每一个景点的历史文化',
      image: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop'
    },
    {
      name: '陈小雨',
      position: '客服经理',
      description: '亲切的客服团队领头人，24小时为客户提供专业咨询和服务',
      image: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop'
    }
  ];

  const values = [
    {
      icon: Heart,
      title: '用心服务',
      description: '我们用心对待每一位客户，将您的满意作为我们最大的成就'
    },
    {
      icon: Star,
      title: '专业品质',
      description: '专业的团队，优质的服务，让您的每一次旅行都成为美好回忆'
    },
    {
      icon: Users,
      title: '团队合作',
      description: '团结协作的团队精神，为客户提供无缝衔接的服务体验'
    },
    {
      icon: Award,
      title: '持续创新',
      description: '不断创新服务模式，为客户带来更多惊喜和超值体验'
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative h-96 flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-emerald-600 opacity-90"></div>
        <div 
          className="absolute inset-0 bg-cover bg-center"
          style={{
            backgroundImage: 'url(https://images.pexels.com/photos/1647976/pexels-photo-1647976.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop)'
          }}
        ></div>
        <div className="relative z-10 text-center text-white px-4">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            关于我们
          </h1>
          <p className="text-xl md:text-2xl opacity-90">
            Sky Mirror World Tour 的故事
          </p>
        </div>
      </section>

      {/* Founder's Message */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                创始人寄语
              </h2>
              <div className="prose prose-lg text-gray-600 space-y-4">
                <p className="text-lg italic text-blue-600 mb-4">
                  "旅行不仅仅是到达目的地，更是一次心灵的洗礼和成长的旅程。"
                </p>
                <p>
                  当我第一次站在天空之镜前，看到天空与海水完美融合的那一刻，我就知道这里有着无可替代的魅力。2018年，我们在瓜拉雪兰莪成立了Sky Mirror World Tour，希望能够将这份美好分享给更多的人。
                </p>
                <p>
                  六年来，我们从一个小小的本地旅游服务商，发展成为拥有完整服务体系的专业旅游公司。我们不仅提供天空之镜的旅游服务，还拓展了机场接送、包车服务、商务接待、海岛旅游等多项业务。
                </p>
                <p>
                  我们相信，每一次旅行都应该是独特的、难忘的。因此，我们始终坚持用心服务，专业品质，让每一位客户都能感受到马来西亚的魅力，创造属于自己的美好回忆。
                </p>
                <p className="text-right text-gray-800 font-semibold">
                  —— 张伟明<br />
                  创始人 & CEO
                </p>
              </div>
            </div>
            <div className="relative">
              <img 
                src="https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=600&h=800&fit=crop"
                alt="创始人"
                className="rounded-lg shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Brand Philosophy */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              品牌理念
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              我们的价值观指引着我们的每一次服务，每一个决定
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => {
              const Icon = value.icon;
              return (
                <div key={index} className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Icon size={28} className="text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">
                    {value.title}
                  </h3>
                  <p className="text-gray-600">
                    {value.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Timeline */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              发展历程
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              从2018年至今，我们一步步成长为专业的旅游服务提供商
            </p>
          </div>

          <div className="relative">
            <div className="absolute left-1/2 transform -translate-x-1/2 w-0.5 h-full bg-blue-200"></div>
            <div className="space-y-8">
              {timeline.map((item, index) => (
                <div key={index} className={`flex items-center ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`}>
                  <div className={`w-1/2 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>
                    <div className="bg-white rounded-lg shadow-lg p-6">
                      <div className="flex items-center space-x-2 mb-2">
                        <Calendar size={16} className="text-blue-600" />
                        <span className="text-sm font-semibold text-blue-600">{item.year}</span>
                      </div>
                      <h3 className="text-lg font-bold text-gray-900 mb-2">{item.title}</h3>
                      <p className="text-gray-600">{item.description}</p>
                    </div>
                  </div>
                  <div className="relative z-10 w-4 h-4 bg-blue-600 rounded-full border-4 border-white shadow-lg"></div>
                  <div className="w-1/2"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Team */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              我们的团队
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              专业、热情、负责任的团队成员，为您的旅行保驾护航
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member, index) => (
              <div key={index} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                <img 
                  src={member.image} 
                  alt={member.name}
                  className="w-full h-48 object-cover"
                />
                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-1">{member.name}</h3>
                  <p className="text-blue-600 font-semibold mb-3">{member.position}</p>
                  <p className="text-gray-600 text-sm">{member.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Certifications */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              资质认证
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              我们拥有完善的资质认证，确保为您提供合法、安全、专业的服务
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-gray-50 rounded-xl p-8 text-center">
              <Award size={48} className="text-blue-600 mx-auto mb-4" />
              <h3 className="text-xl font-bold text-gray-900 mb-2">旅游业营业执照</h3>
              <p className="text-gray-600">马来西亚旅游部颁发的正规旅游业营业执照</p>
            </div>
            <div className="bg-gray-50 rounded-xl p-8 text-center">
              <MapPin size={48} className="text-emerald-600 mx-auto mb-4" />
              <h3 className="text-xl font-bold text-gray-900 mb-2">交通运输许可证</h3>
              <p className="text-gray-600">具备合法的客运服务许可证和车辆运营资格</p>
            </div>
            <div className="bg-gray-50 rounded-xl p-8 text-center">
              <Users size={48} className="text-purple-600 mx-auto mb-4" />
              <h3 className="text-xl font-bold text-gray-900 mb-2">服务质量认证</h3>
              <p className="text-gray-600">通过ISO服务质量管理体系认证</p>
            </div>
          </div>
        </div>
      </section>

      {/* Contact CTA */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-emerald-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            准备好开始您的旅程了吗？
          </h2>
          <p className="text-xl text-white mb-8 opacity-90">
            联系我们，让我们为您规划一次难忘的马来西亚之旅
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/contact"
              className="bg-white text-blue-600 px-8 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors"
            >
              立即联系我们
            </a>
            <a
              href="/signature-tickets"
              className="border-2 border-white text-white px-8 py-3 rounded-full font-semibold hover:bg-white hover:text-blue-600 transition-colors"
            >
              预订体验
            </a>
          </div>
        </div>
      </section>
    </div>
  );
};

export default AboutUs;