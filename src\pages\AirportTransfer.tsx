import React, { useState } from 'react';
import { Plane, Clock, Users, Shield, MapPin, Phone, Star, CheckCircle } from 'lucide-react';

const AirportTransfer = () => {
  const [serviceType, setServiceType] = useState('pickup');
  const [formData, setFormData] = useState({
    flightNumber: '',
    date: '',
    time: '',
    destination: '',
    passengers: '1',
    luggage: '1',
    contactName: '',
    contactPhone: '',
    contactEmail: ''
  });

  const carTypes = [
    {
      name: '舒适5座轿车',
      passengers: '1-4人',
      luggage: '2-3件',
      price: '¥180',
      features: ['空调系统', 'GPS导航', '免费WiFi', '手机充电器'],
      image: 'https://images.pexels.com/photos/116675/pexels-photo-116675.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop'
    },
    {
      name: '豪华7座MPV',
      passengers: '1-6人',
      luggage: '4-6件',
      price: '¥280',
      features: ['真皮座椅', '独立空调', '娱乐系统', '行李空间大'],
      image: 'https://images.pexels.com/photos/1637859/pexels-photo-1637859.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop'
    },
    {
      name: '商务9座面包车',
      passengers: '1-8人',
      luggage: '6-8件',
      price: '¥380',
      features: ['宽敞空间', '商务座椅', '大容量行李', '团体出行首选'],
      image: 'https://images.pexels.com/photos/1007410/pexels-photo-1007410.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop'
    }
  ];

  const serviceFeatures = [
    {
      icon: Clock,
      title: '准时可靠',
      description: '24小时服务，实时航班追踪，确保准时接送'
    },
    {
      icon: Shield,
      title: '安全保障',
      description: '专业司机，车辆定期保养，全程保险覆盖'
    },
    {
      icon: Users,
      title: '贴心服务',
      description: '举牌接机，协助行李，提供当地旅游咨询'
    },
    {
      icon: Star,
      title: '透明定价',
      description: '明码标价，无隐藏费用，支持多种支付方式'
    }
  ];

  const pricingDetails = [
    {
      service: '基础服务',
      includes: ['往返接送', '免费等候30分钟', '行李协助', '基础路线'],
      price: '起步价'
    },
    {
      service: '夜间服务',
      includes: ['22:00-06:00', '夜间服务费20%', '安全保障', '24小时客服'],
      price: '+20%'
    },
    {
      service: '超时等候',
      includes: ['超过30分钟', '每15分钟计费', '灵活等候', '实时沟通'],
      price: '¥20/15分钟'
    }
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    alert('预订提交成功！我们会尽快与您联系确认。');
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative h-[70vh] min-h-[500px] max-h-[600px] flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-emerald-600 opacity-90"></div>
        <div 
          className="absolute inset-0 bg-cover bg-center"
          style={{
            backgroundImage: 'url(https://images.pexels.com/photos/2026324/pexels-photo-2026324.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop)'
          }}
        ></div>
        <div className="relative z-10 text-center text-white px-4">
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-3 md:mb-4">
            机场接送服务
          </h1>
          <p className="text-lg md:text-xl lg:text-2xl opacity-90">
            准时可靠，从机场到酒店的无缝衔接
          </p>
        </div>
      </section>

      {/* Service Features */}
      <section className="py-12 md:py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-10 md:mb-12">
            <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
              为什么选择我们的接送服务
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              专业、安全、贴心的机场接送体验
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {serviceFeatures.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <div key={index} className="bg-white rounded-xl shadow-md p-6 text-center hover:shadow-lg transition-shadow">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Icon size={24} className="text-white" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {feature.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-12 md:py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 md:gap-12">
            {/* Left Column - Booking Form */}
            <div>
              <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-6">
                预订接送服务
              </h2>

              {/* Service Type Selection */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  服务类型
                </label>
                <div className="flex space-x-4">
                  <button
                    onClick={() => setServiceType('pickup')}
                    className={`flex-1 px-4 py-3 rounded-lg font-medium transition-colors ${
                      serviceType === 'pickup'
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    机场接机
                  </button>
                  <button
                    onClick={() => setServiceType('dropoff')}
                    className={`flex-1 px-4 py-3 rounded-lg font-medium transition-colors ${
                      serviceType === 'dropoff'
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    机场送机
                  </button>
                </div>
              </div>

              {/* Booking Form */}
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      航班号
                    </label>
                    <input
                      type="text"
                      name="flightNumber"
                      value={formData.flightNumber}
                      onChange={handleInputChange}
                      placeholder="如：MH370"
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {serviceType === 'pickup' ? '抵达' : '出发'}日期
                    </label>
                    <input
                      type="date"
                      name="date"
                      value={formData.date}
                      onChange={handleInputChange}
                      min={new Date().toISOString().split('T')[0]}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {serviceType === 'pickup' ? '抵达' : '出发'}时间
                  </label>
                  <input
                    type="time"
                    name="time"
                    value={formData.time}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {serviceType === 'pickup' ? '目的地酒店/地址' : '出发地酒店/地址'}
                  </label>
                  <input
                    type="text"
                    name="destination"
                    value={formData.destination}
                    onChange={handleInputChange}
                    placeholder="请输入详细地址"
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      乘车人数
                    </label>
                    <select
                      name="passengers"
                      value={formData.passengers}
                      onChange={handleInputChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      {[1, 2, 3, 4, 5, 6, 7, 8].map(num => (
                        <option key={num} value={num}>{num} 人</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      行李件数
                    </label>
                    <select
                      name="luggage"
                      value={formData.luggage}
                      onChange={handleInputChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      {[1, 2, 3, 4, 5, 6, 7, 8].map(num => (
                        <option key={num} value={num}>{num} 件</option>
                      ))}
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    联系人姓名
                  </label>
                  <input
                    type="text"
                    name="contactName"
                    value={formData.contactName}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      联系电话
                    </label>
                    <input
                      type="tel"
                      name="contactPhone"
                      value={formData.contactPhone}
                      onChange={handleInputChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      邮箱地址
                    </label>
                    <input
                      type="email"
                      name="contactEmail"
                      value={formData.contactEmail}
                      onChange={handleInputChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      required
                    />
                  </div>
                </div>

                <button
                  type="submit"
                  className="w-full bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
                >
                  提交预订
                </button>
              </form>
            </div>

            {/* Right Column - Service Info */}
            <div>
              {/* Service Process */}
              <div className="mb-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-6">服务流程</h3>
                <div className="space-y-4">
                  <div className="flex items-start space-x-4">
                    <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">1</div>
                    <div>
                      <h4 className="font-semibold text-gray-900">在线预订</h4>
                      <p className="text-gray-600">填写预订表单，我们会在24小时内确认</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-4">
                    <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">2</div>
                    <div>
                      <h4 className="font-semibold text-gray-900">司机举牌等候</h4>
                      <p className="text-gray-600">司机会在指定地点举牌等候，轻松找到</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-4">
                    <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">3</div>
                    <div>
                      <h4 className="font-semibold text-gray-900">安全送达</h4>
                      <p className="text-gray-600">专业司机安全将您送达目的地</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Car Fleet */}
              <div className="mb-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-6">车队选择</h3>
                <div className="space-y-4">
                  {carTypes.map((car, index) => (
                    <div key={index} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                      <div className="flex items-center space-x-4">
                        <img 
                          src={car.image} 
                          alt={car.name}
                          className="w-20 h-15 object-cover rounded"
                        />
                        <div className="flex-1">
                          <h4 className="font-semibold text-gray-900">{car.name}</h4>
                          <p className="text-sm text-gray-600">建议乘坐：{car.passengers}</p>
                          <p className="text-sm text-gray-600">行李容量：{car.luggage}</p>
                          <div className="flex flex-wrap gap-1 mt-2">
                            {car.features.map((feature, featureIndex) => (
                              <span key={featureIndex} className="px-2 py-1 bg-blue-100 text-blue-600 text-xs rounded">
                                {feature}
                              </span>
                            ))}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-xl font-bold text-blue-600">{car.price}</div>
                          <div className="text-sm text-gray-500">起</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Pricing Details */}
              <div className="mb-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-6">收费标准</h3>
                <div className="space-y-4">
                  {pricingDetails.map((pricing, index) => (
                    <div key={index} className="bg-gray-50 rounded-lg p-4">
                      <div className="flex justify-between items-center mb-2">
                        <h4 className="font-semibold text-gray-900">{pricing.service}</h4>
                        <span className="text-blue-600 font-bold">{pricing.price}</span>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        {pricing.includes.map((item, itemIndex) => (
                          <div key={itemIndex} className="flex items-center space-x-1">
                            <CheckCircle size={12} className="text-emerald-500" />
                            <span className="text-sm text-gray-600">{item}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* FAQ */}
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">常见问题</h3>
                <div className="space-y-4">
                  <div className="border rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 mb-2">航班延误怎么办？</h4>
                    <p className="text-gray-600">我们会实时追踪航班动态，免费等候30分钟，超时按实际情况收费。</p>
                  </div>
                  <div className="border rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 mb-2">夜间有附加费吗？</h4>
                    <p className="text-gray-600">晚上10点至早上6点期间，会收取20%的夜间服务费。</p>
                  </div>
                  <div className="border rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 mb-2">如何找到司机？</h4>
                    <p className="text-gray-600">司机会在指定地点举牌等候，牌子上会有您的姓名和我们的标识。</p>
                  </div>
                  <div className="border rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 mb-2">可以临时更改目的地吗？</h4>
                    <p className="text-gray-600">可以，但需要提前通知司机，可能会产生额外费用。</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default AirportTransfer;