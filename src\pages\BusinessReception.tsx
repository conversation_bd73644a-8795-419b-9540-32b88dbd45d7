import React, { useState } from 'react';
import { Building, Shield, Clock, Star, Car, Users } from 'lucide-react';

const BusinessReception = () => {
  const [formData, setFormData] = useState({
    companyName: '',
    contactPerson: '',
    contactPhone: '',
    contactEmail: '',
    serviceType: '',
    eventDate: '',
    guestCount: '',
    requirements: '',
    budget: ''
  });

  const services = [
    {
      title: 'VIP机场接送',
      description: '为您的重要客户提供专业的机场接送服务',
      icon: '✈️',
      features: ['豪华车队', '专业司机', '英文服务', '24小时服务']
    },
    {
      title: '会展会议交通统筹',
      description: '大型会议和展览活动的交通统筹服务',
      icon: '🏢',
      features: ['车队调度', '时间协调', '路线规划', '现场管理']
    },
    {
      title: '企业高管用车',
      description: '为企业高管提供专属的用车服务',
      icon: '👔',
      features: ['固定司机', '保密协议', '灵活调度', '高端车型']
    },
    {
      title: '跨国高层接待',
      description: '为国际客户提供全方位的接待服务',
      icon: '🌍',
      features: ['多语言服务', '文化礼仪', '行程安排', '贵宾待遇']
    }
  ];

  const luxuryCars = [
    {
      name: '奔驰 S-Class',
      type: '豪华轿车',
      seats: '4座',
      price: '¥800/天',
      image: 'https://images.pexels.com/photos/170811/pexels-photo-170811.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop',
      features: ['真皮座椅', '按摩功能', '后排娱乐系统', '隔音玻璃']
    },
    {
      name: '宝马 7系',
      type: '豪华轿车',
      seats: '4座',
      price: '¥750/天',
      image: 'https://images.pexels.com/photos/244206/pexels-photo-244206.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop',
      features: ['舒适座椅', '商务办公', '高级音响', '氛围灯']
    },
    {
      name: '奔驰 V-Class',
      type: '豪华商务车',
      seats: '7座',
      price: '¥900/天',
      image: 'https://images.pexels.com/photos/1719647/pexels-photo-1719647.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop',
      features: ['宽敞空间', '会议桌', '冰箱', '独立空调']
    },
    {
      name: '雷克萨斯 LM',
      type: '豪华MPV',
      seats: '4座',
      price: '¥1200/天',
      image: 'https://images.pexels.com/photos/1719647/pexels-photo-1719647.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop',
      features: ['头等舱座椅', '私密空间', '尊贵服务', '顶级配置']
    }
  ];

  const commitments = [
    {
      icon: Clock,
      title: '守时',
      description: '严格按照约定时间，绝不迟到，确保您的商务活动顺利进行'
    },
    {
      icon: Shield,
      title: '保密',
      description: '严格遵守保密协议，保护您的商业机密和隐私信息'
    },
    {
      icon: Star,
      title: '尊贵',
      description: '提供五星级的贵宾服务，彰显您的企业形象和地位'
    },
    {
      icon: Users,
      title: '灵活',
      description: '灵活调整服务方案，满足您的各种特殊需求'
    }
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    alert('咨询提交成功！我们的商务接待专员会在2小时内与您联系。');
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative h-96 flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-gray-900 to-blue-900 opacity-95"></div>
        <div 
          className="absolute inset-0 bg-cover bg-center"
          style={{
            backgroundImage: 'url(https://images.pexels.com/photos/2026324/pexels-photo-2026324.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop)'
          }}
        ></div>
        <div className="relative z-10 text-center text-white px-4">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            商务接待服务
          </h1>
          <p className="text-xl md:text-2xl opacity-90">
            专业、可靠、尊贵的商务用车解决方案
          </p>
        </div>
      </section>

      {/* Service Commitments */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              我们的服务承诺
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              以专业的态度和贴心的服务，为您的商务活动保驾护航
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {commitments.map((commitment, index) => {
              const Icon = commitment.icon;
              return (
                <div key={index} className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-gray-600 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Icon size={28} className="text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">
                    {commitment.title}
                  </h3>
                  <p className="text-gray-600">
                    {commitment.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Services */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              专业商务服务
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              为不同类型的商务需求提供定制化解决方案
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {services.map((service, index) => (
              <div key={index} className="bg-white rounded-xl shadow-lg border hover:shadow-xl transition-shadow p-8">
                <div className="flex items-start space-x-4">
                  <div className="text-4xl">{service.icon}</div>
                  <div className="flex-1">
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{service.title}</h3>
                    <p className="text-gray-600 mb-4">{service.description}</p>
                    <div className="space-y-2">
                      {service.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-center space-x-2">
                          <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                          <span className="text-sm text-gray-600">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Luxury Fleet */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              豪华车队
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              精选豪华品牌车型，为您的商务活动增添光彩
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {luxuryCars.map((car, index) => (
              <div key={index} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                <img 
                  src={car.image} 
                  alt={car.name}
                  className="w-full h-48 object-cover"
                />
                <div className="p-6">
                  <h3 className="text-lg font-bold text-gray-900 mb-1">{car.name}</h3>
                  <p className="text-sm text-gray-600 mb-2">{car.type} • {car.seats}</p>
                  <p className="text-xl font-bold text-blue-600 mb-4">{car.price}</p>
                  <div className="space-y-1">
                    {car.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-emerald-500 rounded-full"></div>
                        <span className="text-xs text-gray-600">{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              企业客户咨询
            </h2>
            <p className="text-xl text-gray-600">
              告诉我们您的需求，我们为您量身定制专业的商务接待方案
            </p>
          </div>

          <form onSubmit={handleSubmit} className="bg-white rounded-xl shadow-lg p-8 border">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  公司名称 *
                </label>
                <input
                  type="text"
                  name="companyName"
                  value={formData.companyName}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  联系人 *
                </label>
                <input
                  type="text"
                  name="contactPerson"
                  value={formData.contactPerson}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  联系电话 *
                </label>
                <input
                  type="tel"
                  name="contactPhone"
                  value={formData.contactPhone}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  邮箱地址 *
                </label>
                <input
                  type="email"
                  name="contactEmail"
                  value={formData.contactEmail}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  服务类型 *
                </label>
                <select
                  name="serviceType"
                  value={formData.serviceType}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                >
                  <option value="">请选择服务类型</option>
                  <option value="vip-transfer">VIP机场接送</option>
                  <option value="conference">会展会议交通</option>
                  <option value="executive">企业高管用车</option>
                  <option value="international">跨国高层接待</option>
                  <option value="custom">定制服务</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  预计服务日期
                </label>
                <input
                  type="date"
                  name="eventDate"
                  value={formData.eventDate}
                  onChange={handleInputChange}
                  min={new Date().toISOString().split('T')[0]}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  预计宾客人数
                </label>
                <input
                  type="number"
                  name="guestCount"
                  value={formData.guestCount}
                  onChange={handleInputChange}
                  min="1"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  预算范围
                </label>
                <select
                  name="budget"
                  value={formData.budget}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">请选择预算范围</option>
                  <option value="5000-10000">¥5,000 - ¥10,000</option>
                  <option value="10000-20000">¥10,000 - ¥20,000</option>
                  <option value="20000-50000">¥20,000 - ¥50,000</option>
                  <option value="50000+">¥50,000+</option>
                </select>
              </div>
            </div>

            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                具体需求描述
              </label>
              <textarea
                name="requirements"
                value={formData.requirements}
                onChange={handleInputChange}
                rows="5"
                placeholder="请详细描述您的商务接待需求，包括具体的服务要求、特殊需求等..."
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              ></textarea>
            </div>

            <button
              type="submit"
              className="w-full bg-gradient-to-r from-gray-600 to-blue-600 text-white py-4 rounded-lg font-semibold text-lg hover:from-gray-700 hover:to-blue-700 transition-all duration-300"
            >
              提交咨询需求
            </button>

            <p className="text-sm text-gray-500 mt-4 text-center">
              提交后，我们的商务接待专员会在2小时内与您联系，为您提供专业的解决方案
            </p>
          </form>
        </div>
      </section>
    </div>
  );
};

export default BusinessReception;