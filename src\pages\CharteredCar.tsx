import React, { useState } from 'react';
import { MapPin, Clock, Users, Star, Car, Route, Camera, Utensils } from 'lucide-react';

const CharteredCar = () => {
  const [selectedDuration, setSelectedDuration] = useState('half-day');
  const [formData, setFormData] = useState({
    date: '',
    duration: 'half-day',
    passengers: '1',
    destinations: [],
    specialRequests: '',
    contactName: '',
    contactPhone: '',
    contactEmail: ''
  });

  const popularRoutes = [
    {
      title: '瓜雪历史自然之旅',
      duration: '全天 (8小时)',
      distance: '120公里',
      highlights: ['天空之镜', '萤火虫观赏', '喂食老鹰', '红树林探索', '皇家山'],
      description: '探索瓜拉雪兰莪的自然奇观和历史文化',
      image: 'https://images.pexels.com/photos/1680247/pexels-photo-1680247.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop',
      estimatedCost: '¥680'
    },
    {
      title: '吉隆坡城市精华游',
      duration: '全天 (8小时)',
      distance: '80公里',
      highlights: ['双子塔', '国家清真寺', '茨厂街', '阿罗街', '独立广场'],
      description: '一日游遍吉隆坡最具代表性的地标建筑',
      image: 'https://images.pexels.com/photos/3271703/pexels-photo-3271703.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop',
      estimatedCost: '¥680'
    },
    {
      title: '马六甲古城探索',
      duration: '全天 (8小时)',
      distance: '200公里',
      highlights: ['红屋', '圣保罗教堂', '鸡场街', '马六甲河', '荷兰广场'],
      description: '穿越时空，感受马六甲的历史韵味',
      image: 'https://images.pexels.com/photos/2880507/pexels-photo-2880507.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop',
      estimatedCost: '¥880'
    },
    {
      title: '云顶高原休闲游',
      duration: '全天 (8小时)',
      distance: '100公里',
      highlights: ['云顶娱乐城', '天空缆车', '草莓园', '清水岩庙'],
      description: '登上云顶，享受清凉气候和娱乐设施',
      image: 'https://images.pexels.com/photos/1271619/pexels-photo-1271619.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop',
      estimatedCost: '¥780'
    }
  ];

  const pricingTable = [
    { 
      type: '舒适5座轿车', 
      halfDay: '¥380', 
      fullDay: '¥680', 
      overtime: '¥50/小时',
      features: ['空调', 'GPS', 'WiFi', '充电器']
    },
    { 
      type: '豪华7座MPV', 
      halfDay: '¥480', 
      fullDay: '¥880', 
      overtime: '¥60/小时',
      features: ['真皮座椅', '娱乐系统', '大空间', '舒适座椅']
    },
    { 
      type: '商务9座面包车', 
      halfDay: '¥580', 
      fullDay: '¥1080', 
      overtime: '¥80/小时',
      features: ['商务座椅', '大行李空间', '团体出行', '会议桌']
    }
  ];

  const whyChooseUs = [
    {
      icon: Star,
      title: '司机兼导游',
      description: '我们的司机不仅熟悉路况，还是专业的导游，为您介绍当地文化和历史'
    },
    {
      icon: Car,
      title: '车辆舒适整洁',
      description: '所有车辆定期保养，内部清洁，配备空调和舒适座椅'
    },
    {
      icon: Users,
      title: '100%定制自由',
      description: '完全根据您的需求定制行程，想去哪里就去哪里'
    },
    {
      icon: Clock,
      title: '灵活时间安排',
      description: '您可以自由控制每个景点的停留时间，无需赶时间'
    }
  ];

  const driverProfiles = [
    {
      name: '阿明',
      experience: '8年',
      languages: ['中文', '英文', '马来文'],
      specialties: ['历史文化', '摄影指导'],
      rating: 4.9,
      description: '熟悉马来西亚历史文化，擅长为游客拍照'
    },
    {
      name: '小李',
      experience: '6年',
      languages: ['中文', '英文'],
      specialties: ['自然景观', '美食推荐'],
      rating: 4.8,
      description: '对当地美食了如指掌，是您的美食向导'
    },
    {
      name: '大华',
      experience: '10年',
      languages: ['中文', '英文', '马来文', '泰米尔文'],
      specialties: ['商务接待', '多语言服务'],
      rating: 5.0,
      description: '经验丰富的商务司机，服务过众多企业客户'
    }
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleDestinationChange = (destination) => {
    setFormData(prev => ({
      ...prev,
      destinations: prev.destinations.includes(destination)
        ? prev.destinations.filter(d => d !== destination)
        : [...prev.destinations, destination]
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    alert('定制咨询提交成功！我们的客服会在24小时内联系您。');
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative h-[70vh] min-h-[500px] max-h-[600px] flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-emerald-600 opacity-90"></div>
        <div 
          className="absolute inset-0 bg-cover bg-center"
          style={{
            backgroundImage: 'url(https://images.pexels.com/photos/1271619/pexels-photo-1271619.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop)'
          }}
        ></div>
        <div className="relative z-10 text-center text-white px-4">
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-3 md:mb-4">
            全天/半天包车
          </h1>
          <p className="text-lg md:text-xl lg:text-2xl opacity-90">
            专业司机带您发现地道之美
          </p>
        </div>
      </section>

      {/* Popular Routes */}
      <section className="py-12 md:py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-10 md:mb-12">
            <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              热门推荐行程
            </h2>
            <p className="text-lg md:text-xl text-gray-600 max-w-2xl mx-auto">
              精选经典路线，带您深度体验马来西亚的魅力
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8">
            {popularRoutes.map((route, index) => (
              <div key={index} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                <img 
                  src={route.image} 
                  alt={route.title}
                  className="w-full h-48 object-cover"
                />
                <div className="p-6">
                  <div className="flex justify-between items-start mb-3">
                    <h3 className="text-xl font-bold text-gray-900">{route.title}</h3>
                    <span className="text-lg font-bold text-blue-600">{route.estimatedCost}</span>
                  </div>
                  <div className="flex items-center space-x-4 mb-3 text-sm text-gray-600">
                    <div className="flex items-center space-x-1">
                      <Clock size={14} />
                      <span>{route.duration}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Route size={14} />
                      <span>{route.distance}</span>
                    </div>
                  </div>
                  <p className="text-gray-600 mb-4">{route.description}</p>
                  <div className="space-y-2">
                    <h4 className="font-semibold text-gray-900">主要景点</h4>
                    <div className="flex flex-wrap gap-2">
                      {route.highlights.map((highlight, highlightIndex) => (
                        <span 
                          key={highlightIndex}
                          className="px-2 py-1 bg-emerald-100 text-emerald-600 text-sm rounded-full"
                        >
                          {highlight}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Driver Profiles */}
      <section className="py-12 md:py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-10 md:mb-12">
            <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              专业司机团队
            </h2>
            <p className="text-lg md:text-xl text-gray-600 max-w-2xl mx-auto">
              经验丰富的司机兼导游，为您的旅程增添更多精彩
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8">
            {driverProfiles.map((driver, index) => (
              <div key={index} className="bg-white border rounded-xl p-6 hover:shadow-lg transition-shadow">
                <div className="text-center mb-4">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-white font-bold text-xl">{driver.name[0]}</span>
                  </div>
                  <h3 className="text-lg font-bold text-gray-900">{driver.name}</h3>
                  <div className="flex items-center justify-center space-x-1 mt-1">
                    <Star size={16} className="text-yellow-400 fill-current" />
                    <span className="text-sm text-gray-600">{driver.rating}</span>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <div>
                    <span className="text-sm font-medium text-gray-700">经验：</span>
                    <span className="text-sm text-gray-600">{driver.experience}</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-700">语言：</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {driver.languages.map((lang, langIndex) => (
                        <span key={langIndex} className="px-2 py-1 bg-blue-100 text-blue-600 text-xs rounded">
                          {lang}
                        </span>
                      ))}
                    </div>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-700">专长：</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {driver.specialties.map((specialty, specialtyIndex) => (
                        <span key={specialtyIndex} className="px-2 py-1 bg-emerald-100 text-emerald-600 text-xs rounded">
                          {specialty}
                        </span>
                      ))}
                    </div>
                  </div>
                  <p className="text-sm text-gray-600">{driver.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="py-12 md:py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-10 md:mb-12">
            <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              为何选择我们
            </h2>
            <p className="text-lg md:text-xl text-gray-600 max-w-2xl mx-auto">
              专业、舒适、自由的包车体验
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8">
            {whyChooseUs.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <div key={index} className="text-center">
                  <div className="w-14 h-14 md:w-16 md:h-16 bg-gradient-to-r from-blue-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Icon size={24} className="text-white md:w-7 md:h-7" />
                  </div>
                  <h3 className="text-base md:text-lg font-semibold text-gray-900 mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 text-sm md:text-base">
                    {feature.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Pricing Table */}
      <section className="py-12 md:py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-10 md:mb-12">
            <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              透明定价
            </h2>
            <p className="text-lg md:text-xl text-gray-600 max-w-2xl mx-auto">
              明码标价，无隐藏费用
            </p>
          </div>

          <div className="bg-white rounded-xl shadow-lg overflow-hidden border">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">车型</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">半天 (4小时)</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">全天 (8小时)</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">超时费用</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">车辆特色</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {pricingTable.map((row, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 text-sm font-medium text-gray-900">{row.type}</td>
                      <td className="px-6 py-4 text-sm text-blue-600 font-semibold">{row.halfDay}</td>
                      <td className="px-6 py-4 text-sm text-blue-600 font-semibold">{row.fullDay}</td>
                      <td className="px-6 py-4 text-sm text-gray-600">{row.overtime}</td>
                      <td className="px-6 py-4">
                        <div className="flex flex-wrap gap-1">
                          {row.features.map((feature, featureIndex) => (
                            <span key={featureIndex} className="px-2 py-1 bg-blue-100 text-blue-600 text-xs rounded">
                              {feature}
                            </span>
                          ))}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600">
              * 价格包含司机服务费、燃油费、过路费。不包含停车费、司机餐费（全天包车包含）
            </p>
          </div>
        </div>
      </section>

      {/* Booking Form */}
      <section className="py-12 md:py-16 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-10 md:mb-12">
            <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              定制您的行程
            </h2>
            <p className="text-lg md:text-xl text-gray-600">
              告诉我们您的需求，我们为您量身定制专属行程
            </p>
          </div>

          <form onSubmit={handleSubmit} className="bg-white rounded-xl shadow-lg p-6 md:p-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  包车日期
                </label>
                <input
                  type="date"
                  name="date"
                  value={formData.date}
                  onChange={handleInputChange}
                  min={new Date().toISOString().split('T')[0]}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  包车时长
                </label>
                <select
                  name="duration"
                  value={formData.duration}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="half-day">半天 (4小时)</option>
                  <option value="full-day">全天 (8小时)</option>
                  <option value="custom">其他时长</option>
                </select>
              </div>
            </div>

            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                人数
              </label>
              <select
                name="passengers"
                value={formData.passengers}
                onChange={handleInputChange}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {[1, 2, 3, 4, 5, 6, 7, 8, 9].map(num => (
                  <option key={num} value={num}>{num} 人</option>
                ))}
              </select>
            </div>

            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-3">
                想去的地点 (可多选)
              </label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {[
                  '天空之镜', '萤火虫观赏', '喂食老鹰', '红树林',
                  '双子塔', '国家清真寺', '茨厂街', '阿罗街',
                  '马六甲', '云顶高原', '黑风洞', '国家动物园'
                ].map(destination => (
                  <label key={destination} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={formData.destinations.includes(destination)}
                      onChange={() => handleDestinationChange(destination)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">{destination}</span>
                  </label>
                ))}
              </div>
            </div>

            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                特殊需求或其他地点
              </label>
              <textarea
                name="specialRequests"
                value={formData.specialRequests}
                onChange={handleInputChange}
                rows="4"
                placeholder="请详细描述您的需求、其他想去的地点或特殊要求..."
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              ></textarea>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  联系人姓名
                </label>
                <input
                  type="text"
                  name="contactName"
                  value={formData.contactName}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  联系电话
                </label>
                <input
                  type="tel"
                  name="contactPhone"
                  value={formData.contactPhone}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  邮箱地址
                </label>
                <input
                  type="email"
                  name="contactEmail"
                  value={formData.contactEmail}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>
            </div>

            <button
              type="submit"
              className="w-full bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
            >
              提交定制需求
            </button>

            <p className="text-sm text-gray-500 mt-4 text-center">
              提交后，我们的客服团队会在24小时内联系您，为您量身定制专属行程
            </p>
          </form>
        </div>
      </section>
    </div>
  );
};

export default CharteredCar;