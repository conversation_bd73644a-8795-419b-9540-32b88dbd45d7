import React, { useState } from 'react';
import { Phone, Mail, MapPin, Clock, MessageCircle, Send } from 'lucide-react';

const ContactUs = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });

  const contactInfo = [
    {
      icon: Phone,
      title: '电话咨询',
      details: ['+60 ************', '+60 ************'],
      description: '24小时服务热线',
      color: 'bg-blue-500'
    },
    {
      icon: MessageCircle,
      title: 'WhatsApp',
      details: ['+60 ************'],
      description: '快速响应，实时沟通',
      color: 'bg-green-500'
    },
    {
      icon: Mail,
      title: '邮箱联系',
      details: ['<EMAIL>', '<EMAIL>'],
      description: '24小时内回复',
      color: 'bg-purple-500'
    },
    {
      icon: MapPin,
      title: '公司地址',
      details: ['瓜拉雪兰莪天空之镜码头', 'Sky Mirror Pier, Kuala Selangor'],
      description: '欢迎来访',
      color: 'bg-red-500'
    }
  ];

  const workingHours = [
    { day: '周一至周五', hours: '8:00 - 18:00' },
    { day: '周六', hours: '8:00 - 17:00' },
    { day: '周日', hours: '8:00 - 16:00' },
    { day: '节假日', hours: '9:00 - 17:00' }
  ];

  const socialMedia = [
    { platform: 'Facebook', handle: '@SkyMirrorWorldTour', color: 'bg-blue-600' },
    { platform: 'Instagram', handle: '@skymirror_tour', color: 'bg-pink-500' },
    { platform: 'Twitter', handle: '@SkyMirrorTour', color: 'bg-blue-400' },
    { platform: '微信', handle: 'SkyMirrorTour', color: 'bg-green-600' }
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    alert('感谢您的留言！我们会在24小时内回复您。');
    setFormData({
      name: '',
      email: '',
      phone: '',
      subject: '',
      message: ''
    });
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative h-96 flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-emerald-600 opacity-90"></div>
        <div 
          className="absolute inset-0 bg-cover bg-center"
          style={{
            backgroundImage: 'url(https://images.pexels.com/photos/1647976/pexels-photo-1647976.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop)'
          }}
        ></div>
        <div className="relative z-10 text-center text-white px-4">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            联系我们
          </h1>
          <p className="text-xl md:text-2xl opacity-90">
            我们随时为您提供专业的旅游咨询服务
          </p>
        </div>
      </section>

      {/* Contact Information */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              多种联系方式
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              选择最适合您的联系方式，我们将竭诚为您服务
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {contactInfo.map((info, index) => {
              const Icon = info.icon;
              return (
                <div key={index} className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow">
                  <div className={`w-12 h-12 ${info.color} rounded-lg flex items-center justify-center mb-4`}>
                    <Icon size={24} className="text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{info.title}</h3>
                  <div className="space-y-1 mb-3">
                    {info.details.map((detail, detailIndex) => (
                      <p key={detailIndex} className="text-gray-800 font-medium">{detail}</p>
                    ))}
                  </div>
                  <p className="text-gray-600 text-sm">{info.description}</p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Contact Form and Details */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                发送消息
              </h2>
              <p className="text-gray-600 mb-8">
                有任何问题或建议，请随时与我们联系。我们会在24小时内回复您。
              </p>

              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      您的姓名 *
                    </label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      联系电话 *
                    </label>
                    <input
                      type="tel"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    邮箱地址 *
                  </label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    主题 *
                  </label>
                  <select
                    name="subject"
                    value={formData.subject}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  >
                    <option value="">请选择咨询主题</option>
                    <option value="booking">预订咨询</option>
                    <option value="services">服务咨询</option>
                    <option value="complaint">投诉建议</option>
                    <option value="partnership">合作洽谈</option>
                    <option value="other">其他</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    详细信息 *
                  </label>
                  <textarea
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    rows="6"
                    placeholder="请详细描述您的需求或问题..."
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  ></textarea>
                </div>

                <button
                  type="submit"
                  className="w-full bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2"
                >
                  <Send size={20} />
                  <span>发送消息</span>
                </button>
              </form>
            </div>

            {/* Additional Information */}
            <div className="space-y-8">
              {/* Working Hours */}
              <div className="bg-gray-50 rounded-xl p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
                  <Clock size={24} className="mr-2 text-blue-600" />
                  工作时间
                </h3>
                <div className="space-y-3">
                  {workingHours.map((schedule, index) => (
                    <div key={index} className="flex justify-between items-center">
                      <span className="text-gray-700">{schedule.day}</span>
                      <span className="font-semibold text-gray-900">{schedule.hours}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Social Media */}
              <div className="bg-gray-50 rounded-xl p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-4">
                  社交媒体
                </h3>
                <div className="space-y-3">
                  {socialMedia.map((social, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <div className={`w-8 h-8 ${social.color} rounded-full flex items-center justify-center`}>
                        <span className="text-white text-sm font-bold">
                          {social.platform[0]}
                        </span>
                      </div>
                      <div>
                        <span className="font-semibold text-gray-900">{social.platform}</span>
                        <span className="text-gray-600 ml-2">{social.handle}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Emergency Contact */}
              <div className="bg-red-50 rounded-xl p-6 border border-red-200">
                <h3 className="text-xl font-bold text-red-900 mb-4">
                  紧急联系
                </h3>
                <p className="text-red-800 mb-3">
                  如果您在旅行中遇到紧急情况，请立即联系我们：
                </p>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Phone size={16} className="text-red-600" />
                    <span className="font-semibold text-red-900">+60 ************</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <MessageCircle size={16} className="text-red-600" />
                    <span className="font-semibold text-red-900">WhatsApp 24/7</span>
                  </div>
                </div>
              </div>

              {/* FAQ Link */}
              <div className="bg-blue-50 rounded-xl p-6 border border-blue-200">
                <h3 className="text-xl font-bold text-blue-900 mb-4">
                  常见问题
                </h3>
                <p className="text-blue-800 mb-3">
                  在联系我们之前，您可以查看常见问题解答，或许能找到您需要的答案。
                </p>
                <button className="text-blue-600 font-semibold hover:text-blue-800 transition-colors">
                  查看常见问题 →
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Map Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              找到我们
            </h2>
            <p className="text-xl text-gray-600">
              我们位于瓜拉雪兰莪天空之镜码头，交通便利，欢迎您来访
            </p>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="aspect-video bg-gray-200 rounded-lg flex items-center justify-center mb-6">
              <div className="text-center">
                <MapPin size={48} className="text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">
                  Google Maps 导航<br />
                  搜索："Sky Mirror World Tour"
                </p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <h3 className="font-semibold text-gray-900 mb-2">详细地址</h3>
                <p className="text-gray-600">
                  瓜拉雪兰莪天空之镜码头<br />
                  Sky Mirror Pier, Kuala Selangor<br />
                  马来西亚
                </p>
              </div>
              <div className="text-center">
                <h3 className="font-semibold text-gray-900 mb-2">GPS 坐标</h3>
                <p className="text-gray-600">
                  纬度: 3.3442° N<br />
                  经度: 101.2425° E
                </p>
              </div>
              <div className="text-center">
                <h3 className="font-semibold text-gray-900 mb-2">交通指引</h3>
                <p className="text-gray-600">
                  从吉隆坡出发约1小时车程<br />
                  提供免费停车场
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default ContactUs;