import React from 'react';
import { Link } from 'react-router-dom';
import { Car, Clock, Users, Shield, Star, ArrowRight } from 'lucide-react';

const DomesticTransport = () => {
  const services = [
    {
      title: '机场接送',
      subtitle: '准时可靠',
      description: '告别等待，从机场到酒店的无缝衔接',
      icon: '✈️',
      path: '/airport-transfer',
      features: ['24小时服务', '航班动态追踪', '免费等候30分钟']
    },
    {
      title: '半天包车',
      subtitle: '灵活高效',
      description: '4小时专属服务，满足您的短途旅行或商务需求',
      icon: '🚗',
      path: '/chartered-car',
      features: ['4小时服务', '自由选择路线', '专业司机导游']
    },
    {
      title: '全天包车',
      subtitle: '深度探索',
      description: '8小时随心畅游，专业司机带您发现地道之美',
      icon: '🌟',
      path: '/chartered-car',
      features: ['8小时服务', '包含司机餐费', '个性化行程定制']
    }
  ];

  const advantages = [
    {
      icon: Shield,
      title: '专业持证司机',
      description: '所有司机均持有专业执照，经验丰富，熟悉当地路况'
    },
    {
      icon: Car,
      title: '车队阵容展示',
      description: '从舒适轿车到豪华MPV，多种车型满足不同需求'
    },
    {
      icon: Users,
      title: '行程高度定制',
      description: '根据您的具体需求，为您量身定制专属行程'
    },
    {
      icon: Star,
      title: '明码标价',
      description: '价格透明公开，无隐藏费用，让您消费更放心'
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section - 优化高度 */}
      <section className="relative h-[70vh] min-h-[500px] max-h-[600px] flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-emerald-600 opacity-90"></div>
        <div 
          className="absolute inset-0 bg-cover bg-center"
          style={{
            backgroundImage: 'url(https://images.pexels.com/photos/116675/pexels-photo-116675.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop)'
          }}
        ></div>
        <div className="relative z-10 text-center text-white px-4">
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-3 md:mb-4">
            您的专属司机
          </h1>
          <p className="text-lg md:text-xl lg:text-2xl opacity-90">
            探索马来西亚的自由与舒适
          </p>
        </div>
      </section>

      {/* Services Section - 优化间距和布局 */}
      <section className="py-12 md:py-16">
        <div className="max-w-8xl mx-auto px-3 sm:px-4 lg:px-6">
          <div className="text-center mb-10 md:mb-12">
            <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-3 md:mb-4">
              选择您的出行方式
            </h2>
            <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              无论是机场接送、半天包车还是全天包车，我们都为您提供专业、舒适的服务
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
            {services.map((service, index) => (
              <div key={index} className="bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border hover:border-blue-200 group h-full">
                <div className="p-5 md:p-6 h-full flex flex-col">
                  <div className="text-4xl md:text-5xl mb-3 md:mb-4 text-center">{service.icon}</div>
                  <h3 className="text-xl md:text-2xl font-bold text-gray-900 mb-2 text-center">
                    {service.title}
                  </h3>
                  <p className="text-base md:text-lg text-blue-600 font-semibold mb-3 md:mb-4 text-center">
                    {service.subtitle}
                  </p>
                  <p className="text-gray-600 mb-4 md:mb-6 text-center flex-grow leading-relaxed">
                    {service.description}
                  </p>
                  
                  <div className="space-y-2 mb-4 md:mb-6">
                    {service.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-emerald-500 rounded-full flex-shrink-0"></div>
                        <span className="text-sm text-gray-600">{feature}</span>
                      </div>
                    ))}
                  </div>

                  <Link
                    to={service.path}
                    className="w-full bg-blue-600 text-white py-2.5 md:py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2 group-hover:scale-105 transform duration-300"
                  >
                    <span>查看详情与预订</span>
                    <ArrowRight size={18} />
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Advantages Section - 紧凑布局 */}
      <section className="py-12 md:py-16 bg-gray-50">
        <div className="max-w-8xl mx-auto px-3 sm:px-4 lg:px-6">
          <div className="text-center mb-10 md:mb-12">
            <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-3 md:mb-4">
              我们的优势
            </h2>
            <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              选择我们，享受专业、安全、舒适的出行体验
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8">
            {advantages.map((advantage, index) => {
              const Icon = advantage.icon;
              return (
                <div key={index} className="text-center group">
                  <div className="w-14 h-14 md:w-16 md:h-16 bg-gradient-to-r from-blue-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-3 md:mb-4 group-hover:scale-110 transition-transform">
                    <Icon size={24} className="text-white md:w-7 md:h-7" />
                  </div>
                  <h3 className="text-base md:text-lg font-semibold text-gray-900 mb-2">
                    {advantage.title}
                  </h3>
                  <p className="text-gray-600 text-sm md:text-base leading-relaxed">
                    {advantage.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* CTA Section - 优化高度 */}
      <section className="py-12 md:py-16 bg-gradient-to-r from-blue-600 to-emerald-600">
        <div className="max-w-6xl mx-auto px-3 sm:px-4 lg:px-6 text-center">
          <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-white mb-3 md:mb-4">
            准备好开始您的旅程了吗？
          </h2>
          <p className="text-lg md:text-xl text-white mb-6 md:mb-8 opacity-90 max-w-2xl mx-auto leading-relaxed">
            立即预订我们的专车服务，享受舒适便捷的出行体验
          </p>
          <div className="flex flex-col sm:flex-row gap-3 md:gap-4 justify-center">
            <Link
              to="/contact"
              className="bg-white text-blue-600 px-6 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors inline-flex items-center justify-center space-x-2 shadow-lg"
            >
              <span>立即咨询</span>
              <ArrowRight size={20} />
            </Link>
            <Link
              to="/airport-transfer"
              className="border-2 border-white text-white px-6 py-3 rounded-full font-semibold hover:bg-white hover:text-blue-600 transition-colors"
            >
              预订接送
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default DomesticTransport;