import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Car, Ticket, Globe, Building, Waves, Coffee, CheckCircle, Clock, Users, Shield } from 'lucide-react';

const Home = () => {
  const services = [
    {
      title: '境内用车',
      description: '专业司机，舒适车辆，为您的出行保驾护航',
      icon: Car,
      path: '/domestic-transport',
      color: 'bg-blue-500'
    },
    {
      title: '特色船票',
      description: '天空之镜、喂食老鹰等精选体验',
      icon: Ticket,
      path: '/signature-tickets',
      color: 'bg-emerald-500'
    },
    {
      title: '国际旅游',
      description: '即将开启的世界之旅，敬请期待',
      icon: Globe,
      path: '/international-tours',
      color: 'bg-purple-500'
    },
    {
      title: '商务接待',
      description: '高端商务服务，彰显您的品味',
      icon: Building,
      path: '/business-reception',
      color: 'bg-gray-600'
    },
    {
      title: '海岛旅游',
      description: '热浪岛、刁曼岛等绝美海岛套餐',
      icon: Waves,
      path: '/island-hopping',
      color: 'bg-cyan-500'
    },
    {
      title: '天空码头咖啡厅',
      description: '一艘老船的新生，旅行者的休憩站',
      icon: Coffee,
      path: '/sky-pier-cafe',
      color: 'bg-amber-500'
    }
  ];

  const features = [
    {
      icon: CheckCircle,
      title: '专业持证司机',
      description: '所有司机均持有专业执照，经验丰富'
    },
    {
      icon: Clock,
      title: '准时可靠',
      description: '严格按时服务，让您的行程无忧'
    },
    {
      icon: Users,
      title: '个性化服务',
      description: '根据您的需求量身定制专属行程'
    },
    {
      icon: Shield,
      title: '安全保障',
      description: '全面的保险覆盖，让您安心出行'
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section - 优化高度和内容密度 */}
      <section className="relative h-[85vh] min-h-[600px] max-h-[800px] flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-emerald-600 opacity-90"></div>
        <div 
          className="absolute inset-0 bg-cover bg-center"
          style={{
            backgroundImage: 'url(https://images.pexels.com/photos/1271619/pexels-photo-1271619.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop)'
          }}
        ></div>
        <div className="relative z-10 text-center text-white px-4 max-w-5xl mx-auto">
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-4 leading-tight">
            发现马来西亚的<br />
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-500">
              天空之镜
            </span>
          </h1>
          <p className="text-lg md:text-xl lg:text-2xl mb-6 max-w-3xl mx-auto opacity-90 leading-relaxed">
            您的专属旅行伙伴，为您打造难忘的马来西亚之旅
          </p>
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Link
              to="/signature-tickets"
              className="bg-white text-blue-600 px-6 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors inline-flex items-center justify-center space-x-2 shadow-lg"
            >
              <span>立即预订</span>
              <ArrowRight size={20} />
            </Link>
            <Link
              to="/about"
              className="border-2 border-white text-white px-6 py-3 rounded-full font-semibold hover:bg-white hover:text-blue-600 transition-colors"
            >
              了解更多
            </Link>
          </div>
        </div>
      </section>

      {/* Services Section - 优化间距和布局 */}
      <section className="py-12 md:py-16 bg-white">
        <div className="max-w-8xl mx-auto px-3 sm:px-4 lg:px-6">
          <div className="text-center mb-10 md:mb-12">
            <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-3">
              我们的服务
            </h2>
            <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              从境内用车到国际旅游，从商务接待到海岛度假，我们为您提供全方位的旅行服务
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
            {services.map((service, index) => {
              const Icon = service.icon;
              return (
                <Link
                  key={index}
                  to={service.path}
                  className="group bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border hover:border-blue-200 h-full"
                >
                  <div className="p-5 md:p-6 h-full flex flex-col">
                    <div className={`w-10 h-10 md:w-12 md:h-12 ${service.color} rounded-lg flex items-center justify-center mb-3 md:mb-4 group-hover:scale-110 transition-transform`}>
                      <Icon size={20} className="text-white md:w-6 md:h-6" />
                    </div>
                    <h3 className="text-lg md:text-xl font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                      {service.title}
                    </h3>
                    <p className="text-gray-600 mb-4 flex-grow text-sm md:text-base leading-relaxed">
                      {service.description}
                    </p>
                    <div className="flex items-center text-blue-600 group-hover:text-blue-700">
                      <span className="text-sm font-medium">了解更多</span>
                      <ArrowRight size={14} className="ml-1 group-hover:translate-x-1 transition-transform" />
                    </div>
                  </div>
                </Link>
              );
            })}
          </div>
        </div>
      </section>

      {/* Features Section - 紧凑布局 */}
      <section className="py-12 md:py-16 bg-gray-50">
        <div className="max-w-8xl mx-auto px-3 sm:px-4 lg:px-6">
          <div className="text-center mb-10 md:mb-12">
            <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-3">
              为什么选择我们
            </h2>
            <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              专业、可靠、贴心的服务，让您的每一次旅行都成为美好回忆
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <div key={index} className="text-center group">
                  <div className="w-14 h-14 md:w-16 md:h-16 bg-gradient-to-r from-blue-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-3 md:mb-4 group-hover:scale-110 transition-transform">
                    <Icon size={24} className="text-white md:w-7 md:h-7" />
                  </div>
                  <h3 className="text-base md:text-lg font-semibold text-gray-900 mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 text-sm md:text-base leading-relaxed">
                    {feature.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* CTA Section - 优化高度和内容布局 */}
      <section className="py-12 md:py-16 bg-gradient-to-r from-blue-600 to-emerald-600">
        <div className="max-w-6xl mx-auto px-3 sm:px-4 lg:px-6 text-center">
          <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-white mb-3 md:mb-4">
            准备开始您的旅程了吗？
          </h2>
          <p className="text-lg md:text-xl text-white mb-6 md:mb-8 opacity-90 max-w-2xl mx-auto leading-relaxed">
            立即联系我们，让我们为您规划一次完美的马来西亚之旅
          </p>
          <div className="flex flex-col sm:flex-row gap-3 md:gap-4 justify-center">
            <Link
              to="/contact"
              className="bg-white text-blue-600 px-6 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors inline-flex items-center justify-center space-x-2 shadow-lg"
            >
              <span>立即咨询</span>
              <ArrowRight size={20} />
            </Link>
            <Link
              to="/signature-tickets"
              className="border-2 border-white text-white px-6 py-3 rounded-full font-semibold hover:bg-white hover:text-blue-600 transition-colors"
            >
              预订体验
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;