import React, { useState } from 'react';
import { Globe, Mail, ArrowRight, MapPin } from 'lucide-react';

const InternationalTours = () => {
  const [selectedCountry, setSelectedCountry] = useState('');
  const [email, setEmail] = useState('');

  const destinations = [
    {
      country: '日本',
      flag: '🇯🇵',
      image: 'https://images.pexels.com/photos/161251/senso-ji-temple-asakusa-tokyo-japan-161251.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop',
      highlights: ['富士山', '东京塔', '京都古城', '大阪城'],
      description: '体验日本的传统文化与现代科技的完美融合'
    },
    {
      country: '韩国',
      flag: '🇰🇷',
      image: 'https://images.pexels.com/photos/9169449/pexels-photo-9169449.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop',
      highlights: ['景福宫', '明洞', '济州岛', '釜山'],
      description: '探索韩流文化的发源地，感受现代都市魅力'
    },
    {
      country: '泰国',
      flag: '🇹🇭',
      image: 'https://images.pexels.com/photos/1007373/pexels-photo-1007373.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop',
      highlights: ['大皇宫', '普吉岛', '清迈', '水上市场'],
      description: '享受热带风情，体验独特的泰式文化'
    },
    {
      country: '新加坡',
      flag: '🇸🇬',
      image: 'https://images.pexels.com/photos/1166209/pexels-photo-1166209.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop',
      highlights: ['滨海湾', '圣淘沙', '牛车水', '小印度'],
      description: '花园城市的现代化与多元文化体验'
    },
    {
      country: '印尼',
      flag: '🇮🇩',
      image: 'https://images.pexels.com/photos/2474691/pexels-photo-2474691.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop',
      highlights: ['巴厘岛', '雅加达', '日惹', '婆罗浮屠'],
      description: '千岛之国的自然美景与文化遗产'
    },
    {
      country: '越南',
      flag: '🇻🇳',
      image: 'https://images.pexels.com/photos/1658967/pexels-photo-1658967.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop',
      highlights: ['下龙湾', '河内', '胡志明市', '会安古城'],
      description: '感受越南的历史底蕴与自然风光'
    },
    {
      country: '菲律宾',
      flag: '🇵🇭',
      image: 'https://images.pexels.com/photos/1320684/pexels-photo-1320684.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop',
      highlights: ['巴拉望', '长滩岛', '马尼拉', '薄荷岛'],
      description: '七千个岛屿的热带天堂'
    },
    {
      country: '柬埔寨',
      flag: '🇰🇭',
      image: 'https://images.pexels.com/photos/1288126/pexels-photo-1288126.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop',
      highlights: ['吴哥窟', '金边', '暹粒', '西哈努克'],
      description: '探索高棉文明的神秘古迹'
    },
    {
      country: '老挝',
      flag: '🇱🇦',
      image: 'https://images.pexels.com/photos/1320684/pexels-photo-1320684.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop',
      highlights: ['万象', '琅勃拉邦', '万荣', '香通寺'],
      description: '宁静的佛教国度，保持着原始的自然风貌'
    },
    {
      country: '缅甸',
      flag: '🇲🇲',
      image: 'https://images.pexels.com/photos/1320684/pexels-photo-1320684.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop',
      highlights: ['仰光', '曼德勒', '蒲甘', '茵莱湖'],
      description: '金色的佛塔与丰富的文化遗产'
    },
    {
      country: '马尔代夫',
      flag: '🇲🇻',
      image: 'https://images.pexels.com/photos/1320684/pexels-photo-1320684.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop',
      highlights: ['马累', '水上别墅', '珊瑚礁', '潜水'],
      description: '印度洋上的珍珠，蜜月天堂'
    },
    {
      country: '斯里兰卡',
      flag: '🇱🇰',
      image: 'https://images.pexels.com/photos/1320684/pexels-photo-1320684.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop',
      highlights: ['科伦坡', '康提', '加勒', '锡吉里耶'],
      description: '印度洋上的明珠，茶叶王国'
    }
  ];

  const handleSubscribe = (e) => {
    e.preventDefault();
    if (!selectedCountry || !email) {
      alert('请选择国家并输入邮箱地址');
      return;
    }
    alert(`感谢您的订阅！我们会第一时间通知您${selectedCountry}旅游线路的上线信息。`);
    setEmail('');
    setSelectedCountry('');
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-blue-600 opacity-90"></div>
        <div 
          className="absolute inset-0 bg-cover bg-center"
          style={{
            backgroundImage: 'url(https://images.pexels.com/photos/346885/pexels-photo-346885.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop)'
          }}
        ></div>
        <div className="relative z-10 text-center text-white px-4 max-w-4xl">
          <div className="mb-8">
            <Globe size={80} className="mx-auto mb-6 text-white" />
          </div>
          <h1 className="text-5xl md:text-7xl font-bold mb-6">
            我们的足迹
          </h1>
          <h2 className="text-3xl md:text-5xl font-bold mb-6">
            即将遍布<span className="text-yellow-400">世界</span>
          </h2>
          <p className="text-xl md:text-2xl opacity-90 mb-8">
            Sky Mirror World Tour 正在扩展我们的服务版图<br />
            让我们带您探索世界的每一个角落
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button 
              onClick={() => document.getElementById('destinations').scrollIntoView({ behavior: 'smooth' })}
              className="bg-white text-purple-600 px-8 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors inline-flex items-center justify-center space-x-2"
            >
              <span>查看目的地</span>
              <ArrowRight size={20} />
            </button>
            <button 
              onClick={() => document.getElementById('subscribe').scrollIntoView({ behavior: 'smooth' })}
              className="border-2 border-white text-white px-8 py-3 rounded-full font-semibold hover:bg-white hover:text-purple-600 transition-colors"
            >
              订阅更新
            </button>
          </div>
        </div>
      </section>

      {/* Destinations Section */}
      <section id="destinations" className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              即将上线的目的地
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              我们正在精心准备这些令人向往的目的地，为您提供最优质的旅游体验
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {destinations.map((destination, index) => (
              <div 
                key={index} 
                className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 cursor-pointer"
                onClick={() => setSelectedCountry(destination.country)}
              >
                <div className="relative">
                  <img 
                    src={destination.image} 
                    alt={destination.country}
                    className="w-full h-48 object-cover"
                  />
                  <div className="absolute top-4 right-4 bg-white rounded-full p-2">
                    <span className="text-2xl">{destination.flag}</span>
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                  <div className="absolute bottom-4 left-4 text-white">
                    <h3 className="text-xl font-bold">{destination.country}</h3>
                  </div>
                </div>
                <div className="p-6">
                  <p className="text-gray-600 mb-4">{destination.description}</p>
                  <div className="space-y-2">
                    <h4 className="font-semibold text-gray-900">精选景点</h4>
                    <div className="flex flex-wrap gap-1">
                      {destination.highlights.map((highlight, highlightIndex) => (
                        <span 
                          key={highlightIndex}
                          className="px-2 py-1 bg-blue-100 text-blue-600 text-xs rounded-full"
                        >
                          {highlight}
                        </span>
                      ))}
                    </div>
                  </div>
                  <div className="mt-6">
                    <div className="flex items-center justify-between">
                      <span className="text-orange-500 font-semibold">即将上线</span>
                      <ArrowRight size={20} className="text-gray-400" />
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Subscribe Section */}
      <section id="subscribe" className="py-20 bg-gradient-to-r from-purple-600 to-blue-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-white rounded-2xl shadow-2xl p-8 md:p-12">
            <Mail size={60} className="mx-auto mb-6 text-purple-600" />
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              第一时间获取更新
            </h2>
            <p className="text-xl text-gray-600 mb-8">
              想第一时间获取您心仪目的地的旅游线路信息吗？<br />
              留下您的邮箱，我们上线时立即通知您！
            </p>

            <form onSubmit={handleSubscribe} className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  选择您感兴趣的目的地
                </label>
                <select
                  value={selectedCountry}
                  onChange={(e) => setSelectedCountry(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-lg"
                  required
                >
                  <option value="">请选择目的地</option>
                  {destinations.map((destination, index) => (
                    <option key={index} value={destination.country}>
                      {destination.flag} {destination.country}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  您的邮箱地址
                </label>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="请输入您的邮箱地址"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-lg"
                  required
                />
              </div>

              <button
                type="submit"
                className="w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white py-4 rounded-lg font-semibold text-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-300 transform hover:scale-105"
              >
                立即订阅更新
              </button>
            </form>

            <p className="text-sm text-gray-500 mt-6">
              我们承诺不会向您发送垃圾邮件，您可以随时取消订阅
            </p>
          </div>
        </div>
      </section>

      {/* Coming Soon Features */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              即将推出的服务
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              我们正在为您准备更多精彩的旅游服务
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center p-6 bg-gradient-to-br from-blue-50 to-purple-50 rounded-xl">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <Globe size={32} className="text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">定制旅游</h3>
              <p className="text-gray-600">
                为您量身定制专属的国际旅游行程
              </p>
            </div>

            <div className="text-center p-6 bg-gradient-to-br from-emerald-50 to-blue-50 rounded-xl">
              <div className="w-16 h-16 bg-gradient-to-r from-emerald-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <MapPin size={32} className="text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">当地导游</h3>
              <p className="text-gray-600">
                专业的当地导游服务，深度体验当地文化
              </p>
            </div>

            <div className="text-center p-6 bg-gradient-to-br from-orange-50 to-red-50 rounded-xl">
              <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <Mail size={32} className="text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">签证协助</h3>
              <p className="text-gray-600">
                专业的签证申请协助服务
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default InternationalTours;