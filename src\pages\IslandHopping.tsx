import React, { useState } from 'react';
import { Waves, Calendar, Users, MapPin, Camera, Utensils } from 'lucide-react';

const IslandHopping = () => {
  const [selectedPackage, setSelectedPackage] = useState(null);

  const packages = [
    {
      id: 1,
      name: '热浪岛3天2夜浮潜套餐',
      price: '¥1,288',
      duration: '3天2夜',
      highlight: '世界级浮潜胜地',
      image: 'https://images.pexels.com/photos/1680247/pexels-photo-1680247.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
      gallery: [
        'https://images.pexels.com/photos/1680247/pexels-photo-1680247.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
        'https://images.pexels.com/photos/1320684/pexels-photo-1320684.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
        'https://images.pexels.com/photos/2901209/pexels-photo-2901209.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      itinerary: [
        {
          day: '第1天',
          title: '出发 - 热浪岛',
          activities: ['早上8:00从瓜拉登嘉楼码头出发', '抵达热浪岛，办理入住', '午餐时间', '下午海滩自由活动', '晚餐及休息']
        },
        {
          day: '第2天',
          title: '浮潜探索',
          activities: ['早餐后准备浮潜装备', '上午浮潜 - 海洋公园', '午餐时间', '下午浮潜 - 鲨鱼点', '晚餐及海滩BBQ']
        },
        {
          day: '第3天',
          title: '自由活动 - 返程',
          activities: ['早餐后自由活动', '海滩漫步或游泳', '午餐时间', '下午2:00返回码头', '结束愉快行程']
        }
      ],
      includes: ['往返快艇', '2晚度假村住宿', '6餐（2早4正）', '浮潜装备', '专业导游', '海洋公园门票'],
      excludes: ['个人消费', '小费', '保险', '往返登嘉楼交通'],
      hotel: {
        name: '热浪岛度假村',
        description: '位于海滩边的舒适度假村，拥有传统马来建筑风格和现代化设施',
        features: ['海景房', '空调', '私人浴室', '海滩前台']
      }
    },
    {
      id: 2,
      name: '刁曼岛4天3夜休闲度假游',
      price: '¥1,688',
      duration: '4天3夜',
      highlight: '东南亚十大美丽海岛',
      image: 'https://images.pexels.com/photos/1320684/pexels-photo-1320684.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
      gallery: [
        'https://images.pexels.com/photos/1320684/pexels-photo-1320684.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
        'https://images.pexels.com/photos/2901209/pexels-photo-2901209.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
        'https://images.pexels.com/photos/1680247/pexels-photo-1680247.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      itinerary: [
        {
          day: '第1天',
          title: '出发 - 刁曼岛',
          activities: ['早上9:00从丰盛港码头出发', '抵达刁曼岛，办理入住', '午餐时间', '下午海滩休闲', '晚餐及夜晚自由活动']
        },
        {
          day: '第2天',
          title: '环岛游览',
          activities: ['早餐后环岛一日游', '参观Asah瀑布', '午餐时间', '下午Monkey Beach', '晚餐海鲜大餐']
        },
        {
          day: '第3天',
          title: '水上活动',
          activities: ['早餐后水上活动', '浮潜或深潜', '午餐时间', '下午香蕉船、摩托艇', '晚餐及海滩派对']
        },
        {
          day: '第4天',
          title: '自由活动 - 返程',
          activities: ['早餐后自由活动', '最后的海滩时光', '午餐时间', '下午3:00返回码头', '结束愉快行程']
        }
      ],
      includes: ['往返快艇', '3晚海景酒店', '9餐（3早6正）', '环岛游览', '水上活动', '专业导游'],
      excludes: ['个人消费', '小费', '保险', '往返丰盛港交通'],
      hotel: {
        name: '刁曼岛海景度假酒店',
        description: '现代化的海滨度假酒店，设施完善，服务优质',
        features: ['海景房', '游泳池', '餐厅', '水上活动中心']
      }
    },
    {
      id: 3,
      name: '浪中岛2天1夜蜜月套餐',
      price: '¥888',
      duration: '2天1夜',
      highlight: '蜜月天堂',
      image: 'https://images.pexels.com/photos/2901209/pexels-photo-2901209.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
      gallery: [
        'https://images.pexels.com/photos/2901209/pexels-photo-2901209.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
        'https://images.pexels.com/photos/1680247/pexels-photo-1680247.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
        'https://images.pexels.com/photos/1320684/pexels-photo-1320684.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop'
      ],
      itinerary: [
        {
          day: '第1天',
          title: '浪漫启程',
          activities: ['早上10:00从瓜拉登嘉楼出发', '抵达浪中岛，蜜月欢迎仪式', '午餐时间', '下午双人SPA', '浪漫海滩晚餐']
        },
        {
          day: '第2天',
          title: '甜蜜时光',
          activities: ['早餐送房服务', '上午浮潜体验', '午餐时间', '下午自由活动', '傍晚返回码头']
        }
      ],
      includes: ['往返快艇', '1晚蜜月套房', '3餐（1早2正）', '双人SPA', '浮潜装备', '蜜月布置'],
      excludes: ['个人消费', '小费', '保险', '往返登嘉楼交通'],
      hotel: {
        name: '浪中岛蜜月度假村',
        description: '专为情侣设计的浪漫度假村，私密性强，环境优美',
        features: ['蜜月套房', '私人阳台', '海景浴缸', '24小时客房服务']
      }
    }
  ];

  const [bookingForm, setBookingForm] = useState({
    packageId: '',
    checkIn: '',
    checkOut: '',
    adults: '2',
    children: '0',
    name: '',
    phone: '',
    email: '',
    requirements: ''
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setBookingForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleBooking = (e) => {
    e.preventDefault();
    if (!selectedPackage) {
      alert('请先选择套餐');
      return;
    }
    alert(`预订成功！套餐：${selectedPackage.name}，我们会尽快与您联系确认详情。`);
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-cyan-600 to-blue-600 opacity-90"></div>
        <div 
          className="absolute inset-0 bg-cover bg-center"
          style={{
            backgroundImage: 'url(https://images.pexels.com/photos/1680247/pexels-photo-1680247.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop)'
          }}
        ></div>
        <div className="relative z-10 text-center text-white px-4">
          <h1 className="text-5xl md:text-7xl font-bold mb-6">
            海岛旅游
          </h1>
          <p className="text-xl md:text-2xl mb-8 opacity-90">
            探索马来西亚最美海岛，享受热带天堂的魅力
          </p>
          <button 
            onClick={() => document.getElementById('packages').scrollIntoView({ behavior: 'smooth' })}
            className="bg-white text-cyan-600 px-8 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors"
          >
            查看套餐
          </button>
        </div>
      </section>

      {/* Packages Section */}
      <section id="packages" className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              精选海岛套餐
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              从热浪岛到刁曼岛，每个套餐都精心设计，为您带来难忘的海岛体验
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {packages.map((pkg) => (
              <div 
                key={pkg.id} 
                className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 cursor-pointer"
                onClick={() => setSelectedPackage(pkg)}
              >
                <div className="relative">
                  <img 
                    src={pkg.image} 
                    alt={pkg.name}
                    className="w-full h-48 object-cover"
                  />
                  <div className="absolute top-4 right-4 bg-white rounded-full px-3 py-1">
                    <span className="text-sm font-semibold text-cyan-600">{pkg.duration}</span>
                  </div>
                  <div className="absolute bottom-4 left-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded">
                    <span className="text-sm">{pkg.highlight}</span>
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{pkg.name}</h3>
                  <div className="flex items-center justify-between mb-4">
                    <span className="text-2xl font-bold text-cyan-600">{pkg.price}</span>
                    <span className="text-sm text-gray-500">起/人</span>
                  </div>
                  <button className="w-full bg-cyan-600 text-white py-2 rounded-lg font-semibold hover:bg-cyan-700 transition-colors">
                    查看详情
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Package Details Modal */}
      {selectedPackage && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-start mb-6">
                <h2 className="text-2xl font-bold text-gray-900">{selectedPackage.name}</h2>
                <button 
                  onClick={() => setSelectedPackage(null)}
                  className="text-gray-500 hover:text-gray-700 text-2xl"
                >
                  ×
                </button>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Left Column - Package Details */}
                <div>
                  {/* Gallery */}
                  <div className="mb-6">
                    <div className="grid grid-cols-3 gap-2">
                      {selectedPackage.gallery.map((image, index) => (
                        <img 
                          key={index}
                          src={image} 
                          alt={`${selectedPackage.name} ${index + 1}`}
                          className="w-full h-24 object-cover rounded-lg"
                        />
                      ))}
                    </div>
                  </div>

                  {/* Itinerary */}
                  <div className="mb-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">行程安排</h3>
                    <div className="space-y-4">
                      {selectedPackage.itinerary.map((day, index) => (
                        <div key={index} className="border rounded-lg p-4">
                          <h4 className="font-semibold text-gray-900 mb-2">{day.day} - {day.title}</h4>
                          <ul className="space-y-1">
                            {day.activities.map((activity, actIndex) => (
                              <li key={actIndex} className="flex items-start space-x-2">
                                <div className="w-2 h-2 bg-cyan-500 rounded-full mt-2"></div>
                                <span className="text-sm text-gray-600">{activity}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Includes & Excludes */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-3 flex items-center">
                        <Utensils size={16} className="mr-2 text-emerald-600" />
                        费用包含
                      </h3>
                      <ul className="space-y-2">
                        {selectedPackage.includes.map((item, index) => (
                          <li key={index} className="flex items-center space-x-2">
                            <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                            <span className="text-sm text-gray-600">{item}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-3">费用不含</h3>
                      <ul className="space-y-2">
                        {selectedPackage.excludes.map((item, index) => (
                          <li key={index} className="flex items-center space-x-2">
                            <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                            <span className="text-sm text-gray-600">{item}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  {/* Hotel Info */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="font-semibold text-gray-900 mb-2">{selectedPackage.hotel.name}</h3>
                    <p className="text-sm text-gray-600 mb-3">{selectedPackage.hotel.description}</p>
                    <div className="flex flex-wrap gap-2">
                      {selectedPackage.hotel.features.map((feature, index) => (
                        <span 
                          key={index}
                          className="px-2 py-1 bg-cyan-100 text-cyan-600 text-xs rounded-full"
                        >
                          {feature}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Right Column - Booking Form */}
                <div>
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">预订此套餐</h3>
                    <form onSubmit={handleBooking} className="space-y-4">
                      <input 
                        type="hidden" 
                        name="packageId" 
                        value={selectedPackage.id}
                        onChange={handleInputChange}
                      />
                      
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            入住日期
                          </label>
                          <input
                            type="date"
                            name="checkIn"
                            value={bookingForm.checkIn}
                            onChange={handleInputChange}
                            min={new Date().toISOString().split('T')[0]}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                            required
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            离店日期
                          </label>
                          <input
                            type="date"
                            name="checkOut"
                            value={bookingForm.checkOut}
                            onChange={handleInputChange}
                            min={bookingForm.checkIn}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                            required
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            成人数量
                          </label>
                          <select
                            name="adults"
                            value={bookingForm.adults}
                            onChange={handleInputChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                          >
                            {[1, 2, 3, 4, 5, 6].map(num => (
                              <option key={num} value={num}>{num} 人</option>
                            ))}
                          </select>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            儿童数量
                          </label>
                          <select
                            name="children"
                            value={bookingForm.children}
                            onChange={handleInputChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                          >
                            {[0, 1, 2, 3, 4].map(num => (
                              <option key={num} value={num}>{num} 人</option>
                            ))}
                          </select>
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          联系人姓名
                        </label>
                        <input
                          type="text"
                          name="name"
                          value={bookingForm.name}
                          onChange={handleInputChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                          required
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          联系电话
                        </label>
                        <input
                          type="tel"
                          name="phone"
                          value={bookingForm.phone}
                          onChange={handleInputChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                          required
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          邮箱地址
                        </label>
                        <input
                          type="email"
                          name="email"
                          value={bookingForm.email}
                          onChange={handleInputChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                          required
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          特殊要求
                        </label>
                        <textarea
                          name="requirements"
                          value={bookingForm.requirements}
                          onChange={handleInputChange}
                          rows="3"
                          placeholder="请填写特殊饮食要求、房间偏好等..."
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                        ></textarea>
                      </div>

                      <div className="border-t pt-4">
                        <div className="flex justify-between items-center mb-4">
                          <span className="text-gray-700">总价格</span>
                          <span className="text-2xl font-bold text-cyan-600">{selectedPackage.price}</span>
                        </div>
                        <button
                          type="submit"
                          className="w-full bg-cyan-600 text-white py-3 rounded-lg font-semibold hover:bg-cyan-700 transition-colors"
                        >
                          确认预订
                        </button>
                      </div>
                    </form>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default IslandHopping;