import React, { useState } from 'react';
import { Calendar, Clock, MapPin, Users, Star } from 'lucide-react';

const SignatureTickets = () => {
  const [activeTab, setActiveTab] = useState('sky-mirror');
  const [selectedDate, setSelectedDate] = useState('');
  const [selectedTime, setSelectedTime] = useState('');

  const experiences = {
    'sky-mirror': {
      title: '天空之镜',
      description: '在这里，天空与海水完美融合，创造出令人叹为观止的镜面效果。穿上鲜艳的衣服，在这片天然的摄影棚中留下最美的瞬间。',
      image: 'https://images.pexels.com/photos/1680247/pexels-photo-1680247.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop',
      duration: '4-5小时',
      bestTime: '早上6:00-10:00',
      location: '瓜拉雪兰莪',
      includes: ['往返船票', '专业向导', '救生衣', '拍照服务'],
      excludes: ['餐食', '个人消费', '小费'],
      tips: ['建议穿着鲜艳色彩的衣服', '请准备防晒用品', '建议携带帽子和太阳镜'],
      timeSlots: ['06:00', '07:00', '08:00', '09:00']
    },
    'eagle-feeding': {
      title: '喂食老鹰',
      description: '观看壮观的老鹰觅食场面，感受大自然的原始魅力。专业向导将带您深入红树林，近距离观察这些雄伟的猛禽。',
      image: 'https://images.pexels.com/photos/792416/pexels-photo-792416.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop',
      duration: '2-3小时',
      bestTime: '下午3:00-6:00',
      location: '瓜拉雪兰莪红树林',
      includes: ['往返船票', '专业向导', '救生衣', '老鹰食物'],
      excludes: ['餐食', '个人消费', '小费'],
      tips: ['请保持安静，不要惊扰老鹰', '建议携带望远镜', '请勿使用闪光灯拍照'],
      timeSlots: ['15:00', '16:00']
    },
    'firefly': {
      title: '萤火虫观赏',
      description: '夜晚的红树林中，成千上万的萤火虫如繁星般闪烁，为您呈现大自然最浪漫的灯光秀。',
      image: 'https://images.pexels.com/photos/1366919/pexels-photo-1366919.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop',
      duration: '2-3小时',
      bestTime: '晚上7:30-9:30',
      location: '瓜拉雪兰莪红树林',
      includes: ['往返船票', '专业向导', '救生衣', '手电筒'],
      excludes: ['餐食', '个人消费', '小费'],
      tips: ['请勿使用闪光灯或强光', '建议穿着长袖衣物防蚊', '请保持安静'],
      timeSlots: ['19:30', '20:00']
    },
    'mangrove': {
      title: '红树林探索',
      description: '探索神秘的红树林生态系统，了解这片独特湿地的生物多样性，感受大自然的奇妙。',
      image: 'https://images.pexels.com/photos/1624438/pexels-photo-1624438.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop',
      duration: '3-4小时',
      bestTime: '上午9:00-下午5:00',
      location: '瓜拉雪兰莪红树林',
      includes: ['往返船票', '专业向导', '救生衣', '生态讲解'],
      excludes: ['餐食', '个人消费', '小费'],
      tips: ['建议穿着舒适的鞋子', '请准备防蚊用品', '建议携带饮用水'],
      timeSlots: ['09:00', '10:00', '14:00', '15:00']
    }
  };

  const tabs = [
    { id: 'sky-mirror', label: '天空之镜', icon: '✨' },
    { id: 'eagle-feeding', label: '喂食老鹰', icon: '🦅' },
    { id: 'firefly', label: '萤火虫观赏', icon: '🌟' },
    { id: 'mangrove', label: '红树林探索', icon: '🌿' }
  ];

  const currentExperience = experiences[activeTab];

  const handleBooking = () => {
    if (!selectedDate || !selectedTime) {
      alert('请选择日期和时间');
      return;
    }
    alert(`预订成功！体验：${currentExperience.title}，日期：${selectedDate}，时间：${selectedTime}`);
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section - 优化高度 */}
      <section className="relative h-[70vh] min-h-[500px] max-h-[600px] flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-emerald-600 opacity-90"></div>
        <div 
          className="absolute inset-0 bg-cover bg-center"
          style={{
            backgroundImage: `url(${currentExperience.image})`
          }}
        ></div>
        <div className="relative z-10 text-center text-white px-4">
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-3 md:mb-4">
            特色船票体验
          </h1>
          <p className="text-lg md:text-xl lg:text-2xl opacity-90">
            探索大自然的奇迹，创造难忘的回忆
          </p>
        </div>
      </section>

      {/* Experience Tabs - 紧凑布局 */}
      <section className="py-12 md:py-16">
        <div className="max-w-8xl mx-auto px-3 sm:px-4 lg:px-6">
          {/* Tab Navigation - 优化间距 */}
          <div className="flex flex-wrap justify-center mb-8 md:mb-10 gap-2 md:gap-3">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 px-4 py-2.5 md:px-6 md:py-3 rounded-full font-semibold transition-all ${
                  activeTab === tab.id
                    ? 'bg-blue-600 text-white shadow-lg'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <span className="text-lg md:text-xl">{tab.icon}</span>
                <span className="text-sm md:text-base">{tab.label}</span>
              </button>
            ))}
          </div>

          {/* Experience Content - 优化布局 */}
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-6 md:gap-8">
            {/* Left Column - Experience Details */}
            <div className="xl:col-span-2">
              <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-3 md:mb-4">
                {currentExperience.title}
              </h2>
              <p className="text-gray-600 mb-4 md:mb-6 leading-relaxed">
                {currentExperience.description}
              </p>

              {/* Key Information - 紧凑网格 */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-4 mb-4 md:mb-6">
                <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                  <Clock className="text-blue-600 flex-shrink-0" size={18} />
                  <div>
                    <div className="font-semibold text-gray-900 text-sm">行程时长</div>
                    <div className="text-gray-600 text-sm">{currentExperience.duration}</div>
                  </div>
                </div>
                <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                  <Star className="text-emerald-600 flex-shrink-0" size={18} />
                  <div>
                    <div className="font-semibold text-gray-900 text-sm">最佳时间</div>
                    <div className="text-gray-600 text-sm">{currentExperience.bestTime}</div>
                  </div>
                </div>
                <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg sm:col-span-2 lg:col-span-1">
                  <MapPin className="text-red-600 flex-shrink-0" size={18} />
                  <div>
                    <div className="font-semibold text-gray-900 text-sm">集合地点</div>
                    <div className="text-gray-600 text-sm">{currentExperience.location}</div>
                  </div>
                </div>
              </div>

              {/* Includes & Excludes - 并排布局 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6 mb-4 md:mb-6">
                <div className="bg-emerald-50 rounded-lg p-4">
                  <h3 className="font-semibold text-gray-900 mb-3 text-emerald-800">费用包含</h3>
                  <ul className="space-y-1.5">
                    {currentExperience.includes.map((item, index) => (
                      <li key={index} className="flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-emerald-500 rounded-full flex-shrink-0"></div>
                        <span className="text-gray-700 text-sm">{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                <div className="bg-red-50 rounded-lg p-4">
                  <h3 className="font-semibold text-gray-900 mb-3 text-red-800">费用不含</h3>
                  <ul className="space-y-1.5">
                    {currentExperience.excludes.map((item, index) => (
                      <li key={index} className="flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-red-500 rounded-full flex-shrink-0"></div>
                        <span className="text-gray-700 text-sm">{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              {/* Tips - 紧凑布局 */}
              <div className="bg-yellow-50 rounded-lg p-4 mb-4 md:mb-6">
                <h3 className="font-semibold text-gray-900 mb-3 text-yellow-800">温馨提示</h3>
                <ul className="space-y-1.5">
                  {currentExperience.tips.map((tip, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <div className="w-1.5 h-1.5 bg-yellow-500 rounded-full mt-2 flex-shrink-0"></div>
                      <span className="text-gray-700 text-sm leading-relaxed">{tip}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Photo Gallery - 优化间距 */}
              <div className="grid grid-cols-3 gap-2 md:gap-3">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="aspect-square rounded-lg overflow-hidden">
                    <img 
                      src={currentExperience.image} 
                      alt={`${currentExperience.title} ${i}`}
                      className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                ))}
              </div>
            </div>

            {/* Right Column - Booking - 优化粘性定位 */}
            <div className="xl:col-span-1">
              <div className="bg-white rounded-xl shadow-lg border p-5 md:p-6 sticky top-4">
                <h3 className="text-xl md:text-2xl font-bold text-gray-900 mb-4 md:mb-6">预订体验</h3>
                
                {/* Date Selection */}
                <div className="mb-4 md:mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    选择日期
                  </label>
                  <input
                    type="date"
                    value={selectedDate}
                    onChange={(e) => setSelectedDate(e.target.value)}
                    min={new Date().toISOString().split('T')[0]}
                    className="w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                {/* Time Selection */}
                <div className="mb-4 md:mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    选择时间
                  </label>
                  <div className="grid grid-cols-2 gap-2">
                    {currentExperience.timeSlots.map((time) => (
                      <button
                        key={time}
                        onClick={() => setSelectedTime(time)}
                        className={`px-3 py-2 rounded-lg font-medium transition-colors text-sm ${
                          selectedTime === time
                            ? 'bg-blue-600 text-white'
                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                        }`}
                      >
                        {time}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Passenger Count */}
                <div className="mb-4 md:mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    参与人数
                  </label>
                  <div className="flex items-center space-x-3">
                    <Users className="text-gray-400" size={18} />
                    <select className="flex-1 px-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                      <option value="1">1 人</option>
                      <option value="2">2 人</option>
                      <option value="3">3 人</option>
                      <option value="4">4 人</option>
                      <option value="5">5 人</option>
                      <option value="6">6+ 人</option>
                    </select>
                  </div>
                </div>

                {/* Price Display */}
                <div className="mb-4 md:mb-6 p-4 bg-blue-50 rounded-lg">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-700 font-medium">成人票价</span>
                    <span className="text-2xl font-bold text-blue-600">¥168</span>
                  </div>
                  <div className="flex justify-between items-center mt-2">
                    <span className="text-gray-700">儿童票价</span>
                    <span className="text-lg font-semibold text-gray-600">¥98</span>
                  </div>
                </div>

                {/* Book Button */}
                <button
                  onClick={handleBooking}
                  className="w-full bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors shadow-md"
                >
                  立即预订
                </button>

                <p className="text-xs text-gray-500 mt-3 text-center leading-relaxed">
                  预订后我们会在24小时内确认您的订单
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default SignatureTickets;