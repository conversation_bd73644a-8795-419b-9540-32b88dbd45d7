import React from 'react';
import { Coffee, Clock, MapPin, Phone, Download, Anchor, Ship, Waves } from 'lucide-react';

const SkyPierCafe = () => {
  const menuItems = [
    {
      category: '船长特调咖啡',
      items: [
        { name: '天空之镜拿铁', price: '¥28', description: '融入当地特色的创意拿铁，奶泡艺术呈现天空之镜美景' },
        { name: '红树林摩卡', price: '¥32', description: '浓郁巧克力与咖啡的完美融合，如红树林般醇厚' },
        { name: '海风卡布奇诺', price: '¥26', description: '清新的海风味道，带来海边的惬意时光' },
        { name: '船长特浓', price: '¥30', description: '老船长的秘制配方，浓郁香醇，回味无穷' }
      ]
    },
    {
      category: '航海者茶饮',
      items: [
        { name: '马来拉茶', price: '¥18', description: '正宗马来西亚拉茶，香浓顺滑' },
        { name: '热带水果茶', price: '¥22', description: '新鲜热带水果调制，清香怡人' },
        { name: '椰香奶茶', price: '¥20', description: '椰浆与茶叶的完美结合' },
        { name: '船舱柠檬茶', price: '¥16', description: '清新柠檬配红茶，消暑解腻' }
      ]
    },
    {
      category: '甲板轻食',
      items: [
        { name: '船长海鲜三明治', price: '¥38', description: '新鲜海鲜配蔬菜，营养丰富' },
        { name: '椰浆吐司', price: '¥25', description: '香甜椰浆涂抹，口感丰富' },
        { name: '热带水果沙拉', price: '¥32', description: '季节性热带水果，清爽健康' },
        { name: '船员能量早餐', price: '¥45', description: '煎蛋、培根、吐司、水果的完美组合' }
      ]
    }
  ];

  const features = [
    {
      icon: Ship,
      title: '独特船舶改造',
      description: '由退役渔船精心改造，保留原有船舶结构，营造独特海洋氛围'
    },
    {
      icon: Coffee,
      title: '精品咖啡',
      description: '选用优质咖啡豆，专业烘焙，每一杯都是匠心之作'
    },
    {
      icon: Waves,
      title: '海景体验',
      description: '坐在甲板上品咖啡，听海浪声，看船只来往，享受独特的海上咖啡厅体验'
    },
    {
      icon: Anchor,
      title: '旅行者港湾',
      description: '位于天空之镜码头，是出发前和归来后的完美休憩站'
    }
  ];

  const shipHistory = [
    {
      year: '1985-2018',
      title: '渔船生涯',
      description: '这艘船在瓜拉雪兰莪海域服役33年，见证了当地渔业的兴衰变迁'
    },
    {
      year: '2019',
      title: '退役与收购',
      description: '船只退役后，我们看中了它的历史价值和改造潜力，决定给它新的生命'
    },
    {
      year: '2020',
      title: '精心改造',
      description: '保留船舶原有结构，加入现代咖啡厅设施，打造独特的海上咖啡体验'
    },
    {
      year: '2021至今',
      title: '天空号船咖啡厅',
      description: '正式营业，成为旅行者心中独特的海上咖啡厅，承载着新的故事'
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-amber-600 to-orange-600 opacity-90"></div>
        <div 
          className="absolute inset-0 bg-cover bg-center"
          style={{
            backgroundImage: 'url(https://images.pexels.com/photos/1647976/pexels-photo-1647976.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop)'
          }}
        ></div>
        <div className="relative z-10 text-center text-white px-4 max-w-4xl">
          <div className="mb-8">
            <Ship size={80} className="mx-auto mb-6 text-white" />
          </div>
          <h1 className="text-5xl md:text-7xl font-bold mb-6">
            天空号船咖啡厅
          </h1>
          <h2 className="text-2xl md:text-3xl font-semibold mb-6 opacity-90">
            Sky Mirror World Boat Cafe
          </h2>
          <p className="text-xl md:text-2xl mb-8 opacity-90">
            一艘老船的新生，旅行者的心灵港湾
          </p>
          <button 
            onClick={() => document.getElementById('story').scrollIntoView({ behavior: 'smooth' })}
            className="bg-white text-amber-600 px-8 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors"
          >
            了解我们的故事
          </button>
        </div>
      </section>

      {/* Ship History Timeline */}
      <section id="story" className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              一艘老船的传奇故事
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              从渔船到咖啡厅，这是一个关于传承、创新和重生的故事
            </p>
          </div>

          <div className="relative">
            <div className="absolute left-1/2 transform -translate-x-1/2 w-0.5 h-full bg-amber-200"></div>
            <div className="space-y-12">
              {shipHistory.map((period, index) => (
                <div key={index} className={`flex items-center ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`}>
                  <div className={`w-1/2 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>
                    <div className="bg-white rounded-lg shadow-lg p-6">
                      <div className="flex items-center space-x-2 mb-2">
                        <Anchor size={16} className="text-amber-600" />
                        <span className="text-sm font-semibold text-amber-600">{period.year}</span>
                      </div>
                      <h3 className="text-lg font-bold text-gray-900 mb-2">{period.title}</h3>
                      <p className="text-gray-600">{period.description}</p>
                    </div>
                  </div>
                  <div className="relative z-10 w-4 h-4 bg-amber-600 rounded-full border-4 border-white shadow-lg"></div>
                  <div className="w-1/2"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              独特的海上咖啡体验
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              在这里，每一杯咖啡都承载着故事，每一刻时光都值得珍藏
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <div key={index} className="text-center group">
                  <div className="w-16 h-16 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                    <Icon size={28} className="text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600">
                    {feature.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Menu Section */}
      <section className="py-20 bg-amber-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              船长的美味航海日志
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-8">
              精选饮品和轻食，为您的旅途增添美好回忆
            </p>
            <button className="bg-amber-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-amber-700 transition-colors inline-flex items-center space-x-2">
              <Download size={20} />
              <span>下载完整菜单</span>
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {menuItems.map((category, index) => (
              <div key={index} className="bg-white rounded-xl shadow-lg p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-4 text-center border-b border-amber-200 pb-2">
                  {category.category}
                </h3>
                <div className="space-y-4">
                  {category.items.map((item, itemIndex) => (
                    <div key={itemIndex} className="border-b border-gray-200 pb-4 last:border-b-0">
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="font-semibold text-gray-900">{item.name}</h4>
                        <span className="text-amber-600 font-bold">{item.price}</span>
                      </div>
                      <p className="text-sm text-gray-600">{item.description}</p>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* 360° Environment */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              船舱内外的独特风景
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              感受咖啡厅的独特魅力，每一个角落都有它的故事
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-4">
              <img 
                src="https://images.pexels.com/photos/1647976/pexels-photo-1647976.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop"
                alt="船舱内部"
                className="w-full h-48 object-cover rounded-lg"
              />
              <h3 className="text-lg font-semibold text-gray-900">温馨船舱</h3>
              <p className="text-gray-600 text-sm">保留船舱原有结构，营造温暖海洋氛围</p>
            </div>
            <div className="space-y-4">
              <img 
                src="https://images.pexels.com/photos/1647976/pexels-photo-1647976.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop"
                alt="海景甲板"
                className="w-full h-48 object-cover rounded-lg"
              />
              <h3 className="text-lg font-semibold text-gray-900">海景甲板</h3>
              <p className="text-gray-600 text-sm">在甲板上享受海风和美景，独特的海上咖啡体验</p>
            </div>
            <div className="space-y-4">
              <img 
                src="https://images.pexels.com/photos/1647976/pexels-photo-1647976.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop"
                alt="码头风景"
                className="w-full h-48 object-cover rounded-lg"
              />
              <h3 className="text-lg font-semibold text-gray-900">码头风景</h3>
              <p className="text-gray-600 text-sm">绝佳的地理位置，观赏来往船只和海上风光</p>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Info */}
      <section className="py-20 bg-gradient-to-r from-amber-600 to-orange-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-8">
              登船品咖啡
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="flex flex-col items-center">
                <Clock size={32} className="mb-4" />
                <h3 className="text-xl font-semibold mb-2">营业时间</h3>
                <p className="text-amber-100">
                  每天 7:00 - 19:00<br />
                  全年无休
                </p>
              </div>
              <div className="flex flex-col items-center">
                <MapPin size={32} className="mb-4" />
                <h3 className="text-xl font-semibold mb-2">停泊位置</h3>
                <p className="text-amber-100">
                  瓜拉雪兰莪天空之镜码头<br />
                  Sky Mirror Pier
                </p>
              </div>
              <div className="flex flex-col items-center">
                <Phone size={32} className="mb-4" />
                <h3 className="text-xl font-semibold mb-2">联系方式</h3>
                <p className="text-amber-100">
                  电话: +60 123 456 789<br />
                  微信: SkyBoatCafe
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Map Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              找到我们的船
            </h2>
            <p className="text-xl text-gray-600">
              位于瓜拉雪兰莪天空之镜码头，交通便利，停车方便
            </p>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="aspect-video bg-gray-200 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <Ship size={48} className="text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">
                  Google Maps 导航<br />
                  搜索："天空号船咖啡厅"或"Sky Mirror World Boat Cafe"
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default SkyPierCafe;