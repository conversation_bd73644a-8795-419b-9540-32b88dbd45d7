/**
 * Sky Mirror World Tour - Service Worker
 * 实现缓存策略和离线功能
 */

const CACHE_NAME = 'sky-mirror-v1.0.0';
const STATIC_CACHE = 'sky-mirror-static-v1.0.0';
const DYNAMIC_CACHE = 'sky-mirror-dynamic-v1.0.0';
const IMAGE_CACHE = 'sky-mirror-images-v1.0.0';

// 需要预缓存的静态资源
const STATIC_ASSETS = [
    '/',
    '/index.html',
    '/assets/css/main.css',
    '/assets/css/components.css',
    '/assets/css/responsive.css',
    '/assets/js/main.js',
    '/assets/js/navigation.js',
    '/pages/signature-tickets.html',
    '/pages/contact.html',
    '/pages/about.html',
    '/pages/domestic-transport.html',
    '/pages/sky-pier-cafe.html'
];

// 需要网络优先的资源
const NETWORK_FIRST = [
    '/pages/contact.html',
    '/assets/js/contact-form.js'
];

// 需要缓存优先的资源
const CACHE_FIRST = [
    '/assets/css/',
    '/assets/js/',
    '/assets/fonts/'
];

// 图片资源模式
const IMAGE_PATTERNS = [
    /\.(?:png|jpg|jpeg|svg|gif|webp)$/,
    /images\.pexels\.com/
];

/**
 * Service Worker 安装事件
 */
self.addEventListener('install', event => {
    console.log('Service Worker 安装中...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then(cache => {
                console.log('预缓存静态资源');
                return cache.addAll(STATIC_ASSETS);
            })
            .then(() => {
                console.log('Service Worker 安装完成');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('Service Worker 安装失败:', error);
            })
    );
});

/**
 * Service Worker 激活事件
 */
self.addEventListener('activate', event => {
    console.log('Service Worker 激活中...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        // 删除旧版本缓存
                        if (cacheName !== STATIC_CACHE && 
                            cacheName !== DYNAMIC_CACHE && 
                            cacheName !== IMAGE_CACHE) {
                            console.log('删除旧缓存:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker 激活完成');
                return self.clients.claim();
            })
    );
});

/**
 * 拦截网络请求
 */
self.addEventListener('fetch', event => {
    const request = event.request;
    const url = new URL(request.url);
    
    // 只处理 GET 请求
    if (request.method !== 'GET') {
        return;
    }
    
    // 根据资源类型选择缓存策略
    if (isImageRequest(request)) {
        event.respondWith(handleImageRequest(request));
    } else if (isStaticAsset(request)) {
        event.respondWith(handleStaticAsset(request));
    } else if (isNetworkFirst(request)) {
        event.respondWith(handleNetworkFirst(request));
    } else if (isCacheFirst(request)) {
        event.respondWith(handleCacheFirst(request));
    } else {
        event.respondWith(handleDefault(request));
    }
});

/**
 * 判断是否为图片请求
 */
function isImageRequest(request) {
    return IMAGE_PATTERNS.some(pattern => pattern.test(request.url));
}

/**
 * 判断是否为静态资源
 */
function isStaticAsset(request) {
    return STATIC_ASSETS.includes(new URL(request.url).pathname);
}

/**
 * 判断是否需要网络优先
 */
function isNetworkFirst(request) {
    return NETWORK_FIRST.some(pattern => request.url.includes(pattern));
}

/**
 * 判断是否需要缓存优先
 */
function isCacheFirst(request) {
    return CACHE_FIRST.some(pattern => request.url.includes(pattern));
}

/**
 * 处理图片请求 - 缓存优先，网络回退
 */
function handleImageRequest(request) {
    return caches.open(IMAGE_CACHE)
        .then(cache => {
            return cache.match(request)
                .then(response => {
                    if (response) {
                        return response;
                    }
                    
                    return fetch(request)
                        .then(networkResponse => {
                            // 只缓存成功的响应
                            if (networkResponse.status === 200) {
                                cache.put(request, networkResponse.clone());
                            }
                            return networkResponse;
                        })
                        .catch(() => {
                            // 返回占位图片
                            return new Response(
                                '<svg width="300" height="200" xmlns="http://www.w3.org/2000/svg"><rect width="300" height="200" fill="#f0f0f0"/><text x="150" y="100" text-anchor="middle" fill="#999">图片加载失败</text></svg>',
                                { headers: { 'Content-Type': 'image/svg+xml' } }
                            );
                        });
                });
        });
}

/**
 * 处理静态资源 - 缓存优先
 */
function handleStaticAsset(request) {
    return caches.open(STATIC_CACHE)
        .then(cache => {
            return cache.match(request)
                .then(response => {
                    return response || fetch(request)
                        .then(networkResponse => {
                            cache.put(request, networkResponse.clone());
                            return networkResponse;
                        });
                });
        });
}

/**
 * 处理网络优先请求
 */
function handleNetworkFirst(request) {
    return fetch(request)
        .then(response => {
            // 缓存成功的响应
            if (response.status === 200) {
                const responseClone = response.clone();
                caches.open(DYNAMIC_CACHE)
                    .then(cache => {
                        cache.put(request, responseClone);
                    });
            }
            return response;
        })
        .catch(() => {
            // 网络失败时从缓存获取
            return caches.open(DYNAMIC_CACHE)
                .then(cache => {
                    return cache.match(request);
                });
        });
}

/**
 * 处理缓存优先请求
 */
function handleCacheFirst(request) {
    return caches.open(DYNAMIC_CACHE)
        .then(cache => {
            return cache.match(request)
                .then(response => {
                    if (response) {
                        // 后台更新缓存
                        fetch(request)
                            .then(networkResponse => {
                                if (networkResponse.status === 200) {
                                    cache.put(request, networkResponse.clone());
                                }
                            })
                            .catch(() => {
                                // 网络错误时忽略
                            });
                        return response;
                    }
                    
                    return fetch(request)
                        .then(networkResponse => {
                            if (networkResponse.status === 200) {
                                cache.put(request, networkResponse.clone());
                            }
                            return networkResponse;
                        });
                });
        });
}

/**
 * 默认处理策略 - 网络优先，缓存回退
 */
function handleDefault(request) {
    return fetch(request)
        .then(response => {
            // 缓存成功的 HTML 页面
            if (response.status === 200 && 
                request.headers.get('accept').includes('text/html')) {
                const responseClone = response.clone();
                caches.open(DYNAMIC_CACHE)
                    .then(cache => {
                        cache.put(request, responseClone);
                    });
            }
            return response;
        })
        .catch(() => {
            // 网络失败时从缓存获取
            return caches.match(request)
                .then(response => {
                    if (response) {
                        return response;
                    }
                    
                    // 如果是 HTML 请求且没有缓存，返回离线页面
                    if (request.headers.get('accept').includes('text/html')) {
                        return caches.match('/');
                    }
                    
                    // 其他请求返回网络错误
                    return new Response('网络连接失败', {
                        status: 408,
                        headers: { 'Content-Type': 'text/plain; charset=utf-8' }
                    });
                });
        });
}

/**
 * 处理消息事件
 */
self.addEventListener('message', event => {
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
    
    if (event.data && event.data.type === 'GET_VERSION') {
        event.ports[0].postMessage({ version: CACHE_NAME });
    }
    
    if (event.data && event.data.type === 'CLEAR_CACHE') {
        caches.keys().then(cacheNames => {
            return Promise.all(
                cacheNames.map(cacheName => caches.delete(cacheName))
            );
        }).then(() => {
            event.ports[0].postMessage({ success: true });
        });
    }
});

/**
 * 后台同步事件
 */
self.addEventListener('sync', event => {
    if (event.tag === 'background-sync') {
        event.waitUntil(doBackgroundSync());
    }
});

/**
 * 执行后台同步
 */
function doBackgroundSync() {
    // 这里可以处理离线时的表单提交等
    console.log('执行后台同步');
    return Promise.resolve();
}

/**
 * 推送通知事件
 */
self.addEventListener('push', event => {
    if (event.data) {
        const data = event.data.json();
        const options = {
            body: data.body,
            icon: '/assets/images/icon-192x192.png',
            badge: '/assets/images/badge-72x72.png',
            vibrate: [100, 50, 100],
            data: {
                dateOfArrival: Date.now(),
                primaryKey: data.primaryKey
            }
        };
        
        event.waitUntil(
            self.registration.showNotification(data.title, options)
        );
    }
});

/**
 * 通知点击事件
 */
self.addEventListener('notificationclick', event => {
    event.notification.close();
    
    event.waitUntil(
        clients.openWindow('/')
    );
});
